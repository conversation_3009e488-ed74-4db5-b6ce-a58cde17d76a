import { welcomeScreenUI } from "../../configs";
import { findBrandByDomain } from "../../configs/configLoader";
import { useIsMobile } from "../../utils";

import "./index.css";

interface TextFieldComponentProps {
  option1: string;
  option2: string;
  unit: string;
  handleUnitChange: (newUnit: string) => void;
  placeholder1?: string;
  placeholder2?: string;
}

const UnitOptions: React.FC<TextFieldComponentProps> = ({
  option1,
  option2,
  unit,
  handleUnitChange,
  placeholder1,
  placeholder2,
}) => {
  const isMobile = useIsMobile();
  const brandDefined = findBrandByDomain();

  const isSRP = brandDefined?.name === "SRP";
  const isVB = brandDefined?.name === "Victoria Beckham";
  const isVelour = brandDefined?.name === "Velour Garments";

  const unitsStyles = {
    fontWeight: welcomeScreenUI.units.fontWeight,
    fontSize: welcomeScreenUI.units.fontSize,
    top: welcomeScreenUI.units.top,
    right:
      (isMobile && isVB) || (isMobile && isVelour)
        ? "0"
        : welcomeScreenUI.units.right,
    textTransform: (welcomeScreenUI.units as any)?.textTransform,
    fontFamily: "Times New Roman Custom, serif",
    fontStyle: "normal",
    border: !isVB && !isVelour ? "none" : "1px solid #000000",
    padding: isVelour ? "5px" : !isVB ? "none" : "10px",
  };

  const getVictoriaBeckhamStyles = (unit: string, option: string) => {
    return {
      color:
        unit === option
          ? welcomeScreenUI.units.inactiveColor
          : welcomeScreenUI.units.activeColor,
      backgroundColor:
        unit === option
          ? welcomeScreenUI.units.activeColor
          : welcomeScreenUI.units.inactiveColor,
    };
  };

  const victoriaStylesFirst =
    isVB || isVelour ? getVictoriaBeckhamStyles(unit, option1) : {};

  const victoriaStylesSecond =
    isVB || isVelour ? getVictoriaBeckhamStyles(unit, option2) : {};

  return (
    <div
      className="unit-options"
      style={{
        gap: !isVB && !isVelour ? "10px" : 0,
      }}
    >
      <span
        style={{
          ...unitsStyles,
          color:
            unit === option1
              ? welcomeScreenUI.units.activeColor
              : welcomeScreenUI.units.inactiveColor,
          position: "relative",
          cursor: "pointer",
          textDecoration:
            unit === option1
              ? (welcomeScreenUI?.units as { activeFontStyle?: string })
                  ?.activeFontStyle || ""
              : "",
          fontWeight: isSRP && unit !== option1 ? 400 : unitsStyles.fontWeight,
          ...victoriaStylesFirst,
        }}
        onClick={() => handleUnitChange(option1)}
      >
        {placeholder1 ? placeholder1 : option1}
      </span>
      <span
        style={{
          ...unitsStyles,
          color:
            unit === option2
              ? welcomeScreenUI.units.activeColor
              : welcomeScreenUI.units.inactiveColor,
          position: "relative",
          cursor: "pointer",
          textDecoration:
            unit === option2
              ? (welcomeScreenUI?.units as { activeFontStyle?: string })
                  ?.activeFontStyle || ""
              : "",
          fontWeight: isSRP && unit !== option2 ? 400 : unitsStyles.fontWeight,
          ...victoriaStylesSecond,
        }}
        onClick={() => handleUnitChange(option2)}
      >
        {placeholder2 ? placeholder2 : option2}
      </span>
    </div>
  );
};

export default UnitOptions;
