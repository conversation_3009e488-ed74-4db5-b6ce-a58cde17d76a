{"components/intro": {"title": "Encontre o seu tamanho para cada um dos nossos artigos", "description": "Algumas questões para te conhecer melhor", "steps": {"consent": "KENZO X KLEEP", "gender": "Encontre o tamanho ideal para todos os nossos artigos", "intro_mobile": "Encontre o tamanho ideal para todos os nossos artigos", "intro": "Encontre o seu tamanho para cada um dos nossos artigos", "device_select": "Encontre o seu tamanho para cada um dos nossos artigos", "device_select_shoe": "Encontre o seu número de sapato ideal", "shoe_gender": "Por favor, indique o seu gênero", "shoe_sizes": "Qual número de sapato você costuma usar?", "shoe_questions": "Qual é a largura dos seus pés?", "result_shoe": "Pointure recommandée", "qr_code": "Scanner corporal rápido e preciso", "qr_code_shoe": "Scan rapide et précis", "lingerie_question_1": "Especifique o fecho que você mais usa", "lingerie_question_2": "Indique como se sente em relação à faixa sob o busto", "lingerie_question_3": "Indique como se sente em relação às taças", "lingerie_question_4": "Indique como se sente em relação às alças", "belly": "Escolha a forma da sua cintura", "torso": {"male": "Escolha a forma do seu tronco", "female": "Escolha a forma do seu quadril"}, "cuisses": "Escolha a forma do seu quadril", "breasts": "Escolha o tamanho do seu peito", "sleeves": "Escolha o comprimento das mangas", "error": "Infelizmente detectamos um erro", "error_outOfRange": "Infelizmente detectamos um erro.", "result": "Tamanho recomendado&nbsp;:", "unavailable": "O seu tamanho está indisponível&nbsp;:", "antibracketing": "ESTÁ EM DÚVIDA SOBRE O TAMANHO?"}, "gender": {"title": "Você é", "description": "<PERSON>so nos ajuda a entender melhor o seu tamanho de calçado", "male": "<PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "children": {"male": "Garçon", "female": "<PERSON><PERSON>"}}, "height": {"title": "<PERSON><PERSON><PERSON>", "mobiletitle": "<PERSON><PERSON><PERSON>", "variation": "<PERSON><PERSON><PERSON>*", "unit": "cm", "errorcm": "Por favor, indique a sua altura.", "errorfeet": "Por favor, indique a sua altura.", "placeholder": "A sua altura (ex.: 160 cm)", "placeholderfeet": "ex : 5’", "placeholderinches": "ex : 9\"", "children": {"placeholder": "A sua altura (ex.: 105 cm)", "placeholderfeet": "ex : 3’", "placeholderinches": "ex : 4\""}}, "weight": {"title": "Peso", "variation": "Peso", "unit": "kg", "errorkg": "Por favor, indique o seu peso.", "errorlbs": "Por favor, indique o seu peso.", "placeholderKG": "O seu peso (ex. 70 kg)", "placeholderLBS": "O seu peso (ex.: 154 lbs)", "children": {"placeholderKG": "O seu peso (ex. 16 kg)", "placeholderLBS": "O seu peso (ex.: 35 lbs)"}}, "age": {"title": "<PERSON><PERSON>", "variation": "<PERSON><PERSON>", "unit": "anos", "error": "Por favor, indique a sua idade.", "placeholder": "A sua idade (ex.: 20 anos)", "placeholderKG": "A sua idade (ex.: 20 anos)", "placeholdermonths": "ex: 12", "children": {"placeholder": "A sua idade (ex.: 4 anos)", "year": "anos", "month": "meses"}}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "pp_part1": "<PERSON>o continuar, aceita a nossa política de privacidade.", "pp_link": "<PERSON><PERSON> continuar, aceita a nossa <a href=\"https://kleep.ai/privacy-policy\">Política de privacidade</a>.", "measured_by": "Medição por"}, "components/belly": {"title": "Escolha a forma da sua cintura", "description": "Abdomen", "size": {"one": "Plana", "two": "Mediana", "three": "Redonda"}, "scan": "Para uma melhor precisão, experimente o scanner corporal"}, "components/torso": {"description": "<PERSON><PERSON>", "size": {"male": {"one": "Esguio", "two": "Mediano", "three": "Largo"}, "female": {"one": "Reto", "two": "Mediano", "three": "<PERSON><PERSON><PERSON><PERSON>"}, "size": {"one": "Reto", "two": "Mediano", "three": "<PERSON><PERSON><PERSON><PERSON>"}, "one": "<PERSON><PERSON><PERSON><PERSON>", "two": "Medianas", "three": "<PERSON><PERSON><PERSON>"}}, "components/cuisses": {"description": "Thigh", "size": {"one": "<PERSON><PERSON><PERSON><PERSON>", "two": "Medianas", "three": "<PERSON><PERSON><PERSON>"}, "continue": "Encontrar meu tamanho"}}