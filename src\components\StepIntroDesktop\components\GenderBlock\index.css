.gender-buttons {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.gender-buttons button {
  padding: 10px 20px;
  border: 1px solid #f0f0f0;
  background-color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
  /* width: 129px; */
  width: auto;
  height: 48px;
}

.gender-buttons button.selected {
  color: #fff;
}

.gender-buttons:focus {
  outline: none;
}

button.with-arrow::before {
  content: "→";
  margin-left: 0px;
  margin-right: 5px;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

button.with-arrow::after {
  content: "→";
  margin-left: 8px;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

@media (max-width: 767px) {
  .gender-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
  }

  .gender-buttons button {
    padding: 10px 20px;
    border: 1px solid #f0f0f0;
    background-color: #fff;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
    width: 100%;
    height: 48px;
  }

  .gender-buttons button.selected {
    color: #fff;
  }
}
