import { Checkbox } from "@mui/material";
import { styled } from "@mui/system";

import "./index.css";

const CustomCheckbox = styled(Checkbox)({
  "&:before": {
    content: '""',
    display: "block",
    width: "14px",
    height: "14px",
    border: "1px solid #000",
    backgroundColor: "#FFFFFF",
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
  },
  "&.Mui-checked": {
    "&:before": {
      backgroundColor: "#FFFFFF",
    },
    "&:after": {
      content: '""',
      display: "block",
      width: "4px",
      height: "4px",
      backgroundColor: "#000000",
      position: "absolute",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
    },
  },
});

export default CustomCheckbox;
