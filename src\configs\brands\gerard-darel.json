{"name": "<PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#1c1c1c", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1c1c1c", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#1c1c1c", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #1c1c1c"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#1c1c1c", "borderRadius": "0", "borderColor": "#1c1c1c", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#1c1c1c", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#2E2E2E", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "6px", "right": "-11px", "activeColor": "#1c1c1c", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#1c1c1c", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#1c1c1c"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#1c1c1c", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#44883F", "textAlign": "left"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "13px", "fontColor": "#1c1c1c", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#e9e9e9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "13px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#1c1c1c", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#1c1c1c", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1c1c1c", "textTransform": "capitalize", "textDecoration": "none", "borderRadius": "0", "borderColor": "#1c1c1c", "borderWidth": "1px"}, "unavailable_size_text": {"fontWeight": "600", "fontSize": "72px", "fontColor": "#1c1c1c", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Inter", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#1c1c1c", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#1c1c1c", "fontSize": "13px"}, "cta": {"unfocused": {"backgroundColor": "#DDDDDD", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#1c1c1c", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#1c1c1c", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#1c1c1c", "fontWeight": "regular", "textTransform": "uppercase"}}, "questions": {"scanCTA": {"borderWidth": "", "borderColor": "", "borderRadius": "0px", "fontSize": "14px", "fontWeight": "", "fontColor": "", "textTransform": "", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#a7a7a7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1c1c1c", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#1c1c1c", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1c1c1c", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#1c1c1c"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#a7a7a7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1c1c1c", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#a7a7a7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1c1c1c", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#1c1c1c", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#A7A7A7", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#1c1c1c", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#1c1c1c", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#1c1c1c", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}