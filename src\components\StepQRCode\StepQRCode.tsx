import { FC, useState, useEffect, useMemo } from "react";
import { getLocalAndParse } from "../../store/localStoreUtils";
import { WEB_APP_URL } from "../../constants/modal";

import QrCodeMobile from "../QrcodeMobile";
import QrCodeDesktop from "../QrcodeDesktop";
import MeasuredBy from "../molecules/MeasuredBy";

import { useTranslation } from "react-i18next";

import {
  recommend,
  scanCheckMeasurements,
  visionCheck,
  shoeChildrenRecommend,
} from "../../api/endpoints";
import { capitalizeFirstLetter, useIsMobile } from "../../utils";
import { findBrandByDomain } from "../../configs/configLoader";

import { useUserContext } from "../../store/userContext";
import { handleAnalytics } from "../../utils/tracking";
import { logger } from "../../utils/logging";

import "./index.css";

interface IPropsStepQRCode {
  step: any;
  gender?: string;
  skipToResult: () => void;
  setRecommendedSize: (value: any) => void;
  titleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
  productStockData: any;
  variantId: string | null;
  variantCol: string | null;
  isShoesProduct?: boolean;
  handleError: () => void;
  setIsScanError?: React.Dispatch<React.SetStateAction<boolean>>;
}

const StepQRCode: FC<IPropsStepQRCode> = ({
  step,
  gender,
  skipToResult,
  setRecommendedSize,
  titleStyles,
  subtitleStyles,
  productStockData,
  variantId,
  variantCol,
  isShoesProduct,
  setIsScanError,
  handleError,
}) => {
  const isMobile = useIsMobile();

  const { t } = useTranslation(
    isMobile ? "components/qrcode/mobile" : "components/qrcode/desktop"
  );

  const userContext = useUserContext() || undefined;

  const [WebAppURL, setWebAppURL] = useState<string>("");
  const [checkCount, setCheckCount] = useState(1);
  const user = getLocalAndParse("user");
  const [mid, setMid] = useState<string | null>(userContext?.mid ?? null);
  const [uid, setUid] = useState<string | null>(userContext?.uid ?? null);
  const [pid, setPid] = useState<string | null>(userContext?.pid ?? null);

  const urlParameters = new URLSearchParams(window.location.search);
  const lang = urlParameters.get("lang")?.split("-")[0].toLowerCase() || "";

  const brandDefined = findBrandByDomain();

  useEffect(() => {
    const localPid = userContext?.pid;
    const localUid = userContext?.uid;
    const localMid = userContext?.mid;

    if (localPid) setPid(localPid);
    if (localUid) setUid(localUid);
    if (localMid) setMid(localMid);
  }, [userContext]);

  const setStoredData = () => {
    if (isShoesProduct) {
      setWebAppURL(userContext?.shoeWebAppURL || "");
    } else {
      setWebAppURL(
        `${WEB_APP_URL}?uid=${uid}&mid=${mid}&product_id=${pid}&gender=${
          user?.gender ?? "male"
        }&age=${user?.age ?? 40}&height=${user?.height ?? 170}&weight=${
          user?.weight ?? 70
        }&lang=${lang}`
      );
    }
  };

  useEffect(() => {
    if (userContext?.shoeWebAppURL) {
      setWebAppURL(userContext?.shoeWebAppURL);
    }
  }, [userContext]);

  useEffect(() => {
    logger.log("checkCount", checkCount);
  }, [checkCount]);

  const handleCheckShoeRecommend = () => {
    if (uid) {
      visionCheck(uid)
        .then((value: any) => {
          logger.log("visionCheck", value);
          if (value?.data?.completed) {
            const date_retry: number | null = sessionStorage.getItem(
              "shoe_retry"
            )
              ? parseInt(sessionStorage.getItem("shoe_retry")!, 10)
              : null;
            const date_completion: number | null = new Date(
              value?.data?.completion_time
            ).getTime();
            if (!date_completion) {
              logger.warn("Something wrong with date_completion");
            }
            logger.log(date_completion, date_retry);
            if (date_retry && date_completion < date_retry) {
              setTimeout(() => {
                setCheckCount((prev) => prev + 1);
              }, 500);
            } else {
              setCheckCount(0);
              setIsScanError && setIsScanError(false);

              if (pid) {
                shoeChildrenRecommend(pid, false).then((result: any) => {
                  if (result?.success) {
                    setRecommendedSize(result?.data);
                    skipToResult();
                  } else {
                    handleError();
                    setCheckCount(0);
                    setIsScanError && setIsScanError(true);
                  }
                });
              }
            }
          } else {
            if (checkCount > 0) {
              setTimeout(() => {
                setCheckCount((prev) => prev + 1);
              }, 500);
            }
          }
        })
        .catch((error: string) => {
          logger.error(`Error: scan shoe failed - ${error}`);
          handleError();
          setCheckCount(0);
          setIsScanError && setIsScanError(true);
        });
    }
  };

  const handleCheckClothingRecommend = () => {
    scanCheckMeasurements()
      .then((value: any) => {
        if (value) {
          if (userContext) userContext.isScanChecked.current = true;
          setCheckCount(0);
          recommend(pid, productStockData, variantId, variantCol).then(
            (result: any) => {
              if (result?.success) {
                setRecommendedSize(result?.data);
                skipToResult();
              } else {
                handleError();
                setCheckCount(0);
                setIsScanError && setIsScanError(true);
              }
            }
          );
        } else {
          setTimeout(() => {
            setCheckCount((prev) => prev + 1);
          }, 500);
        }
      })
      .catch((error) => {
        logger.error(`Error: scan failed - ${error}`);
        handleError();
        setCheckCount(0);
        setIsScanError && setIsScanError(true);
      });
  };

  useEffect(() => {
    setStoredData();
    if (isShoesProduct) {
      handleCheckShoeRecommend();
    } else {
      handleCheckClothingRecommend();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkCount, uid, mid, pid, isShoesProduct]);

  const ShoeIntro: FC<{ url: string }> = ({ url }) => (
    <>
      <div
        className="main-container"
        style={{
          margin: 0,
        }}
      >
        <span
          style={{
            color: "#2E2E2E",
            fontSize: "18px",
            fontWeight: 700,
            marginTop: "25px",
            minHeight: "25px",
            textAlign: "left",
            textTransform: "uppercase",
          }}
        >
          {t("shoe.title")}
        </span>
        <span
          className="description"
          style={{
            color: "#2E2E2E",
            fontSize: "14px",
            fontWeight: 400,
            textAlign: "left",
            textTransform: "none",
            justifyContent: "left",
            fontFamily: isMobile
              ? "Figtree, sans-serif"
              : "Figtree, sans-serif !important",
            lineHeight: "17px",
            marginTop: isMobile ? "20px" : "30px",
            marginBottom: "40px",
            margin: 0,
          }}
        >
          {t("shoe.text")}
        </span>

        <div
          style={{
            marginTop: "35px",
            marginBottom: "36px",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <video
            muted
            style={{
              minHeight: "210px",
              height: "210px",
              minWidth: "210px",
            }}
            playsInline
            autoPlay
            loop
          >
            <source
              src={
                "https://kleep-prod-assets-public.s3.eu-west-1.amazonaws.com/videos/Animation_Footwear.mp4"
              }
              type="video/mp4"
            />
          </video>
        </div>

        <button
          type="button"
          className={`continue-button`}
          onClick={() => {
            if (url) {
              handleAnalytics("action", "scan_clicked", null);
              window.open(url, "_blank", "noopener,noreferrer");
            }
          }}
          style={{
            textTransform: "uppercase",
            marginBottom: "10px",
          }}
        >
          {t("shoe.continue")}
        </button>
      </div>
      <MeasuredBy step={step} />
    </>
  );

  const memoShoeIntro = useMemo(
    () => <ShoeIntro url={WebAppURL} />,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [WebAppURL]
  );

  return isShoesProduct && isMobile ? (
    memoShoeIntro
  ) : (
    <>
      <div
        className="main-qr-container"
        style={{ minHeight: isMobile ? "20vh" : "79vh" }}
      >
        <div className={isMobile ? "text-block-mobile" : "text-block"}>
          <span
            className="title-scan"
            style={{
              ...(isMobile ? titleStyles : subtitleStyles),
              marginTop:
                brandDefined?.name === "Zadig & Voltaire" ||
                brandDefined?.name === "Lacoste"
                  ? "10px"
                  : isMobile
                  ? "10px"
                  : 0,
              textTransform:
                (isMobile ? titleStyles : subtitleStyles).textTransform ===
                "capitalize"
                  ? "none"
                  : ((isMobile ? titleStyles : subtitleStyles)
                      .textTransform as React.CSSProperties["textTransform"]),
              fontSize:
                !isMobile && brandDefined?.name === "SRP"
                  ? "18px"
                  : (isMobile ? titleStyles : subtitleStyles).fontSize,
            }}
          >
            {isShoesProduct
              ? t("shoeTitle")
              : (isMobile ? titleStyles : subtitleStyles).textTransform ===
                "capitalize"
              ? capitalizeFirstLetter(t("title"))
              : t("title")}
          </span>
          <span
            className="description-scan"
            style={{
              ...subtitleStyles,
              textTransform:
                subtitleStyles.textTransform === "capitalize"
                  ? "none"
                  : (subtitleStyles.textTransform as React.CSSProperties["textTransform"]),
              fontSize:
                !isMobile && brandDefined?.name === "SRP"
                  ? "18px"
                  : subtitleStyles.fontSize,
              whiteSpace: !isMobile ? "nowrap" : "normal",
            }}
          >
            {isShoesProduct
              ? t("shoeDescription")
              : subtitleStyles.textTransform === "capitalize"
              ? capitalizeFirstLetter(t("description"))
              : t("description")}
          </span>
        </div>
        <div
          style={{
            marginBottom: isMobile && isShoesProduct ? "80px" : 0,
          }}
        >
          {isMobile ? (
            <QrCodeMobile
              gender={gender || user?.gender}
              loading={!WebAppURL}
              url={WebAppURL}
              isShoesProduct={isShoesProduct}
              subtitleStyles={subtitleStyles}
            />
          ) : (
            <QrCodeDesktop
              loading={!WebAppURL}
              url={WebAppURL}
              subtitleStyles={subtitleStyles}
            />
          )}
        </div>
      </div>
      <MeasuredBy step={step} />
    </>
  );
};

export default StepQRCode;
