{"name": "Hartford", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "14px", "fontColor": "#050505", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#050505", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "18px", "borderColor": "#050505", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #050505"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#050505", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#050505", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#050505", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#050505"}}, "3": {"qrcode": {"backgroundColor": "#050505"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#050505", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "14px", "fontColor": "#050505", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "12px", "fontColor": "#050505", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#d8d8d8", "borderWidth": "2px"}, "focused": {"backgroundColor": "#050505", "fontWeight": "600", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#050505", "textTransform": "uppercase", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "600", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Libre Franklin", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#050505", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#050505", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#808080", "fontWeight": "600", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#050505", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": ""}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "", "borderRadius": "0", "fontSize": "12px", "fontWeight": "600", "fontColor": "#050505", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#050505", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "12px", "fontColor": "#050505", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#050505", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "600", "fontSize": "12px", "fontColor": "#050505", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#050505"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#050505", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#050505", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#050505", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#050505", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#050505", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#050505", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#050505", "fontWeight": "400", "fontStyle": "underline", "fontSize": "12px"}}}}}