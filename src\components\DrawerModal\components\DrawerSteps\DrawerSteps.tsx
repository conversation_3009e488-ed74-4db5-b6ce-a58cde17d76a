import { FC } from "react";

import StepIntroDesktop from "../../../StepIntroDesktop";
import StepDeviceSelect from "../../../StepDeviceSelect";
import StepQRCode from "../../../StepQRCode";
import StepBelly from "../../../StepBelly";
import StepTorse from "../../../StepTorse";
import StepCuisses from "../../../StepCuisses";
import StepBreasts from "../../../StepBreasts";
import ResultError from "../../../ResultError";
import Result from "../../../Result";
import ConsentScreen from "../../../ConsentScreen";
import StepSleeves from "../../../StepSleeves";

import { GENDERS, MODAL_STEPS } from "../../../../constants/modal";
import { SizeDataType } from "../../../../types/result";

import { removeLocalStore } from "../../../../store/localStoreUtils";
import {
  font,
  subtitlesStyles,
  titleStyles,
  uxGender,
  uxRoute,
} from "../../../../configs";
import { useUserContext } from "../../../../store/userContext";
import { handleAnalytics } from "../../../../utils/tracking";
import { postClearMid } from "../../../../utils/post";
import { findBrandByDomain } from "../../../../configs/configLoader";
import { INCHtoCM, LBStoKG, logger, useIsMobile } from "../../../../utils";
import LingerieSteps from "./components/LingerieSteps";
import ShoeSteps from "./components/ShoeSteps";
import StepShoeGender from "../StepShoeGender/StepShoeGender";
import { visionInit } from "../../../../api/endpoints";

import "./index.css";

interface IPropsDrawerSteps {
  step: any;
  error: any;
  height: string;
  unit: string;
  feet: string;
  inches: string;
  unitWeight: string;
  weight?: string | undefined;
  age: string;
  selectedGender: string;
  selectedShoeGender: string | null;
  setSelectedShoeGender: React.Dispatch<React.SetStateAction<string | null>>;
  disableContinue: boolean;
  bellyValue: any;
  torsoValue: any;
  cuissesValue: any;
  sizeValue: any;
  cupValue: any;
  sleeveValue: { current?: number };
  ageChildrenYears: string;
  ageChildrenMonths: string;
  unitAge: string;
  handleUnitAgeChange: (newUnit: string) => void;
  sendQuestionAnswers: (isSkipResult?: boolean) => void;
  handleFieldChange: (e: any, type: string, blurSignal: string) => void;
  handleUnitChange: (newUnit: string) => void;
  handleUnitWeightChange: (newUnit: string) => void;
  handleGenderSelect: (newUnit: string) => void;
  setNeedValidate: React.Dispatch<React.SetStateAction<boolean>>;
  recommendedSize: any;
  setRecommendedSize: React.Dispatch<any>;
  reducedResult: any;
  selectedRoute: string;
  setSelectedRoute: React.Dispatch<React.SetStateAction<string>>;
  sizeCountry: string;
  setSizeCountry: React.Dispatch<React.SetStateAction<string>>;
  productStockData: any;
  variantId: string | null;
  variantCol: string | null;
  similarProducts: any;
  isSizeUnavailable: boolean;
  selectedSize: SizeDataType | null;
  setSelectedSize: React.Dispatch<React.SetStateAction<SizeDataType | null>>;
  setSimilarProducts: React.Dispatch<any>;
  setStep: (
    value: React.SetStateAction<{
      number: number;
    } | null>
  ) => void;
  setPreviousStep: React.Dispatch<
    React.SetStateAction<{
      number: number;
    } | null>
  >;
  restart: () => void;
  setIsScanError: React.Dispatch<React.SetStateAction<boolean>>;
  outOfRange: boolean;
  isChildrenProduct: boolean;
  shoeQuestion: string | null;
  setShoeQuestion: React.Dispatch<React.SetStateAction<string | null>>;
  policyChecked: boolean;
  setPolicyChecked: React.Dispatch<React.SetStateAction<boolean>>;
  lingerieQuestion1: string | null;
  setLingerieQuestion1: React.Dispatch<React.SetStateAction<string | null>>;
  lingerieQuestion2: string | null;
  setLingerieQuestion2: React.Dispatch<React.SetStateAction<string | null>>;
  lingerieQuestion3: string | null;
  setLingerieQuestion3: React.Dispatch<React.SetStateAction<string | null>>;
  lingerieQuestion4: string | null;
  setLingerieQuestion4: React.Dispatch<React.SetStateAction<string | null>>;
}

const DrawerSteps: FC<IPropsDrawerSteps> = ({
  step,
  error,
  height,
  unit,
  feet,
  inches,
  unitWeight,
  weight,
  age,
  selectedGender,
  selectedShoeGender,
  setSelectedShoeGender,
  disableContinue,
  bellyValue,
  torsoValue,
  cuissesValue,
  sizeValue,
  cupValue,
  sleeveValue,
  sendQuestionAnswers,
  handleFieldChange,
  handleUnitChange,
  handleUnitWeightChange,
  handleGenderSelect,
  setNeedValidate,
  recommendedSize,
  setRecommendedSize,
  reducedResult,
  selectedRoute,
  setSelectedRoute,
  sizeCountry,
  setSizeCountry,
  productStockData,
  variantId,
  variantCol,
  similarProducts,
  isSizeUnavailable,
  selectedSize,
  setSelectedSize,
  setStep,
  setPreviousStep,
  restart,
  setSimilarProducts,
  setIsScanError,
  outOfRange,
  isChildrenProduct,
  ageChildrenYears,
  ageChildrenMonths,
  unitAge,
  handleUnitAgeChange,
  shoeQuestion,
  setShoeQuestion,
  policyChecked,
  setPolicyChecked,
  lingerieQuestion1,
  setLingerieQuestion1,
  lingerieQuestion2,
  setLingerieQuestion2,
  lingerieQuestion3,
  setLingerieQuestion3,
  lingerieQuestion4,
  setLingerieQuestion4,
}) => {
  const isMobile = useIsMobile();
  const userContext = useUserContext();

  const urlParameters = new URLSearchParams(window.location.search);
  const domain = urlParameters.get("domain");
  const mock = urlParameters.get("mock") === "true";

  const isFootwear =
    urlParameters.get("category") === "footwear" ||
    urlParameters.get("footwear") === "true";
  const isLingerie =
    urlParameters.get("category") === "lingerie" ||
    urlParameters.get("lingerie") === "true";

  const brandDefined = findBrandByDomain();

  const titlesStyles = {
    color: titleStyles.color as React.CSSProperties["color"],
    fontSize: titleStyles.fontSize as React.CSSProperties["fontSize"],
    fontWeight: titleStyles.fontWeight,
    textAlign: titleStyles.textAlign as React.CSSProperties["textAlign"],
    textTransform:
      titleStyles.textTransform as React.CSSProperties["textTransform"],
    justifyContent:
      titleStyles.textAlign as React.CSSProperties["justifyContent"],
    fontFamily: isMobile
      ? `${font}, sans-serif`
      : `${font}, sans-serif !important`,
    letterSpacing: (titleStyles as any)?.letterSpacing,
  };

  const subtitleStyles = {
    color: subtitlesStyles.color as React.CSSProperties["color"],
    fontSize: subtitlesStyles.fontSize as React.CSSProperties["fontSize"],
    fontWeight: subtitlesStyles.fontWeight as React.CSSProperties["fontWeight"],
    textAlign: subtitlesStyles.textAlign as React.CSSProperties["textAlign"],
    textTransform:
      subtitlesStyles.textTransform as React.CSSProperties["textTransform"],
    justifyContent:
      subtitlesStyles.textAlign as React.CSSProperties["justifyContent"],
    fontFamily: isMobile
      ? `${font}, sans-serif`
      : `${font}, sans-serif !important`,
    letterSpacing: (subtitlesStyles as any)?.letterSpacing,
  };

  const defaultStep =
    isMobile && uxGender
      ? MODAL_STEPS.INTRO_MOBILE
      : isMobile
      ? MODAL_STEPS.GENDER
      : MODAL_STEPS.INTRO;

  const isFirstIntroScreen =
    step?.number === MODAL_STEPS.INTRO.number ||
    step?.number === MODAL_STEPS.GENDER.number ||
    step?.number === MODAL_STEPS.INTRO_MOBILE.number;

  const preselectedRoute =
    uxRoute === "question_pushScan" || uxRoute === "question_only";

  const handleRouteEvent = async (type: string) => {
    if (type === "question_pushScan") {
      if (!userContext) return;

      if (isFootwear && isMobile) {
        openScan();
      } else {
        const resultNewMeasure = await userContext.createNewMeasure("scan");
        if (resultNewMeasure) {
          setStep(MODAL_STEPS.QR_CODE);
        }
      }
    }

    if (type === "question_only") {
      if (isFootwear) {
        setStep(MODAL_STEPS.SHOE_GENDER);
      } else {
        setStep(MODAL_STEPS.BELLY);
      }
    }

    setSelectedRoute(type);
  };

  const handleRetry = async () => {
    if (isFootwear) {
      setStep(MODAL_STEPS.DEVICE_SELECT);
      setIsScanError(false);
      restart();
    } else {
      removeLocalStore("mid");
      removeLocalStore("uid");
      removeLocalStore("gender");

      if (userContext && domain) {
        const validation = await userContext.createNewUser(domain);
        if (validation) {
          if (uxRoute === "none") {
            restart();
          } else {
            if (selectedRoute === "question_pushScan") {
              const resultNewMeasure = await userContext.createNewMeasure(
                "scan"
              );
              if (resultNewMeasure) {
                setStep(MODAL_STEPS.QR_CODE);
              }

              handleAnalytics("action", "error_restart", {
                key: "route_selected",
                value: "scan",
              });
            } else if (selectedRoute === "question_only") {
              const resultNewMeasure = await userContext.createNewMeasure(
                "question"
              );
              if (resultNewMeasure) setStep(defaultStep);

              handleAnalytics("action", "error_restart", {
                key: "route_selected",
                value: "question",
              });
            } else {
              logger.log(`route not recongnized ${selectedRoute}`);
            }
          }
        }
      }
    }
  };

  const handleSaveUserInfo = async () => {
    const body = {
      gender: selectedGender,
      age,
      height: unit === "feet" ? INCHtoCM(`${feet} ${inches}`) : height,
      weight:
        unitWeight === "lbs" && weight ? LBStoKG(parseInt(weight)) : weight,
    };
    localStorage.setItem("user", JSON.stringify(body));
  };

  const clearMid = () => {
    postClearMid();
  };

  const openScan = () => {
    const scanUrl = userContext?.shoeWebAppURL;

    if (!!scanUrl) {
      handleAnalytics("action", "scan_clicked", null);

      window.open(scanUrl, "_blank", "noopener,noreferrer");

      userContext?.handleCheckShoeRecommend();
    }
  };

  return (
    <>
      {step?.number === MODAL_STEPS.CONSENT.number && (
        <ConsentScreen
          nextStep={() => {
            handleAnalytics("action", "consent_submit", null);
            setStep(
              isMobile
                ? uxGender
                  ? MODAL_STEPS.INTRO_MOBILE
                  : MODAL_STEPS.GENDER
                : MODAL_STEPS.INTRO
            );
          }}
          clearMid={clearMid}
        />
      )}
      {isFirstIntroScreen && (
        <StepIntroDesktop
          step={step}
          error={error}
          height={height}
          unit={unit}
          feet={feet}
          inches={inches}
          unitWeight={unitWeight}
          weight={weight}
          age={age}
          unitAge={unitAge}
          selectedGender={selectedGender}
          ageChildrenYears={ageChildrenYears}
          ageChildrenMonths={ageChildrenMonths}
          disableContinue={disableContinue}
          handleFieldChange={handleFieldChange}
          handleUnitChange={handleUnitChange}
          handleUnitWeightChange={handleUnitWeightChange}
          handleGenderSelect={handleGenderSelect}
          setNeedValidate={setNeedValidate}
          subtitleStyles={subtitleStyles}
          isChildrenProduct={isChildrenProduct}
          handleUnitAgeChange={handleUnitAgeChange}
          policyChecked={policyChecked}
          setPolicyChecked={setPolicyChecked}
          nextStepGender={async () => {
            if (
              !uxGender &&
              isMobile &&
              step?.number === MODAL_STEPS.GENDER.number
            ) {
              setStep(MODAL_STEPS.INTRO_MOBILE);
            }
          }}
          nextStep={async () => {
            if (isChildrenProduct && userContext) {
              await userContext.createNewMeasure("question");
              sendQuestionAnswers();
            } else {
              if (uxRoute === "none" && userContext) {
                const resultNewMeasure = await userContext.createNewMeasure(
                  "question"
                );
                if (resultNewMeasure) {
                  if (selectedGender === GENDERS.M) {
                    sendQuestionAnswers();
                  } else if (selectedGender === GENDERS.F) {
                    setStep(MODAL_STEPS.BREASTS);
                  }
                }
              }

              if (["question_pushScan", "question_only"].includes(uxRoute)) {
                await handleRouteEvent(uxRoute);
                setSelectedRoute(uxRoute);
              }

              // desktop first screen
              if (
                uxRoute === "default" &&
                [
                  MODAL_STEPS.INTRO.number,
                  MODAL_STEPS.INTRO_MOBILE.number,
                ].includes(step?.number) &&
                !preselectedRoute
              ) {
                setStep(MODAL_STEPS.DEVICE_SELECT);
              }
            }

            handleSaveUserInfo();
          }}
        />
      )}
      {step?.number === MODAL_STEPS.DEVICE_SELECT.number && (
        <StepDeviceSelect
          step={step}
          isShoesProduct={isFootwear}
          nextPhotoStep={async () => {
            await handleRouteEvent("question_pushScan");
            setSelectedRoute("question_pushScan");
          }}
          nextQuestionsStep={async () => {
            await handleRouteEvent("question_only");
            setSelectedRoute("question_only");
          }}
        />
      )}
      {step?.number === MODAL_STEPS.QR_CODE.number && (
        <StepQRCode
          step={step}
          gender={selectedGender}
          setRecommendedSize={setRecommendedSize}
          titleStyles={titlesStyles}
          subtitleStyles={subtitleStyles}
          productStockData={productStockData}
          variantId={variantId}
          variantCol={variantCol}
          isShoesProduct={isFootwear}
          skipToResult={() => setStep(MODAL_STEPS.RESULT)}
          handleError={() => setStep(MODAL_STEPS.ERROR)}
          setIsScanError={setIsScanError}
        />
      )}
      {step?.number === MODAL_STEPS.SHOE_GENDER.number && (
        <StepShoeGender
          selectedShoeGender={selectedShoeGender}
          setSelectedShoeGender={setSelectedShoeGender}
          nextStep={() => setStep(MODAL_STEPS.BREASTS)}
          subtitlesStyles={subtitlesStyles}
        />
      )}
      {step?.number === MODAL_STEPS.BELLY.number && (
        <StepBelly
          step={step}
          gender={selectedGender}
          nextStep={() => setStep(MODAL_STEPS.TORSO)}
          setStep={setStep}
          setPreviousStep={setPreviousStep}
          value={bellyValue}
          subtitleStyles={subtitleStyles}
        />
      )}
      {step?.number === MODAL_STEPS.TORSO.number && (
        <StepTorse
          step={step}
          gender={selectedGender}
          nextStep={() => {
            if (selectedGender === GENDERS.M) {
              setStep(MODAL_STEPS.CUISSES);
            } else {
              setStep(MODAL_STEPS.BREASTS);
            }
          }}
          setStep={setStep}
          setPreviousStep={setPreviousStep}
          value={torsoValue}
          subtitleStyles={subtitleStyles}
        />
      )}
      {step?.number === MODAL_STEPS.CUISSES.number && (
        <StepCuisses
          step={step}
          gender={selectedGender}
          value={cuissesValue}
          nextStep={async () => {
            if (brandDefined?.name === "Hast") {
              setStep(MODAL_STEPS.SLEEVES);
            } else {
              if (userContext) {
                await userContext.createNewMeasure("question");
                sendQuestionAnswers();
              }
            }
          }}
          setStep={setStep}
          setPreviousStep={setPreviousStep}
          subtitleStyles={subtitleStyles}
        />
      )}
      {step?.number === MODAL_STEPS.SLEEVES.number && (
        <StepSleeves
          step={step}
          gender={selectedGender}
          value={sleeveValue}
          nextStep={async () => {
            if (userContext) {
              await userContext.createNewMeasure("question");
              sendQuestionAnswers();
            }
          }}
          setStep={setStep}
          setPreviousStep={setPreviousStep}
          subtitleStyles={subtitleStyles}
        />
      )}
      {step?.number === MODAL_STEPS.BREASTS.number && (
        <StepBreasts
          step={step}
          value_1={sizeValue}
          value_2={cupValue}
          selectedGender={selectedGender}
          nextStep={async () => {
            if (isLingerie) {
              setStep(MODAL_STEPS.LINGERIE_QUESTION_1);
            }

            if (isFootwear) {
              setStep(MODAL_STEPS.SHOE_QUESTIONS);
            }

            if (!isFootwear && !isLingerie) {
              if (userContext) {
                await userContext.createNewMeasure("question");
                sendQuestionAnswers();
              }
            }
          }}
          skipResult={async () => {
            if (isFootwear) {
              setStep(MODAL_STEPS.SHOE_QUESTIONS);
            } else {
              if (userContext) {
                await userContext.createNewMeasure("question");
                sendQuestionAnswers(true);
              }
            }
          }}
          sizeCountry={sizeCountry}
          setSizeCountry={setSizeCountry}
          subtitleStyles={subtitleStyles}
          isShoesProduct={isFootwear}
        />
      )}

      {/* Custom steps */}
      <LingerieSteps
        step={step}
        setStep={setStep}
        subtitlesStyles={subtitlesStyles}
        sendQuestionAnswers={sendQuestionAnswers}
        lingerieQuestion1={lingerieQuestion1}
        setLingerieQuestion1={setLingerieQuestion1}
        lingerieQuestion2={lingerieQuestion2}
        setLingerieQuestion2={setLingerieQuestion2}
        lingerieQuestion3={lingerieQuestion3}
        setLingerieQuestion3={setLingerieQuestion3}
        lingerieQuestion4={lingerieQuestion4}
        setLingerieQuestion4={setLingerieQuestion4}
      />

      <ShoeSteps
        step={step}
        shoeQuestion={shoeQuestion}
        setShoeQuestion={setShoeQuestion}
        setStep={setStep}
        subtitlesStyles={subtitlesStyles}
        sendQuestionAnswers={sendQuestionAnswers}
        mock={mock}
        domain={domain}
        visionInit={visionInit}
      />

      {step?.number === MODAL_STEPS.ERROR.number && (
        <ResultError
          step={step}
          nextStep={async () => await handleRetry()}
          isShoesProduct={isFootwear}
          outOfRange={outOfRange}
        />
      )}
      {(step?.number === MODAL_STEPS.RESULT.number ||
        step?.number === MODAL_STEPS.UNAVAILABLE_RESULT.number) && (
        <Result
          step={step}
          reducedResult={reducedResult}
          selectedGender={selectedGender}
          productStockData={productStockData}
          similarProducts={similarProducts}
          isSizeUnavailable={isSizeUnavailable}
          selectedSize={selectedSize}
          setSelectedSize={setSelectedSize}
          restart={restart}
          setSimilarProducts={setSimilarProducts}
          isShoesProduct={isFootwear}
          isChildrenProduct={isChildrenProduct}
          recommendedSize={recommendedSize}
        />
      )}
    </>
  );
};

export default DrawerSteps;
