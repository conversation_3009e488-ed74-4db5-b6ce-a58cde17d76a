<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="blue-gradient" x1="50%" y1="0%" x2="50%" y2="100%">
            <stop stop-color="#0033A0" offset="0%" />
            <stop stop-color="#00247D" offset="100%" />
        </linearGradient>
        <linearGradient id="yellow-gradient" x1="50%" y1="0%" x2="50%" y2="100%">
            <stop stop-color="#FFD700" offset="0%" />
            <stop stop-color="#FFC600" offset="100%" />
        </linearGradient>
    </defs>
    <!-- Background -->
    <rect width="21" height="15" fill="url(#blue-gradient)" />
    <!-- Circle of Stars -->
    <g transform="translate(10.5, 7.5)">
        <circle cx="0" cy="-5" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="2.8" cy="-4" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="4.6" cy="-2" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="5" cy="0" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="4.6" cy="2" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="2.8" cy="4" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="0" cy="5" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="-2.8" cy="4" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="-4.6" cy="2" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="-5" cy="0" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="-4.6" cy="-2" r="0.5" fill="url(#yellow-gradient)" />
        <circle cx="-2.8" cy="-4" r="0.5" fill="url(#yellow-gradient)" />
    </g>
</svg>
