import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import MeasuredBy from "../../../molecules/MeasuredBy";

import { GENDERS, SHOES_BASE_SIZES } from "../../../../constants/modal";

import {
  disabledStylesContinue,
  activeStylesContinue,
  femaleBraScreenUI,
  welcomeScreenUI,
  font,
  titleStyles,
} from "../../../../configs";
import { capitalizeFirstLetter, useIsMobile } from "../../../../utils";
import { findBrandByDomain } from "../../../../configs/configLoader";
import StyledSelector from "../.././components/StyledSelector";
import BaseSizeSelector from "../.././components/BaseSizeSelector";
import { handleAnalytics } from "../../../../utils/tracking";
import {
  SMALL_PADDING_BRANDS,
  MEDIUM_PADDING_BRANDS,
} from "../../../../constants/sizes";
import { hoverStylesContinue } from "../../../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";

import "./index.css";

interface IPropsShoesComponentSize {
  step: any;
  value_1: {
    current: string | null;
  };
  nextStep: () => void;
  selectedGender: string;
  sizeCountry: string;
  setSizeCountry: React.Dispatch<React.SetStateAction<string>>;
  isShoesProduct: boolean;
}

type BodyItemsType = "unfocused" | "focused";

const ShoesComponentSize: FC<IPropsShoesComponentSize> = ({
  step,
  value_1,
  nextStep,
  selectedGender,
  sizeCountry,
  setSizeCountry,
  isShoesProduct,
}) => {
  const isMobile = useIsMobile();
  const [open, setOpen] = useState(false);

  const { t } = useTranslation("components/breasts");

  const [hoveredSize, setHoveredSize] = useState<string | null>(null);

  const [selectedSize, setSelectedSize] = useState<any | undefined>();

  const brandDefined = findBrandByDomain();

  const [hovered, setHovered] = useState(false);

  const fontFamily = `${font}, sans-serif`;

  useEffect(() => {
    if (selectedSize) {
      value_1.current = selectedSize ? selectedSize?.toString() : null;
    }
  }, [sizeCountry, selectedSize, value_1]);

  useEffect(() => {
    if (value_1.current) {
      handleAnalytics("action", "bra_size", {
        key: "bra_size",
        // type: "STR",
        value: value_1.current,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value_1.current]);

  useEffect(() => {
    if (sizeCountry) {
      handleAnalytics("action", "bra_size_system", {
        key: "bra_system",
        // type: "STR",
        value: sizeCountry,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sizeCountry]);

  useEffect(() => {
    handleAnalytics("step", "bra", {
      key: "step",
      value: "6_BRA",
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePaddingForBrand = (brandName: string) => {
    const smallPadding = "10px";
    const mediumPadding = "13px";

    if (brandName === "Zadig & Voltaire" || brandName === "Lacoste") {
      return "12px";
    }

    if (SMALL_PADDING_BRANDS.includes(brandName)) {
      return smallPadding;
    } else if (MEDIUM_PADDING_BRANDS.includes(brandName)) {
      return mediumPadding;
    } else {
      return smallPadding;
    }
  };

  const getBodyItemsStyles = (
    type: BodyItemsType,
    isHovered = false,
    isSelected = false
  ) => {
    const padding = brandDefined?.name
      ? handlePaddingForBrand(brandDefined?.name)
      : "10px";

    const sizeSelectorItem = femaleBraScreenUI.sizeSelector;

    let borderColor = sizeSelectorItem[type].borderColor;

    if (brandDefined?.name === "Intersport" && isHovered && !isSelected) {
      borderColor = "#A1A1A1";
    }

    return {
      padding,
      backgroundColor: sizeSelectorItem[type].backgroundColor,
      borderRadius: sizeSelectorItem[type].borderRadius,
      borderWidth: sizeSelectorItem[type].borderWidth,
      borderStyle: "solid",
      borderColor,
      color: sizeSelectorItem[type].fontColor,
      fontSize: sizeSelectorItem[type].fontSize,
      fontWeight: sizeSelectorItem[type]
        .fontWeight as React.CSSProperties["fontWeight"],
      textTransform: sizeSelectorItem[type]
        .textTransform as React.CSSProperties["textTransform"],
      height: isMobile ? "24px" : `calc(${padding} * 3)`,
      fontFamily,
      letterSpacing: (sizeSelectorItem[type] as any)?.letterSpacing,
    };
  };

  const titleTextTransform =
    titleStyles.textTransform as React.CSSProperties["textTransform"];

  const titlesStyles = {
    fontWeight: welcomeScreenUI.input_fields.title.fontWeight,
    textAlign: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["textAlign"],
    textTransform:
      titleTextTransform === "capitalize" ? "none" : titleTextTransform,
    color: welcomeScreenUI.input_fields.title
      .fontColor as React.CSSProperties["color"],
    fontSize: isMobile
      ? "14px"
      : (welcomeScreenUI.input_fields.title
          .fontSize as React.CSSProperties["fontSize"]),
    justifyContent: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["justifyContent"],
    fontStyle: (welcomeScreenUI.input_fields.title as any)
      .fontStyle as React.CSSProperties["fontStyle"],
    fontFamily: (welcomeScreenUI.input_fields.title as any)
      .fontFamily as React.CSSProperties["fontFamily"],
  };

  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...(selectedSize === undefined
      ? disabledStylesContinue
      : activeStylesContinue),
    ...(isIntersport && hovered && selectedSize ? hoverStylesContinue : {}),
    marginTop: isMobile ? "60px" : "30px",
    position: "relative" as React.CSSProperties["position"],
    marginBottom: isMobile ? "20px" : "0",
    textTransform:
      activeStylesContinue.textTransform === "capitalize"
        ? "none"
        : (activeStylesContinue.textTransform as React.CSSProperties["textTransform"]),
    padding: brandDefined?.name === "Victoria Beckham" ? "7px" : "16px",
    height: brandDefined?.name === "Victoria Beckham" ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && selectedSize ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && selectedSize ? 1 : 0,
    transform: hovered && selectedSize ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div className="breasts">
      <div
        className="breasts__titles"
        style={{
          display: "flex",
          marginTop: "30px",
        }}
      >
        <p className="breasts__titles__title" style={titlesStyles}>
          {titleTextTransform === "capitalize"
            ? capitalizeFirstLetter(t("pays"))
            : t("pays")}
        </p>
        <div onClick={() => setOpen(!open)}>
          {brandDefined?.name === "SRP" ? (
            <StyledSelector
              sizeCountry={sizeCountry}
              setSizeCountry={setSizeCountry}
              open={open}
              setOpen={setOpen}
              isShoesProduct={isShoesProduct}
            />
          ) : (
            <BaseSizeSelector
              sizeCountry={sizeCountry}
              setSizeCountry={setSizeCountry}
              open={open}
              setOpen={setOpen}
              isShoesProduct={isShoesProduct}
            />
          )}
        </div>
      </div>
      <div className="breasts__titles">
        <p className="breasts__titles__title" style={titlesStyles}>
          {titleTextTransform === "capitalize"
            ? capitalizeFirstLetter(t("size"))
            : t("size")}
        </p>
      </div>
      <div className="breasts__body">
        <table className="breasts__body__table">
          <tbody>
            <tr>
              {SHOES_BASE_SIZES.find(
                (value: any) => value.name === sizeCountry
              )?.sizes.map((row, index: number) => {
                const value =
                  selectedGender === GENDERS.M ? row?.male : row?.female;

                return (
                  <td key={value}>
                    <button
                      className={`breasts__body__table__cell ${
                        selectedSize === value
                          ? "breasts__body__table__cell--selected"
                          : undefined
                      }`}
                      onClick={() => setSelectedSize(value)}
                      onMouseEnter={() => setHoveredSize(index.toString())}
                      onMouseLeave={() => setHoveredSize(null)}
                      style={Object.assign({
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        width:
                          value?.toString()?.length > 4
                            ? "auto"
                            : `calc(${
                                getBodyItemsStyles(
                                  selectedSize === value
                                    ? "focused"
                                    : "unfocused",
                                  hoveredSize === index.toString(),
                                  selectedSize === value
                                ).padding
                              } * 3)`,
                        ...(selectedSize === value
                          ? getBodyItemsStyles(
                              "focused",
                              hoveredSize === index.toString(),
                              true
                            )
                          : getBodyItemsStyles(
                              "unfocused",
                              hoveredSize === index.toString(),
                              false
                            )),
                      })}
                    >
                      {value}
                    </button>
                  </td>
                );
              })}
            </tr>
          </tbody>
        </table>
      </div>
      <button
        type="button"
        disabled={selectedSize === undefined}
        className={`continue-button breasts-continue`}
        onMouseEnter={(e) => {
          setHovered(true);
          if (selectedSize) {
            Object.assign(e.currentTarget.style, hoverStylesContinue);
          }
        }}
        onMouseLeave={(e) => {
          setHovered(false);
          if (selectedSize) {
            Object.assign(e.currentTarget.style, CTAStyles);
          }
        }}
        onClick={() => {
          nextStep();
          handleAnalytics("action", "continue", {
            key: "current_step",
            value: "6_BRA",
          });
        }}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {activeStylesContinue.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("continue", { ns: "components/intro" }))
            : t("continue", { ns: "components/intro" })}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>
      <MeasuredBy step={step} />
    </div>
  );
};

export default ShoesComponentSize;
