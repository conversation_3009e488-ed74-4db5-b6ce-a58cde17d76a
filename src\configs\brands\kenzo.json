{"name": "KENZO", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": true, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#2E2E2E", "textTransform": "none", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#F9F9F9", "fontWeight": "400", "fontSize": "13px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#F9F9F9", "fontWeight": "400", "fontSize": "13px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "8px", "mobileBorderRadius": "8px", "borderColor": "#2E2E2E", "marginBottom": 0, "padding": "15px 0 15px 20px", "borderBottom": "none"}}, "genderCTA": {"unfocused": {"backgroundColor": "#939393", "fontColor": "#FFFFFF", "borderRadius": "8px", "borderColor": "#939393", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#2E2E2E", "fontColor": "#FFFFFF", "borderRadius": "8px", "borderColor": "#2E2E2E", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "12px", "right": "20px", "activeColor": "#2E2E2E", "inactiveColor": "#9F9F9F"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#2E2E2E", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "13px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "11px", "fontColor": "#2E2E2E", "textTransform": "capitalize", "borderRadius": "8px", "borderColor": "#E9E9E9", "borderWidth": "1px"}, "focused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "11px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "8px", "borderColor": "#44883F", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "11px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "8px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textTransform": "none", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#B9B9B9", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Graphik", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#2E2E2E", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#2E2E2E", "fontSize": "13px"}, "cta": {"unfocused": {"backgroundColor": "#939393", "fontWeight": "600", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "8px", "borderColor": "#939393", "borderWidth": "2px", "borderTop": "2px solid #939393", "borderBottom": "2px solid #939393", "borderLeft": "2px solid #939393", "borderRight": "2px solid #939393"}, "focused": {"backgroundColor": "#2E2E2E", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "8px", "borderColor": "#2E2E2E", "borderWidth": "2px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#2E2E2E", "borderRadius": "4px", "fontSize": "12px", "fontWeight": "400", "fontColor": "#2E2E2E", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#E9E9E9", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "regular", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#2E2E2E", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "regular", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#2E2E2E"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#2E2E2E", "fontSize": "13px", "fontWeight": "500", "borderRadius": "2px", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#2E2E2E", "fontColor": "#FFFFFF", "fontSize": "13px", "fontWeight": "500", "borderRadius": "2px", "borderColor": "#2E2E2E", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#2E2E2E", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}