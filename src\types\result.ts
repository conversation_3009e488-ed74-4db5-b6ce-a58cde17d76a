export interface FitIndicatorType {
  limb: string;
  value: number;
}

export interface SizeDataType {
  label: string;
  variant_id: string;
  reference?: string;
  possible: number;
  label_rank: number;
  fit_indicators: FitIndicatorType[];
}

export interface ReducedResultType {
  [key: string]: SizeDataType;
}

export interface ProductFeedbacks {
  params: {
    hip: number;
    waist: number;
    chest: number;
  };
  image: string;
}
