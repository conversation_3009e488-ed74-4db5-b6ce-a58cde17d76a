{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#A3A1A7", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#0a0909", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#0a0909", "borderRadius": "0px", "borderColor": "#0a0909", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#0a0909", "fontColor": "#FFFFFF", "borderRadius": "0px", "borderColor": "0px", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#0a0909", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#0a0909"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#0a0909", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#0a0909", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#0a0909", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#D9D9D9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#0a0909", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "0a0909", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "none", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "<PERSON><PERSON>", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "uppercase", "color": "#0a0909", "fontSize": "14px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "lowercase", "color": "#646060", "fontSize": "12px"}, "cta": {"unfocused": {"backgroundColor": "#d9d9d9", "fontWeight": "400", "fontSize": "10px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "none", "borderWidth": "0px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#0a0909", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "10px", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "none", "borderWidth": "0px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "", "borderRadius": "0", "fontSize": "10px", "fontWeight": "400", "fontColor": "#0a0909", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#0a0909", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#0a0909", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#0a0909", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#0a0909", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#0a0909", "borderWidth": "0px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#0a0909", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#0a0909", "borderWidth": "0px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#0a0909", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "none", "fontColor": "#0a0909", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "none", "borderWidth": "0px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "none", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "none", "borderWidth": "0px", "textTransform": "uppercase", "textDecoration": "underline"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "none", "fontSize": "12px"}}}}}