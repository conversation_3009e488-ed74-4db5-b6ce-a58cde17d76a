{"name": "<PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "12px", "fontColor": "#282828", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#282828", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #282828"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#282828", "borderRadius": "0", "borderColor": "#282828", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#282828", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#282828", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "8px", "right": "-11px", "activeColor": "#282828", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#282828"}}, "3": {"qrcode": {"backgroundColor": "#282828"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#282828", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#282828", "textAlign": "center"}, "sizeSelector": {"focused": {"backgroundColor": "#D9D9D9", "fontWeight": "600", "fontSize": "12px", "fontColor": "#282828", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#282828", "borderWidth": "1px"}, "unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "12px", "fontColor": "#282828", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#282828", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#282828", "textAlign": "center"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#282828", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#898989", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#B9B9B9", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Roboto", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#282828", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#282828", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#DDDDDD", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "0px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#282828", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#282828", "borderWidth": "0px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#282828", "borderRadius": "0", "fontSize": "12px", "fontWeight": "600", "fontColor": "#282828", "textTransform": "unset", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#dfdfdf", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "12px", "fontColor": "#282828", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#282828", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "600", "fontSize": "12px", "fontColor": "#282828", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#413F3F"}, "unfocused": {"color": "#dfdfdf"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "13px", "fontColor": "#282828", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "13px", "fontColor": "#282828", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#737373", "fontSize": "12px", "fontWeight": "600", "borderRadius": "0", "borderColor": "#E9E9E9", "borderWidth": "2px", "textTransform": "unset"}, "focused": {"backgroundColor": "#282828", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "600", "borderRadius": "0", "borderColor": "#282828", "borderWidth": "2px", "textTransform": "unset"}}, "skip": {"fontColor": "#898989", "fontWeight": "400", "fontStyle": "capitalize", "fontSize": "14px"}}}}}