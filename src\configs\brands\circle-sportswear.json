{"name": "Circle Sportwear", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#010F16", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#010f16", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#010f16", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#010f16", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #010f16"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#010f16", "borderRadius": "5px", "borderColor": "#010F16", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#010f16", "fontColor": "#FFFFFF", "borderRadius": "5px", "borderColor": "#010f16", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#010F16", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#010F16"}}, "3": {"qrcode": {"backgroundColor": "#010F16"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#010F16", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"focused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#44883F", "borderWidth": "1px"}, "unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#010F16", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#010F16", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#010F16", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#010F16", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#010F16", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "PublicSans", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#010F16", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#545454", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#c6c3c3", "fontWeight": "500", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "transparent", "borderWidth": "", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#010F16", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "#010F16", "borderWidth": ""}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#010F16", "borderRadius": "5px", "fontSize": "12px", "fontWeight": "500", "fontColor": "#010F16", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#010F16", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#010F16", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#010F16", "borderWidth": "2px", "borderRadius": "5px", "fontWeight": "500", "fontSize": "14px", "fontColor": "#010F16", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#010F16"}, "unfocused": {"color": "#e6e6e8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#010F16", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#010F16", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#010F16", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#010F16", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#010F16", "fontColor": "#FFFFFF", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#010F16", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#010F16", "fontWeight": "400", "fontStyle": "underline", "fontSize": "16px"}}}}}