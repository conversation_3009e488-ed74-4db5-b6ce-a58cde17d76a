import { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import GenderBlock from "./components/GenderBlock";
import UserInfoBlock from "./components/UserInfoBlock";
import MeasuredBy from "../molecules/MeasuredBy";

import { MODAL_STEPS } from "../../constants/modal";
import {
  activeStylesContinue,
  disabledStylesContinue,
  uxGender,
  welcomeScreenUI,
  policityStyles,
} from "../../configs";
import CustomCheckbox from "../ConsentScreen/components/CustomCheckbox";
import { capitalizeFirstLetter, useIsMobile } from "../../utils";
import { findBrandByDomain } from "../../configs/configLoader";
import { handleAnalytics } from "../../utils/tracking";
import { logger } from "../../utils/logging";
import { hoverStylesContinue } from "../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";

import "./index.css";

interface StepIntroDesktopProps {
  step: any;
  error: any;
  height: string;
  unit: string;
  feet: string;
  inches: string;
  unitWeight: string;
  weight?: string | undefined;
  age: string;
  selectedGender: string;
  disableContinue: boolean;
  handleFieldChange: (e: any, type: string, blurSignal: string) => void;
  handleUnitChange: (newUnit: string) => void;
  handleUnitWeightChange: (newUnit: string) => void;
  handleGenderSelect: (newUnit: string) => void;
  setNeedValidate: React.Dispatch<React.SetStateAction<boolean>>;
  nextStepGender: () => void;
  nextStep: () => void;
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
  isChildrenProduct: boolean;
  ageChildrenYears: string;
  ageChildrenMonths: string;
  unitAge: string;
  handleUnitAgeChange: (newUnit: string) => void;
  policyChecked: boolean;
  setPolicyChecked: React.Dispatch<React.SetStateAction<boolean>>;
}

const StepIntroDesktop: React.FC<StepIntroDesktopProps> = ({
  step,
  error,
  height,
  unit,
  feet,
  inches,
  unitWeight,
  weight,
  age,
  selectedGender,
  disableContinue,
  handleFieldChange,
  handleUnitChange,
  handleUnitWeightChange,
  handleGenderSelect,
  setNeedValidate,
  nextStepGender,
  nextStep,
  subtitleStyles,
  isChildrenProduct,
  ageChildrenYears,
  ageChildrenMonths,
  unitAge,
  handleUnitAgeChange,
  policyChecked,
  setPolicyChecked,
}) => {
  const isMobile = useIsMobile();

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: !isMobile ? 830 : 778,
    minWidth: 767,
  });

  const isMediumHeightRelative = useMediaQuery({
    maxHeight: 678,
    minWidth: 767,
  });

  const isSmallHeightRelative = useMediaQuery({
    maxHeight: 620,
    minWidth: 767,
  });

  const [hovered, setHovered] = useState(false);

  const { t } = useTranslation("components/intro");

  const brandDefined = findBrandByDomain();

  const isMobileSRP = isMobile && brandDefined?.name === "SRP";
  const isSportyAndRich = brandDefined?.name === "Sporty & Rich";
  const isVB = brandDefined?.name === "Victoria Beckham";
  const isSRP = brandDefined?.name === "SRP";
  const isPetrone = brandDefined?.name === "Petrone";
  const isNinaRicci = brandDefined?.name === "Nina Ricci";

  const handleChangePolicyCheck = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPolicyChecked(event.target.checked);
    handleAnalytics("action", "consent_click", {
      key: "value",
      value: event.target.checked,
    });
  };

  const continueButtonStyles = disableContinue
    ? disabledStylesContinue
    : activeStylesContinue;

  const titlesStyles = {
    fontWeight: welcomeScreenUI.input_fields.title.fontWeight,
    textAlign: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["textAlign"],
    textTransform: welcomeScreenUI.input_fields.title
      .textTransform as React.CSSProperties["textTransform"],
    color: welcomeScreenUI.input_fields.title
      .fontColor as React.CSSProperties["color"],
    fontSize:
      (isLargeHeightRelative && !isSportyAndRich) ||
      (isMediumHeightRelative && isSportyAndRich)
        ? `calc(${welcomeScreenUI.input_fields.title.fontSize} - 2px)`
        : isMobile
        ? "14px"
        : (welcomeScreenUI.input_fields.title
            .fontSize as React.CSSProperties["fontSize"]),
    justifyContent: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["justifyContent"],
    letterSpacing: (welcomeScreenUI.input_fields.title as any)?.letterSpacing,
    fontStyle: (welcomeScreenUI.input_fields.title as any)
      .fontStyle as React.CSSProperties["fontStyle"],
    fontFamily: (welcomeScreenUI.input_fields.title as any)
      .fontFamily as React.CSSProperties["fontFamily"],
  };

  const descriptionStyles = {
    ...subtitleStyles,
    fontSize: isSRP
      ? subtitleStyles.fontSize
      : (isLargeHeightRelative && !isSportyAndRich) ||
        (isMediumHeightRelative && isSportyAndRich)
      ? `calc(${subtitleStyles.fontSize} - 2px)`
      : subtitleStyles.fontSize,
    textTransform:
      subtitleStyles.textTransform === "capitalize"
        ? "none"
        : subtitleStyles.textTransform,
    marginTop: isSRP
      ? "40px"
      : (isLargeHeightRelative && !isSportyAndRich) ||
        (isMediumHeightRelative && isSportyAndRich)
      ? "20px"
      : 0,
    marginBottom:
      (isLargeHeightRelative && !isSportyAndRich) ||
      (isMediumHeightRelative && isSportyAndRich)
        ? "20px"
        : !isMobile
        ? "40px"
        : "30px",
    letterSpacing: (subtitleStyles as any)?.letterSpacing,
    fontFamily: (welcomeScreenUI.input_fields.title as any)
      .fontFamily as React.CSSProperties["fontFamily"],
  };

  const customLinkStyles = {
    fontSize: policityStyles.fontSize,
    color: welcomeScreenUI.input_fields.title
      .fontColor as React.CSSProperties["color"],
    fontWeight: policityStyles.fontWeight,
    letterSpacing: (policityStyles as any)?.letterSpacing,
  };

  useEffect(() => {
    const heightInput = document.querySelector(
      "#height"
    ) as HTMLInputElement | null;
    if (heightInput) {
      heightInput?.focus();
    } else {
      logger.log("Element with id 'height' not found.");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isIntersport = brandDefined?.name === "Intersport";

  const mergedButtonStyles: React.CSSProperties = {
    ...continueButtonStyles,
    ...(isIntersport && hovered && !disableContinue ? hoverStylesContinue : {}),
    textTransform:
      continueButtonStyles?.textTransform === "capitalize"
        ? "none"
        : continueButtonStyles?.textTransform,
    marginBottom:
      isMobile && isVB
        ? "30px"
        : isMobileSRP
        ? "20px"
        : !isMobile
        ? "80px"
        : "10px",
    position: "relative",
    bottom: isMobileSRP ? "22px" : 0,
    padding: isVB ? "7px" : "16px",
    height: isVB ? "30px" : "auto",
    display: "inline-flex",
    alignItems: "center",
    justifyContent: "center",
    overflow: "visible",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && !disableContinue ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && !disableContinue ? 1 : 0,
    transform:
      hovered && !disableContinue ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <>
      {!isVB ? (
        <>
          {!isMobile && (
            <span className="description" style={descriptionStyles}>
              {subtitleStyles.textTransform === "capitalize"
                ? capitalizeFirstLetter(t("description"))
                : t("description")}
            </span>
          )}
          {((isMobile && step?.number === MODAL_STEPS.GENDER.number) ||
            isMobileSRP) && (
            <span className="descriptionSmall" style={descriptionStyles}>
              {(descriptionStyles.textTransform as string) === "capitalize"
                ? capitalizeFirstLetter(
                    t(isSRP ? "description" : "gender.title")
                  )
                : t(isSRP ? "description" : "gender.title")}
            </span>
          )}
          {isMobileSRP && step?.number === MODAL_STEPS.GENDER.number && (
            <span className="descriptionSmall" style={titlesStyles}>
              {(descriptionStyles.textTransform as string) === "capitalize"
                ? capitalizeFirstLetter(t("gender.title"))
                : t("gender.title")}
            </span>
          )}
        </>
      ) : null}
      <form
        style={{
          marginTop: isMobile ? "30px" : 0,
          gap:
            (isMediumHeightRelative && !isSportyAndRich) ||
            (isSmallHeightRelative && isSportyAndRich) ||
            isPetrone
              ? "5px"
              : "15px",
        }}
      >
        {(!isMobile ||
          (isMobile && step?.number === MODAL_STEPS.INTRO_MOBILE.number)) && (
          <UserInfoBlock
            error={error}
            height={height}
            unit={unit}
            feet={feet}
            inches={inches}
            unitWeight={unitWeight}
            weight={weight}
            age={age}
            handleFieldChange={handleFieldChange}
            handleUnitChange={handleUnitChange}
            handleUnitWeightChange={handleUnitWeightChange}
            setNeedValidate={setNeedValidate}
            titlesStyles={titlesStyles}
            disableContinue={disableContinue}
            nextStep={nextStep}
            isChildrenProduct={isChildrenProduct}
            ageChildrenYears={ageChildrenYears}
            ageChildrenMonths={ageChildrenMonths}
            unitAge={unitAge}
            handleUnitAgeChange={handleUnitAgeChange}
          />
        )}
        {isNinaRicci ? (
          <div
            style={{
              display: "flex",
              gap: "5px",
            }}
          >
            <CustomCheckbox
              checked={policyChecked}
              onChange={handleChangePolicyCheck}
              style={{
                color: "#000000",
                margin: 0,
                padding: 0,
              }}
            />
            <p
              style={customLinkStyles}
              dangerouslySetInnerHTML={{
                __html: t("pp_link")
                  .replace(/<a(.*?)>/g, (match: any) => {
                    return `<a style="${Object.entries(customLinkStyles)
                      .map(([key, value]) => `${key}: ${value}`)
                      .join("; ")}" target="_blank"${match.slice(2)}`;
                  })
                  .replace(/<\/a>\./g, ".</a>"),
              }}
            />
          </div>
        ) : null}
        <div
          className="gender-block"
          style={{
            marginBottom:
              isMobile && isSRP && step?.number === MODAL_STEPS.GENDER.number
                ? "20px"
                : "0",
          }}
        >
          {!uxGender && (
            <>
              {!isMobile && (
                <label
                  htmlFor="gender"
                  style={{
                    ...titlesStyles,
                    textTransform:
                      titlesStyles.textTransform === "capitalize"
                        ? "none"
                        : (titlesStyles.textTransform as React.CSSProperties["textTransform"]),
                  }}
                >
                  {titlesStyles.textTransform === "capitalize"
                    ? capitalizeFirstLetter(t("gender.title"))
                    : t("gender.title")}
                </label>
              )}
              {((isMobile && brandDefined?.name === "Velour Garments") ||
                step.number !== MODAL_STEPS.INTRO_MOBILE.number) && (
                <GenderBlock
                  selectedGender={selectedGender}
                  handleGenderSelect={handleGenderSelect}
                  nextStep={nextStepGender}
                  nextStepDesktop={nextStep}
                  disableContinue={disableContinue}
                  isChildrenProduct={isChildrenProduct}
                />
              )}
            </>
          )}
        </div>

        {!(isMobile && step?.number === MODAL_STEPS.GENDER.number) && (
          <button
            type="button"
            disabled={disableContinue}
            className="continue-button"
            onMouseEnter={() => {
              if (!disableContinue) setHovered(true);
            }}
            onMouseLeave={() => {
              if (!disableContinue) setHovered(false);
            }}
            onClick={() => {
              if (!disableContinue) {
                nextStep();
                handleAnalytics("action", "info_validation", null);
              }
            }}
            style={mergedButtonStyles}
          >
            <div
              style={{ display: "inline-flex", alignItems: "center", gap: 0 }}
            >
              {continueButtonStyles?.textTransform === "capitalize"
                ? capitalizeFirstLetter(t("continue"))
                : t("continue")}

              {isIntersport && (
                <span style={arrowContainerStyle} aria-hidden>
                  <ChevronRight style={iconStyle} />
                </span>
              )}
            </div>
          </button>
        )}
      </form>
      <MeasuredBy step={step} />
    </>
  );
};

export default StepIntroDesktop;
