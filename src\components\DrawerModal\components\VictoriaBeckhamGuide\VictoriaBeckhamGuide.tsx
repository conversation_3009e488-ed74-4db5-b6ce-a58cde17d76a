import { useIsMobile } from "../../../../utils";

import "./index.css";

interface VictoriaBeckhamGuideProps {
  step: any;
  stepMap: { label: string; stepNumber: number }[];
}

const VictoriaBeckhamGuide = ({ step, stepMap }: VictoriaBeckhamGuideProps) => {
  const isMobile = useIsMobile();

  const currentStepNumber =
    step?.number ??
    stepMap.find((s) => s.label.toUpperCase() === "ABOUT YOU")?.stepNumber ??
    stepMap[0]?.stepNumber;

  const currentIndex = stepMap.findIndex(
    (s) => s.stepNumber === currentStepNumber
  );

  const isLeftActive = currentIndex <= 2;
  const isRightActive = currentIndex >= 3;

  let displayedSteps = stepMap;

  if (isMobile) {
    if (isLeftActive) {
      displayedSteps = stepMap.slice(0, 3);
    } else if (isRightActive) {
      displayedSteps = stepMap.slice(-3);
    }
  }

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "100%",
      }}
    >
      <div
        style={{
          display: "flex",
          textAlign: "center",
          justifyContent: "center",
          marginTop: isMobile ? "30px" : "80px",
        }}
      >
        <span
          style={{
            fontSize: isMobile ? "32px" : "36px",
            fontWeight: 400,
            color: "#000000",
            textAlign: "center",
            fontStyle: "italic",
          }}
        >
          Size & Fit Guide
        </span>
      </div>

      <div
        style={{
          display: "flex",
          border: "1px solid #000",
          marginTop: isMobile ? "0px" : "50px",
          marginBottom: isMobile ? "0px" : "20px",
          overflowX: "hidden",
          fontFamily: "Arial Custom, sans-serif !important",
        }}
      >
        {displayedSteps?.map(({ label, stepNumber }, index) => (
          <div
            key={label}
            style={{
              flex: 1,
              padding: "10px 0",
              borderLeft: index !== 0 ? "1px solid #000" : "none",
              backgroundColor:
                currentStepNumber === stepNumber ? "#000" : "#fff",
              color: currentStepNumber === stepNumber ? "#fff" : "#000",
              fontWeight: 400,
              fontSize: "10px",
              fontFamily: "Arial Custom, sans-serif !important",
              fontStyle: "normal",
              textAlign: "center",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {label}
          </div>
        ))}
      </div>

      {isMobile ? (
        <div className="progress-bar-wrapper">
          <div
            className="progress-bar-half"
            style={{
              backgroundColor: isLeftActive ? "#000" : "#e0e0e0",
              borderTopLeftRadius: "2px",
              borderBottomLeftRadius: "2px",
            }}
          />
          <div
            className="progress-bar-half"
            style={{
              backgroundColor: isRightActive ? "#000" : "#e0e0e0",
              borderTopRightRadius: "2px",
              borderBottomRightRadius: "2px",
            }}
          />
        </div>
      ) : null}
    </div>
  );
};

export default VictoriaBeckhamGuide;
