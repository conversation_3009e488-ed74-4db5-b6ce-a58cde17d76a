import { SizeDataType } from "../types/result";

export const getActualDescriptionApparel = (
  selectedVariant: string,
  selectedSize: SizeDataType | null
) => {
  if (selectedVariant) {
    switch (selectedVariant) {
      case "0":
        return selectedSize?.possible === 0
          ? "description.size_down.not_possible"
          : "description.size_down.ideal";
      case "1":
        return "description.normal.ideal";
      case "2":
        return selectedSize?.possible === 0
          ? "description.size_up.not_possible"
          : "description.size_up.ideal";
      default:
        return "description.normal.ideal";
    }
  } else {
    return "description.normal.ideal";
  }
};

export const getActualDescriptionShoes = (
  selectedSize: any,
  smallerSize: any,
  goodSize: any,
  biggerSize: any
) => {
  if (selectedSize?.variant_id) {
    if (smallerSize?.variant_id === selectedSize?.variant_id) {
      return selectedSize?.possible === 0
        ? "description.shoe.size_down.not_possible"
        : "description.shoe.size_down.ideal";
    } else if (goodSize?.variant_id === selectedSize?.variant_id) {
      return "description.shoe.normal.ideal";
    } else if (biggerSize?.variant_id === selectedSize?.variant_id) {
      return selectedSize?.possible === 0
        ? "description.shoe.size_up.not_possible"
        : "description.shoe.size_up.ideal";
    }
  }
  return "description.shoe.normal.ideal";
};
