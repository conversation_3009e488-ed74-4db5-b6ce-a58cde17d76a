{"name": "Sporty & Rich", "config": {"ux": {"gender": "", "route": "none", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "16px", "fontColor": "#000000", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#2E2E2E", "marginBottom": 0, "padding": "5px 0", "borderBottom": "1px solid #2E2E2E"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#2E2E2E", "fontWeight": "500", "fontSize": "12px", "borderRadius": "0", "borderColor": "#2E2E2E", "borderWidth": "1px", "textTransform": "none"}, "focused": {"backgroundColor": "#007834", "fontColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "", "textTransform": "none"}}, "units": {"fontWeight": "500", "fontSize": "12px", "top": "2px", "right": "", "activeColor": "#2E2E2E", "inactiveColor": "#A7A7A7"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#2E2E2E", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "400", "fontSize": "14px", "fontColor": "#2E2E2E", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#2E2E2E", "borderWidth": "1px"}, "focused": {"backgroundColor": "", "fontWeight": "400", "fontSize": "14px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#2E2E2E", "textAlign": "center"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#2E2E2E", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#2E2E2E", "textTransform": "none", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#B9B9B9", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Futura", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "none", "color": "#2E2E2E", "fontSize": "20px"}, "subtitles": {"textAlign": "left", "fontWeight": "500", "textTransform": "none", "color": "#2E2E2E", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "medium", "fontColor": "#2E2E2E", "fontSize": "12px", "textTransform": "none", "borderRadius": "0", "borderColor": "#2E2E2E", "borderWidth": "1px", "borderTop": "1px solid #2E2E2E", "borderBottom": "1px solid #2E2E2E", "borderLeft": "1px solid #2E2E2E", "borderRight": "1px solid #2E2E2E"}, "focused": {"backgroundColor": "#007834", "fontWeight": "medium", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "none", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#2E2E2E", "borderRadius": "4px", "fontSize": "12px", "fontWeight": "600", "fontColor": "#2E2E2E", "textTransform": "unset", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "none", "fontWeight": "regular", "fontSize": "12px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#2E2E2E", "borderWidth": "2px", "borderRadius": "none", "fontWeight": "regular", "fontSize": "12px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#2E2E2E"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#9b9da0", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#9b9da0", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#737373", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "unset"}, "focused": {"backgroundColor": "#007834", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#007834", "borderWidth": "1px", "textTransform": "unset"}}, "skip": {"fontColor": "#898989", "fontWeight": "400", "fontStyle": "underline", "fontSize": "12px"}}}}}