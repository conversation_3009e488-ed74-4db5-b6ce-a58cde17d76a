{"components/results/result": {"mark1": "<PERSON><PERSON>", "mark2": "Ajuste perfecto", "size": {"title": "Talle perfecto", "size1": "<PERSON><PERSON> <PERSON>", "size2": "Ideal", "size3": "<PERSON><PERSON>", "variations": {"size1": "Regular", "size2": "Oversize", "size3": "Ajuste perfecto", "size4": "<PERSON><PERSON>", "size5": "Ligeramente ajustado", "size6": "<PERSON><PERSON>", "size7": "Intencionalmente holgado", "size8": "Ligeramente holgado", "size9": "<PERSON><PERSON><PERSON>", "size10": "<PERSON><PERSON>"}}, "description": {"normal": {"ideal": "Según tu morfología y los comentarios de nuestros clientes, creemos que la talla [S] te quedará como un guante.", "not_possible_up": "No se ha podido identificar su talla ideal : según tu morfología, la talla [S] será demasiado grande.", "not_possible_down": "No se ha podido identificar su talla ideal : según tu morfología, la talla [S] será vdemasiado pequeña.", "not_possible_secondary": "Le recomendamos que acuda a una de nuestras tiendas para recibir asistencia personalizada."}, "size_up": {"ideal": "Para un ajuste más holgado, elige la talla [S].", "not_possible": "Según tu morfología, la talla [S] será demasiado grande. Te recomendamos elegir una talla inferior."}, "size_down": {"ideal": "Para un ajuste más entallado, elige la talla [S].", "not_possible": "Según tu morfología, la talla [S] será vdemasiado pequeña. Te recomendamos elegir una talla superior."}}, "result": {"title": "Ajuste regular.", "description": "Equilibrio perfecto entre estilo y comodidad para un look deportivo y casual."}, "unavailable": {"description": "Desafortunadamente, la talla seleccionada está agotada.", "title": "Intenta de nuevo :"}, "button": "Agrega el talle [S] al carrito", "skip": "Intenta de nuevo"}}