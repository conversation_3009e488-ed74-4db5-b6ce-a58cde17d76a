import { Brand } from "../types/domains";
import { combinedConfig } from "./combinedConfig";

export const brandsArray: Brand[] = [
  {
    name: "Lacoste",
    domains: [
      {
        name: "lacoste.com",
        unit_system: "metric",
      },
      {
        name: "shop-fr-staging.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "us.lacoste.com",
        unit_system: "imperial",
        defaultBraSize: "US",
      },
      {
        name: "gb.lacoste.com",
        unit_system: "imperial",
        defaultBraSize: "UK",
      },
      {
        name: "br.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "de.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "at.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "be.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "ch.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "dk.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "es.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "ie.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "it.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "nl.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "pt.lacoste.com",
        unit_system: "metric",
      },
      {
        name: "se.lacoste.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.lacoste.config,
  },
  {
    name: "KENZO",
    domains: [
      {
        name: "kenzo.com",
        unit_system: "metric",
        defaultBraSize: "FR",
      },
      {
        name: "www.kenzo.com",
        unit_system: "metric",
        defaultBraSize: "FR",
      },
      {
        name: "www.kenzo.com/en-us",
        unit_system: "imperial",
        defaultBraSize: "US",
      },
      {
        name: "stg.kenzo.com",
        unit_system: "metric",
        defaultBraSize: "FR",
      },
      {
        name: "sb03.kenzo.com",
        unit_system: "metric",
        defaultBraSize: "FR",
      },
    ],
    config: combinedConfig.kenzo.config,
  },
  {
    name: "Sporty & Rich",
    domains: [
      {
        name: "sportyandrich.com",
        unit_system: "metric",
      },
      {
        name: "sporty-rich.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.sportyAndRich.config,
  },
  {
    name: "Place des Tendances",
    domains: [
      {
        name: "placedestendances.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.placeDesTondances.config,
  },
  {
    name: "Le Slip Français",
    domains: [
      {
        name: "www.leslipfrancais.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.leSlipFrancais.config,
  },
  {
    name: "IZAC",
    domains: [
      {
        name: "izac.fr",
        unit_system: "metric",
      },
      {
        name: "izac-paris.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.izac.config,
  },
  {
    name: "JOTT",
    domains: [
      {
        name: "jott.com",
        unit_system: "metric",
      },
      {
        name: "justoverthetop-testing.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.jott.config,
  },
  {
    name: "From Future",
    domains: [
      {
        name: "www.fromfuture.com",
        unit_system: "metric",
      },
      {
        name: "fromfuture-int.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.fromFuture.config,
  },
  {
    name: "Circle SportsWear",
    domains: [
      {
        name: "circlesportswear.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.circleSportswear.config,
  },
  {
    name: "Rodier",
    domains: [
      {
        name: "www.rodier.fr",
        unit_system: "metric",
      },
      {
        name: "preprod02-fo-rodier.wshop.cloud",
        unit_system: "metric",
      },
      {
        name: "en.rodier.fr",
        unit_system: "imperial",
        defaultBraSize: "UK",
      },
      {
        name: "preprod-fo.rodier-en.wshop.cloud",
        unit_system: "imperial",
        defaultBraSize: "UK",
      },
      {
        name: "www.rodier.be",
        unit_system: "metric",
      },
      {
        name: "www.rodier.at",
        unit_system: "metric",
      },
      {
        name: "www.de-rodier.com",
        unit_system: "metric",
      },
      {
        name: "www.rodier.nl",
        unit_system: "metric",
      },
      {
        name: "www.rodier.pt",
        unit_system: "metric",
      },
      {
        name: "www.rodier.es",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.rodier.config,
  },
  {
    name: "La Canadienne",
    domains: [
      {
        name: "la-canadienne.com",
        unit_system: "metric",
      },
      {
        name: "la-canadienne-boutique.myshopify.com",
        unit_system: "metric",
      },
      {
        name: "www.la-canadienne.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.laCanadienne.config,
  },
  {
    name: "Gerard Darel",
    domains: [
      {
        name: "gerarddarel.com",
        unit_system: "metric",
      },
      {
        name: "gerard-darel-development.myshopify.com",
        unit_system: "metric",
      },
      {
        name: "gerard-darel.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.gerardDarel.config,
  },
  {
    name: "Ron Dorff",
    domains: [
      {
        name: "rondorff.com",
        unit_system: "metric",
      },
      {
        name: "uk.rondorff.com",
        unit_system: "metric",
      },
      {
        name: "us.rondorff.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.ronDorff.config,
  },
  {
    name: "The Kooples",
    domains: [
      {
        name: "thekooples.com",
        unit_system: "metric",
      },
      {
        name: "dev.thekooples.com",
        unit_system: "metric",
      },
      {
        name: "stg.thekooples.com",
        unit_system: "metric",
      },
      {
        name: "staging.thekooples.com",
        unit_system: "metric",
      },
      {
        name: "www.thekooples.com",
        unit_system: "metric",
      },
      {
        name: "getkleep-marcy-paris.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.theKooples.config,
  },
  {
    name: "Chlore",
    domains: [
      {
        name: "www.chlore-swimwear.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.chlore.config,
  },
  {
    name: "Molli",
    domains: [
      {
        name: "www.molli.com",
        unit_system: "metric",
      },
      {
        name: "molli.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.molli.config,
  },
  {
    name: "Place du Jour",
    domains: [
      {
        name: "placedujour.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.placeDuJour.config,
  },
  {
    name: "Gualap",
    domains: [
      {
        name: "gualap.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.gualap.config,
  },
  {
    name: "Vogstore",
    domains: [
      {
        name: "vogstoree.com",
        unit_system: "metric",
      },
      {
        name: "vog-store.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.vogstore.config,
  },
  {
    name: "SRP",
    domains: [
      {
        name: "showroomprive.com",
        unit_system: "metric",
      },
      {
        name: "fr-v4.terone.showroomprive.net",
        unit_system: "metric",
      },
      {
        name: "saldiprivati.com",
        unit_system: "metric",
      },
      {
        name: "showroomprive.es",
        unit_system: "metric",
      },
      {
        name: "showroomprive.pt",
        unit_system: "metric",
      },
      {
        name: "showroomprive.be",
        unit_system: "metric",
      },
      {
        name: "showroomprive.nl",
        unit_system: "metric",
      },
      {
        name: "showroomprive.ma",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.srp.config,
  },
  {
    name: "Hartford",
    domains: [
      {
        name: "hartford.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.hartford.config,
  },
  {
    name: "La Petite Etoile",
    domains: [
      {
        name: "www.lapetiteetoile.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.laPetiteEtoile.config,
  },
  {
    name: "Mister K",
    domains: [
      {
        name: "misterk.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.misterK.config,
  },
  {
    name: "Petrone",
    domains: [
      {
        name: "petroneparis.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.petrone.config,
  },
  {
    name: "Zadig & Voltaire",
    domains: [
      {
        name: "zadig-et-voltaire.com",
        unit_system: "metric",
      },
      {
        name: "www.preprod.eu.z-v.cloud",
        unit_system: "metric",
      },
      {
        name: "www.jupiter.eu.z-v.cloud",
        unit_system: "metric",
      },
      {
        name: "www.venus.eu.z-v.cloud",
        unit_system: "metric",
      },
      {
        name: "www.saturne.eu.z-v.cloud",
        unit_system: "metric",
      },
      {
        name: "www.neptune.eu.z-v.cloud",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.zadigVoltaire.config,
  },
  {
    name: "Bensimon",
    domains: [
      {
        name: "bensimon.com",
        unit_system: "metric",
      },
      {
        name: "preprod-fo.bensimon.wshop.cloud",
        unit_system: "metric",
      },
      {
        name: "en.bensimon.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.bensimon.config,
  },
  {
    name: "Stella & Suzie",
    domains: [
      {
        name: "stellaetsuzie.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.stellaSuzie.config,
  },
  {
    name: "Antonelle",
    domains: [
      {
        name: "www.antonelle.fr",
        unit_system: "metric",
      },
      {
        name: "antonellefr.preprod123.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.antonelle.config,
  },
  {
    name: "Une Jour Ailleurs",
    domains: [
      {
        name: "unjourailleurs.com",
        unit_system: "metric",
      },
      {
        name: "unjourailleurscom.preprod123.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.uneJourAilleurs.config,
  },
  {
    name: "Kookai",
    domains: [
      {
        name: "kookai.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.kookai.config,
  },
  {
    name: "Asphalte",
    domains: [
      {
        name: "asphalte.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.asphalte.config,
  },
  {
    name: "Soeur",
    domains: [
      {
        name: "www.soeur.fr",
        unit_system: "metric",
        defaultBraSize: "FR",
      },
      {
        name: "us.soeur.fr",
        unit_system: "imperial",
        defaultBraSize: "US",
      },
      {
        name: "www.soeur.uk",
        unit_system: "imperial",
        defaultBraSize: "UK",
      },
    ],
    config: combinedConfig.soeur.config,
  },
  {
    name: "Et Paris",
    domains: [
      {
        name: "etparis.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.etParis.config,
  },
  {
    name: "Faguo",
    domains: [
      {
        name: "faguo-store.com",
        unit_system: "metric",
      },
      {
        name: "faguo-dev01.sidemo.fr",
        unit_system: "metric",
      },
      {
        name: "preprod01.faguo-store.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.faguo.config,
  },
  {
    name: "Project X Paris",
    domains: [
      {
        name: "www.projectxparis.com",
        unit_system: "metric",
      },
      {
        name: "project-x-paris-shop.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.projectXParis.config,
  },
  {
    name: "Hast",
    domains: [
      {
        name: "www.hastparis.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.hast.config,
  },
  {
    name: "SAAJ",
    domains: [
      {
        name: "saajparis.fr",
        unit_system: "metric",
      },
      {
        name: "saajparis.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.saaj.config,
  },
  {
    name: "Bonne Gueule",
    domains: [
      {
        name: "bonnegueule.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.bonneGueule.config,
  },
  {
    name: "Devred",
    domains: [
      {
        name: "devred.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.devred.config,
  },
  {
    name: "Theodore",
    domains: [
      {
        name: "theodore.ae",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.theodore.config,
  },
  {
    name: "LTDC",
    domains: [
      {
        name: "letempsdescerises.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.ltdc.config,
  },
  {
    name: "Nina Ricci",
    domains: [
      {
        name: "www.ninaricci.com",
        unit_system: "metric",
      },
      {
        name: "ninaricci-development-eur.myshopify.com",
        unit_system: "metric",
      },
      {
        name: "ninaricci-global.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.ninaRicci.config,
  },
  {
    name: "Maison Kitsuné",
    domains: [
      {
        name: "maisonkitsune.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.maisonKitsune.config,
  },
  {
    name: "Victoria Beckham",
    domains: [
      {
        name: "victoriabeckham.com",
        unit_system: "metric",
        language: "en",
      },
      {
        name: "www.victoriabeckham.com",
        unit_system: "imperial",
        language: "en",
        defaultBraSize: "UK",
      },
      {
        name: "victoria-beckham-uk.myshopify.com",
        unit_system: "imperial",
        language: "en",
      },
      {
        name: "international.victoriabeckham.com",
        unit_system: "metric",
        language: "en",
        defaultBraSize: "EU",
      },
      {
        name: "victoria-beckham-international.myshopify.com",
        unit_system: "metric",
        language: "en",
      },
      {
        name: "us.victoriabeckham.com",
        unit_system: "imperial",
        language: "en",
        defaultBraSize: "US",
      },
      {
        name: "victoria-beckham-us.myshopify.com",
        unit_system: "imperial",
        language: "en",
      },
    ],
    config: combinedConfig.victoriaBeckham.config,
  },
  {
    name: "Rabanne",
    domains: [
      {
        name: "fashion.rabanne.com",
        unit_system: "metric",
      },
      {
        name: "rabanne.com",
        unit_system: "metric",
      },
      {
        name: "stag-fashion.rabanne.com",
        unit_system: "metric",
      },
      {
        name: "stag-fashion.rabanne.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.rabanne.config,
  },
  {
    name: "Weill",
    domains: [
      {
        name: "www.weill.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.weill.config,
  },
  {
    name: "Sud Express",
    domains: [
      {
        name: "www.sudexpress.fr",
        unit_system: "metric",
      },
      {
        name: "sudexpress.com",
        unit_system: "metric",
      },
      {
        name: "www.sudexpress.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.sudExpress.config,
  },
  {
    name: "Loom",
    domains: [
      {
        name: "www.loom.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.loom.config,
  },
  {
    name: "Alme",
    domains: [
      {
        name: "www.almeparis.com",
        unit_system: "metric",
        language: "fr",
      },
      {
        name: "de.almeparis.com",
        unit_system: "metric",
        language: "de",
      },
      {
        name: "it.almeparis.com",
        unit_system: "metric",
        language: "it",
      },
      {
        name: "gb.almeparis.com",
        unit_system: "imperial",
        language: "en",
      },
      {
        name: "es.almeparis.com",
        unit_system: "metric",
        language: "es",
      },
      {
        name: "pt.almeparis.com",
        unit_system: "metric",
        language: "pt",
      },
      {
        name: "nl.almeparis.com",
        unit_system: "metric",
        language: "nl",
      },
      {
        name: "se.almeparis.com",
        unit_system: "metric",
        language: "se",
      },
      {
        name: "ch.almeparis.com",
        unit_system: "metric",
        language: "ch",
      },
      {
        name: "fi.almeparis.com",
        unit_system: "metric",
        language: "fi",
      },
      {
        name: "dk.almeparis.com",
        unit_system: "metric",
        language: "dk",
      },
      {
        name: "pl.almeparis.com",
        unit_system: "metric",
        language: "pl",
      },
      {
        name: "at.almeparis.com",
        unit_system: "metric",
        language: "at",
      },
    ],
    config: combinedConfig.alme.config,
  },
  {
    name: "Sport 2000",
    domains: [
      {
        name: "www.sport2000.fr",
        unit_system: "metric",
      },
      {
        name: "sport2000.fr",
        unit_system: "metric",
      },
      {
        name: "ecom.sport2000.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.sport2000.config,
  },
  {
    name: "Loro Piana",
    domains: [
      {
        name: "fr.loropiana.com",
        unit_system: "metric",
      },
      {
        name: "demo.kleep.ai",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.loropiana.config,
  },
  {
    name: "Givenchy",
    domains: [
      {
        name: "www.givenchy.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.givenchy.config,
  },
  {
    name: "Ogier",
    domains: [
      {
        name: "ogier-og.com",
        unit_system: "metric",
      },
      {
        name: "ogier1948.com",
        unit_system: "metric",
      },
      {
        name: "ogierstore.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.ogier.config,
  },
  {
    name: "Loulou de Saison",
    domains: [
      {
        name: "www.louloudesaison.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.louloudeSaison.config,
  },
  {
    name: "Eric Bompard",
    domains: [
      {
        name: "www.eric-bompard.com",
        unit_system: "metric",
      },
      {
        name: "ericbompard-development.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.ericBompard.config,
  },
  {
    name: "Intersport",
    domains: [
      {
        name: "www.intersport.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.intersport.config,
  },
  {
    name: "Blackstore",
    domains: [
      {
        name: "www.blackstore.fr",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.blackstore.config,
  },
  {
    name: "Vanessa WU",
    domains: [
      {
        name: "vanessawu.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.vanessawu.config,
  },
  {
    name: "Bonpoint",
    domains: [
      {
        name: "www.bonpoint.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.bonpoint.config,
  },
  {
    name: "Paprika",
    domains: [
      {
        name: "www.paprika.fr",
        unit_system: "metric",
        defaultBraSize: "FR",
      },
      {
        name: "www.paprika-shopping.be",
        unit_system: "metric",
        defaultBraSize: "EU",
      },
      {
        name: "www.paprika-shopping.de",
        unit_system: "metric",
        defaultBraSize: "EU",
      },
      {
        name: "www.paprika-shopping.nl",
        unit_system: "metric",
        defaultBraSize: "EU",
      },
      {
        name: "cassispaprika-dev.myshopify.com",
        unit_system: "metric",
        defaultBraSize: "EU",
      },
      {
        name: "cassispaprika-prod.myshopify.com",
        unit_system: "metric",
        defaultBraSize: "EU",
      },
    ],
    config: combinedConfig.paprika.config,
  },
  {
    name: "Fedeli Cashmere",
    domains: [
      {
        name: "www.fedelicashmere.com",
        unit_system: "metric",
      },
      {
        name: "fedelicashmere.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.fedelicashmere.config,
  },
  {
    name: "Pyrenex",
    domains: [
      {
        name: "pyrenex.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.pyrenex.config,
  },
  {
    name: "Club Brugge",
    domains: [
      {
        name: "shop.clubbrugge.be",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.brugge.config,
  },
  {
    name: "LeCollet",
    domains: [
      {
        name: "lecollet.pl",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.lecollet.config,
  },
  {
    name: "Velour Garments",
    domains: [
      {
        name: "velourgarments.eu",
        unit_system: "metric",
      },
      {
        name: "velour-garments.myshopify.com",
        unit_system: "metric",
      },
    ],
    config: combinedConfig.velourGarments.config,
  },
];
