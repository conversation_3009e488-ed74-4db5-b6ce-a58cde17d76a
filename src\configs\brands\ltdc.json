{"name": "LTDC", "config": {"ux": {"gender": "", "route": "default", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "14px", "fontColor": "#1F2F70", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#767676", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "700", "fontSize": "14px", "fontColor": "#1F2F70", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#1F2F70", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #1F2F70"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#1F2F70", "borderRadius": "0", "borderColor": "#1F2F70", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#1F2F70", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "fontSize": "14px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#1F2F70", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#1F2F70", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#1F2F70"}}, "3": {"qrcode": {"backgroundColor": "#1F2F70"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "70px", "fontColor": "#1F2F70", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "16px", "fontColor": "#1F2F70", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1F2F70", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#767676", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#1F2F70", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#1F2F70", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1F2F70", "textTransform": "capitalize", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Work Sans", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#1F2F70", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#767676", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#E3E7F7", "fontWeight": "400", "fontSize": "14px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#1F2F70", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#1F2F70", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#1F2F70", "borderRadius": "0", "fontSize": "10px", "fontWeight": "400", "fontColor": "#1F2F70", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#1F2F70", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1F2F70", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#1F2F70", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1F2F70", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#1F2F70"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#767676", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1F2F70", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#767676", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#1F2F70", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#767676", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#767676", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#1F2F70", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#1F2F70", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#1F2F70", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}