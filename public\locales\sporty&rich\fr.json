{"components/results/result": {"mark1": "<PERSON><PERSON><PERSON>", "mark2": "Légèrement étroit", "size": {"title": "<PERSON><PERSON>", "size1": "Plus ajustée", "size2": "<PERSON><PERSON><PERSON><PERSON>", "size3": "Plus ample", "variations": {"size1": "Regular", "size2": "Oversize", "size3": "<PERSON><PERSON>", "size4": "<PERSON><PERSON><PERSON>", "size5": "Légèrement ajusté", "size6": "<PERSON><PERSON><PERSON>", "size7": "Volontairement ample", "size8": "Légèrement ample", "size9": "Ample", "size10": "<PERSON><PERSON><PERSON>"}}, "description": {"normal": {"ideal": "La plupart des clients ayant la même morphologie que vous ont acheté la taille [S] de cet article et ont été satisfaits.", "not_possible_up": "Votre taille idéale n’a pas pu être identifiée pour ce modèle : d’après votre morphologie, la taille [S] sera trop grande.", "not_possible_down": "Votre taille idéale n’a pas pu être identifiée pour ce modèle : d’après votre morphologie, la taille [S] sera trop petite.", "not_possible_secondary": "Nous vous recommandons de vous rendre en boutique pour bénéficier d’un accompagnement sur mesure."}, "size_up": {"ideal": "Pour un effet plus ample, choisissez la taille [S].", "not_possible": "D’après votre morphologie, la taille [S] sera trop grande. Nous vous conseillons de choisir une taille inférieure."}, "size_down": {"ideal": "Pour un effet plus ajusté, choisissez la taille [S].", "not_possible": "D’après votre morphologie, la taille [S] sera trop petite. Nous vous conseillons de choisir une taille supérieure."}}, "result": {"title": "Coupe Regular", "description": "Équilibre parfait entre style et confort, pour un look sportif et décontracté."}, "unavailable": {"description": "Malheureusement, la taille sélectionnée est en rupture de stock.", "title": "Produits similaires disponibles dans cette taille :"}, "button": "Ajouter la taille [S] au panier", "skip": "Recommencer"}}