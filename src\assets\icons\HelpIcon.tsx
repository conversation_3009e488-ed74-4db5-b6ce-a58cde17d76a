import { SvgIcon, SvgIconProps } from "@mui/material";

const HelpIcon = (props: SvgIconProps) => (
  <SvgIcon
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <mask
      id="mask0_4095_19406"
      style={{ maskType: "alpha" }}
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="32"
      height="32"
    >
      <rect width="32" height="32" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_4095_19406)">
      <path
        d="M15.9334 24.0003C16.4001 24.0003 16.7945 23.8392 17.1167 23.517C17.439 23.1948 17.6001 22.8003 17.6001 22.3337C17.6001 21.867 17.439 21.4725 17.1167 21.1503C16.7945 20.8281 16.4001 20.667 15.9334 20.667C15.4667 20.667 15.0723 20.8281 14.7501 21.1503C14.4279 21.4725 14.2667 21.867 14.2667 22.3337C14.2667 22.8003 14.4279 23.1948 14.7501 23.517C15.0723 23.8392 15.4667 24.0003 15.9334 24.0003ZM14.7334 18.867H17.2001C17.2001 18.1337 17.2834 17.5559 17.4501 17.1337C17.6167 16.7114 18.089 16.1337 18.8667 15.4003C19.4445 14.8225 19.9001 14.2725 20.2334 13.7503C20.5667 13.2281 20.7334 12.6003 20.7334 11.867C20.7334 10.6225 20.2779 9.66699 19.3667 9.00033C18.4556 8.33366 17.3779 8.00033 16.1334 8.00033C14.8667 8.00033 13.839 8.33366 13.0501 9.00033C12.2612 9.66699 11.7112 10.467 11.4001 11.4003L13.6001 12.267C13.7112 11.867 13.9612 11.4337 14.3501 10.967C14.739 10.5003 15.3334 10.267 16.1334 10.267C16.8445 10.267 17.3779 10.4614 17.7334 10.8503C18.089 11.2392 18.2667 11.667 18.2667 12.1337C18.2667 12.5781 18.1334 12.9948 17.8667 13.3837C17.6001 13.7725 17.2667 14.1337 16.8667 14.467C15.889 15.3337 15.289 15.9892 15.0667 16.4337C14.8445 16.8781 14.7334 17.6892 14.7334 18.867ZM16.0001 29.3337C14.1556 29.3337 12.4223 28.9837 10.8001 28.2837C9.17786 27.5837 7.76675 26.6337 6.56675 25.4337C5.36675 24.2337 4.41675 22.8225 3.71675 21.2003C3.01675 19.5781 2.66675 17.8448 2.66675 16.0003C2.66675 14.1559 3.01675 12.4225 3.71675 10.8003C4.41675 9.1781 5.36675 7.76699 6.56675 6.56699C7.76675 5.36699 9.17786 4.41699 10.8001 3.71699C12.4223 3.01699 14.1556 2.66699 16.0001 2.66699C17.8445 2.66699 19.5779 3.01699 21.2001 3.71699C22.8223 4.41699 24.2334 5.36699 25.4334 6.56699C26.6334 7.76699 27.5834 9.1781 28.2834 10.8003C28.9834 12.4225 29.3334 14.1559 29.3334 16.0003C29.3334 17.8448 28.9834 19.5781 28.2834 21.2003C27.5834 22.8225 26.6334 24.2337 25.4334 25.4337C24.2334 26.6337 22.8223 27.5837 21.2001 28.2837C19.5779 28.9837 17.8445 29.3337 16.0001 29.3337ZM16.0001 26.667C18.9779 26.667 21.5001 25.6337 23.5667 23.567C25.6334 21.5003 26.6667 18.9781 26.6667 16.0003C26.6667 13.0225 25.6334 10.5003 23.5667 8.43366C21.5001 6.36699 18.9779 5.33366 16.0001 5.33366C13.0223 5.33366 10.5001 6.36699 8.43341 8.43366C6.36675 10.5003 5.33341 13.0225 5.33341 16.0003C5.33341 18.9781 6.36675 21.5003 8.43341 23.567C10.5001 25.6337 13.0223 26.667 16.0001 26.667Z"
        fill={props?.fill || "#002D18"}
      />
    </g>
  </SvgIcon>
);

export default HelpIcon;
