interface GenderButtonProps {
  gender: string;
  selectedGender: string | null;
  baseStyles: React.CSSProperties;
  hovered: string | null;
  setHovered: (val: string | null) => void;
  brandName?: string;
  label: string;
  onSelect: () => void;
}

const GenderButton: React.FC<GenderButtonProps> = ({
  gender,
  selectedGender,
  baseStyles,
  hovered,
  setHovered,
  brandName,
  label,
  onSelect,
}) => {
  const isSelected = selectedGender === gender;
  const isHovered = hovered === gender;
  const isIntersport = brandName === "Intersport";

  const styles: React.CSSProperties = {
    ...baseStyles,
    textTransform:
      baseStyles.textTransform === "capitalize"
        ? "none"
        : baseStyles.textTransform,
    borderColor: selectedGender
      ? baseStyles.borderColor
      : !selectedGender && isHovered && isIntersport
      ? "#A1A1A1"
      : baseStyles.borderColor,
  };

  return (
    <button
      type="button"
      className={`gender-btn ${isSelected ? "selected" : ""}`}
      style={styles}
      onClick={onSelect}
      onMouseEnter={() => setHovered(gender)}
      onMouseLeave={() => setHovered(null)}
    >
      {label}
    </button>
  );
};

export default GenderButton;
