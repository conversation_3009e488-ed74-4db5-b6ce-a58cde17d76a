{"name": "SRP", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#272626", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#767474", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#272626", "textAlign": "left"}, "borderRadius": "8px", "mobileBorderRadius": "8px", "borderColor": "#272626", "marginBottom": "10px", "padding": "10px", "borderBottom": "1px solid #272626"}}, "genderCTA": {"unfocused": {"backgroundColor": "transparent", "fontColor": "#272626", "borderRadius": "4px", "borderColor": "#272626", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "600", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#272626", "fontColor": "#FFFFFF", "borderRadius": "4px", "borderColor": "#272626", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "600", "textTransform": "capitalize"}}, "units": {"fontWeight": "600", "fontSize": "14px", "top": "-30px", "right": "", "activeColor": "#272626", "inactiveColor": "#767474", "activeFontStyle": "underline"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#272626"}}, "3": {"qrcode": {"backgroundColor": "#272626"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "46px", "fontColor": "#272626", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "11px", "fontColor": "#4B7C34", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "transparent", "fontWeight": "600", "fontSize": "11px", "fontColor": "#272626", "textTransform": "capitalize", "borderRadius": "5px", "borderColor": "#A7A7A7", "borderWidth": "1px"}, "focused": {"backgroundColor": "transparent", "fontWeight": "600", "fontSize": "11px", "fontColor": "#4B7C34", "textTransform": "capitalize", "borderRadius": "5px", "borderColor": "#4B7C34", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "11px", "fontColor": "rgba(133, 100, 0, 1)", "textTransform": "capitalize", "borderRadius": "5px", "borderColor": "rgba(133, 100, 0, 0.32)", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#272626", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#272626", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "12px", "fontColor": "#272626", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "600", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Montserrat", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#272626", "fontSize": "18px "}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#272626", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#272626", "backgroundOpacity": "8%", "fontWeight": "600", "fontSize": "14px", "fontColor": "#272626", "fontOpacity": "32%", "textTransform": "capitalize", "borderRadius": "5px", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#272626", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "capitalize", "borderRadius": "5px", "borderColor": "#272626", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#272626", "borderRadius": "4px", "fontSize": "12px", "fontWeight": "600", "fontColor": "#272626", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "12px", "fontColor": "#272626", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#272626", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "12px", "fontColor": "#272626", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#272626", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#272626", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#272626", "fontSize": "12px", "fontWeight": "600", "borderRadius": "5px", "borderColor": "#272626", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "600", "borderRadius": "5px", "borderColor": "#272626", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#272626", "fontWeight": "400", "fontStyle": "underline", "fontSize": "14px"}}}}}