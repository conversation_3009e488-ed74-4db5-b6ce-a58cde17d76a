{"name": "Stella & Suzie", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "16px", "fontColor": "#222222", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "12px", "fontColor": "#545454", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#222222", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#222222", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #222222"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#222222", "borderRadius": "0", "borderColor": "#222222", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#222222", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#222222", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "6px", "right": "-11px", "activeColor": "#222222", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#222222"}}, "3": {"qrcode": {"backgroundColor": "#3d4557"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#222222", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#222222", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#222222", "borderWidth": "2px"}, "focused": {"backgroundColor": "#222222", "fontWeight": "400", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#222222", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Fira Sans", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#222222", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#222222", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#c3c3c3", "fontWeight": "700", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#222222", "fontWeight": "700", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#222222", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#222222", "borderRadius": "0", "fontSize": "12px", "fontWeight": "400", "fontColor": "#222222", "textTransform": "capitalize", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#222222", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#222222", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#222222", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#222222", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#222222"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#222222", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#222222", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#222222", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#222222", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#222222", "fontSize": "12px", "fontWeight": "400", "borderRadius": "5px", "borderColor": "#c3c3c3", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#222222", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "5px", "borderColor": "#222222", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#222222", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}