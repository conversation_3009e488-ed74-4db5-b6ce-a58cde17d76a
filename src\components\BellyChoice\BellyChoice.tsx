import { FC, useState } from "react";
import <PERSON><PERSON> from "lottie-react";

import { morphoUIStyles, font } from "../../configs";
import {
  capitalizeFirstLetter,
  handleAdditionalBrands,
  useIsMobile,
} from "../../utils";
import { GENDERS } from "../../constants/modal";
import { findBrandByDomain } from "../../configs/configLoader";

import "./index.css";

interface IPropsBellyChoice {
  image: string;
  onClick: () => void;
  text: string;
  isSelected?: boolean;
  type?: string;
  gender?: string;
}

type BellyChoiceType = "unfocused" | "focused";

const BellyChoice: FC<IPropsBellyChoice> = ({
  image,
  onClick,
  text,
  isSelected,
  type,
  gender,
}) => {
  const [loading, setLoading] = useState(true);

  const isMobile = useIsMobile();

  const urlParams = new URLSearchParams(window.location.search);
  const lang = urlParams.get("lang")?.split("-")[0].toLowerCase();
  const brandDefined = findBrandByDomain();

  const torsoClass = loading
    ? "torso_choice__image_loading"
    : "torso_choice__image";

  const cuissesClass = loading
    ? "cuisses_choice__image_loading"
    : "cuisses_choice__image";

  const getSelectorStyles = (selectorType: BellyChoiceType) => {
    const ctaStyles = morphoUIStyles.morphoCTA[selectorType]!;

    const selectorSizeStyles = {
      backgroundColor:
        ctaStyles.backgroundColor as React.CSSProperties["backgroundColor"],
      borderRadius:
        ctaStyles.borderRadius as React.CSSProperties["borderRadius"],
      border: `${
        ctaStyles.borderWidth as React.CSSProperties["borderWidth"]
      } solid ${ctaStyles.borderColor as React.CSSProperties["borderColor"]}`,
      fontWeight: ctaStyles.fontWeight as React.CSSProperties["fontWeight"],
      color: ctaStyles.fontColor as React.CSSProperties["color"],
      fontSize:
        (isMobile &&
          (lang === "de" || lang === "en") &&
          type === "torso" &&
          gender === GENDERS.F) ||
        (isMobile && brandDefined?.name === "The Kooples")
          ? `calc(${ctaStyles.fontSize} - 2px)`
          : (ctaStyles.fontSize as React.CSSProperties["fontSize"]),
      textAlign: ctaStyles.textAlign as React.CSSProperties["textAlign"],
      textTransform:
        ctaStyles.textTransform as React.CSSProperties["textTransform"],
      borderWidthStyle: ctaStyles.borderWidth,
      letterSpacing: (ctaStyles as any)?.letterSpacing,
    };

    return selectorSizeStyles;
  };

  const selectorStyles = isSelected
    ? getSelectorStyles("focused")
    : getSelectorStyles("unfocused");

  const isAdditionalBrands = handleAdditionalBrands(brandDefined?.name);

  const getTextTransform = (textTransform?: string) => {
    return textTransform === "capitalize" ? "none" : textTransform;
  };

  const getGap = (loading?: boolean) => {
    return loading ? 0 : "15px";
  };

  const getBorder = (
    isAdditionalBrands?: boolean,
    brandName?: string,
    fallbackBorder?: string
  ) => {
    if (isAdditionalBrands && brandName === "Victoria Beckham") return "none";
    return fallbackBorder;
  };

  const getHeight = (isAdditionalBrands?: boolean, brandName?: string) => {
    if (isAdditionalBrands && brandName === "Victoria Beckham") return "auto";
    if (isAdditionalBrands && brandName !== "Victoria Beckham") return "240px";
    return "220px";
  };

  return (
    <button
      className={isSelected ? "belly_choice_selected" : "belly_choice"}
      onClick={onClick}
      style={{
        ...selectorStyles,
        fontFamily: `${font}, sans-serif !important`,
        textTransform: getTextTransform(
          selectorStyles?.textTransform
        ) as React.CSSProperties["textTransform"],
        gap: getGap(loading),
        border: getBorder(
          isAdditionalBrands,
          brandDefined?.name,
          selectorStyles.border
        ),
        height: getHeight(isAdditionalBrands, brandDefined?.name),
      }}
    >
      {loading === true ? (
        <Lottie
          className="loading"
          animationData={require("../../assets/animations/loading_image.json")}
          width={90}
          height={120}
        />
      ) : null}
      <div className="belly-data-container">
        <img
          className={
            type === "torso"
              ? torsoClass
              : type === "cuisses"
              ? cuissesClass
              : loading
              ? "belly_choice__image_loading"
              : "belly_choice__image"
          }
          style={{
            width: isAdditionalBrands ? "110px" : "90px",
            borderRadius: isAdditionalBrands
              ? "0"
              : selectorStyles.borderRadius,
            height: isAdditionalBrands ? "auto" : "120px",
            transform:
              brandDefined?.name === "Ogier"
                ? isMobile
                  ? "scale(2)"
                  : "scale(1.4)"
                : "scale(1)",
            transformOrigin: "center center",
          }}
          alt={type}
          src={image}
          onLoad={() => setLoading(false)}
        />

        <span
          style={{
            ...selectorStyles,
            border: "none",
            fontFamily: `${font}, sans-serif !important`,
            textTransform:
              selectorStyles?.textTransform === "capitalize"
                ? "none"
                : selectorStyles?.textTransform,
            backgroundColor: "transparent",
            position: "relative",
            top: brandDefined?.name === "Ogier" && isMobile ? "20px" : 0,
          }}
        >
          {selectorStyles?.textTransform === "capitalize"
            ? capitalizeFirstLetter(text)
            : text}
        </span>
      </div>
    </button>
  );
};

export default BellyChoice;
