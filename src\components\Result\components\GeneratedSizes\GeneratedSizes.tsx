import { FC } from "react";
import { useIsMobile } from "../../../../utils";

import SizeSelector from "../SizeSelector";

import { Check as CheckIcon } from "@mui/icons-material";

import { findBrandByDomain } from "../../../../configs/configLoader";
import { useTranslation } from "react-i18next";
import { SizeDataType } from "../../../../types/result";

import "./index.css";

interface IPropsGeneratedSizes {
  selectedSizeImage: string;
  feedbacks: any;
  selectedVariant: string;
  fit_feedbacks: string[];
  selectedSize: SizeDataType | null;
}

const GeneratedSizes: FC<IPropsGeneratedSizes> = ({
  selectedSizeImage,
  feedbacks,
  selectedVariant,
  fit_feedbacks,
  selectedSize,
}) => {
  const isMobile = useIsMobile();

  const { t } = useTranslation("components/results/result");

  const brandDefined = findBrandByDomain();

  const getActualDescription = () => {
    if (selectedVariant) {
      switch (selectedVariant) {
        case "0":
          return selectedSize?.possible === 0
            ? "description.size_down.not_possible"
            : "description.size_down.ideal";
        case "1":
          return "description.normal.ideal";
        case "2":
          return selectedSize?.possible === 0
            ? "description.size_up.not_possible"
            : "description.size_up.ideal";
        default:
          return "description.normal.ideal";
      }
    } else {
      return "description.normal.ideal";
    }
  };

  return (
    <div
      className="result-body"
      style={{
        height: isMobile ? "180px" : !isMobile ? "242px" : "auto",
        border: "1px #E9E9E9 solid",
      }}
    >
      <div className="result">
        <SizeSelector
          selectedSizeImage={selectedSizeImage}
          feedbacks={feedbacks}
        />
      </div>
      <div
        className="text"
        style={{
          display: "flex",
          flexDirection: "column",
          width: !isMobile && brandDefined?.name === "Lacoste" ? "80%" : "100%",
        }}
      >
        <div className="result-title">
          <CheckIcon />
          <p>
            {selectedVariant
              ? fit_feedbacks[parseInt(selectedVariant, 10)]
              : fit_feedbacks[1]}
          </p>
        </div>
        <p
          className="result-description"
          style={{
            paddingLeft:
              !isMobile && brandDefined?.name === "Lacoste" ? "0" : "20px",
            paddingRight:
              isMobile && brandDefined?.name === "Lacoste" ? "5px" : "20px",
          }}
          dangerouslySetInnerHTML={{
            __html: t(getActualDescription()).replace(
              "[S]",
              `<b>${selectedSize?.label || ""}</b>`
            ),
          }}
        />
      </div>
    </div>
  );
};

export default GeneratedSizes;
