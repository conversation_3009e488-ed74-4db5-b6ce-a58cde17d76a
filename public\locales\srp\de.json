{"components/intro": {"title": "Finden Sie Ihre ideale Größe für jedes unserer Produkte", "description": "Ein paar Fragen, um <PERSON> besser kennenzulernen.", "topTitle": "Größenratgeber", "steps": {"consent": "KENZO X KLEEP", "gender": "Finden Sie Ihre ideale Größe für jedes unserer Produkte", "intro_mobile": "Finden Sie Ihre ideale Größe für jedes unserer Produkte", "intro": "Finden Sie Ihre ideale Größe für jedes unserer Produkte", "device_select_shoe": "Finde deine ideale Schuhgröße", "shoe_gender": "Bitte gib dein Geschlecht an", "shoe_sizes": "<PERSON><PERSON> Schuhgröße trägst du normalerweise?", "shoe_questions": "Wie breit sind deine Füße?", "result_shoe": "Deine ideale Schuhgröße", "device_select": "Finden Sie Ihre ideale Größe für jedes unserer Produkte", "qr_code": "Finden Sie Ihre ideale Größe für jedes unserer Produkte", "qr_code_shoe": "<PERSON><PERSON><PERSON> und präziser Scan", "lingerie_question_1": "Geben Sie an, welchen Haken Sie am häufigsten verwenden", "lingerie_question_2": "Geben Sie Ihr Empfinden bezüglich des Unterbrustbands an", "lingerie_question_3": "Geben Sie Ihr Empfinden bezüglich der Cups an", "lingerie_question_4": "Geben Sie Ihr Empfinden bezüglich der Träger an", "belly": "Wählen Sie Die Form Ihres Bauchs", "torso": {"male": "Wählen Sie Die Form Ihres Oberkörpers", "female": "Wählen Sie die Form Ihrer Hüfte aus"}, "cuisses": "Wählen Sie Die Form Ihrer Oberschenkel", "breasts": "Wählen Sie Ihre Brustgrösse", "error": "<PERSON><PERSON><PERSON>, es ist ein Fehler aufgetreten", "error_outOfRange": "Ihre Idealgröße konnte für dieses Kleidungsstück nicht ermittelt werden.", "result": "<PERSON><PERSON><PERSON><PERSON>", "unavailable": "Ihre empfohlene grösse ist nicht mehr verfügbar", "antibracketing": "Unsicher Bei Der Grösse?"}, "gender": {"title": "<PERSON><PERSON> sind", "description": "Das hilft uns, deine Größe besser zu verstehen", "male": "<PERSON>", "female": "<PERSON><PERSON>", "children": {"male": "<PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON><PERSON>"}}, "height": {"title": "Größe*", "mobiletitle": "Größe", "variation": "GRÖSSE*", "unit": "cm", "errorcm": "Bitte geben Sie Ihre Größe an.", "errorfeet": "Bitte geben Sie Ihre Größe an.", "placeholder": "z.B.: 160", "placeholderfeet": "z.B.: 5'", "placeholderinches": "z.B.: 9\"", "children": {"placeholder": "z.B.: 105", "placeholderfeet": "z.B.: 3'", "placeholderinches": "z.B.: 4\""}}, "weight": {"title": "Gewicht*", "variation": "GEWICHT*", "unit": "kg", "errorkg": "Bitte geben Sie Ihr Gewicht an.", "errorlbs": "Bitte geben Sie Ihr Gewicht an.", "placeholderKG": "z.B.: 60", "placeholderLBS": "z.B.: 154", "children": {"placeholderKG": "z.B.: 16", "placeholderLBS": "z.B.: 35"}}, "age": {"title": "Alter*", "variation": "Alter*", "unit": "ans", "error": "Bitte geben Sie Ihr Alter an.", "placeholder": "z.B.: 30 Jahre", "placeholdermonths": "z.B.: 12", "children": {"placeholder": "z.B.: 4 Jahre", "year": "Jahre", "month": "<PERSON><PERSON>"}}, "continue": "WEITER", "pp_part1": "Indem Sie fortfahren, stimmen Sie dem zu", "pp_link": "<PERSON><PERSON> <PERSON> fort<PERSON>, stimmen <PERSON> unser<PERSON> <a href=\"https://kleep.ai/privacy-policy\">Datenschutzrichtlinie</a> zu."}, "components/results/result": {"mark1": "<PERSON><PERSON> eng", "mark2": "Perfekte Passform", "size": {"title": "Ideale Größe", "size1": "<PERSON><PERSON><PERSON><PERSON>", "size2": "Ideal", "size3": "<PERSON><PERSON><PERSON>", "variations": {"size1": "Regular", "size2": "Oversize", "size3": "Perfekte Passform", "size4": "<PERSON><PERSON> eng", "size5": "Le<PERSON>t eng", "size6": "<PERSON><PERSON> eng", "size7": "<PERSON><PERSON><PERSON><PERSON><PERSON> weit", "size8": "<PERSON><PERSON><PERSON> locker", "size9": "<PERSON><PERSON>", "size10": "<PERSON>hr weit"}}, "description": {"shoe": {"normal": {"ideal": "Kunden mit derselben Fußlänge und -breite haben die Größe [S] gekauft und waren zufrieden.", "not_possible": ""}, "size_up": {"ideal": "Wähle die Größe [S] für mehr Komfort.", "not_possible": "Basierend auf der Morphologie deines Fußes könnte die Größe [S] zu groß für dich sein."}, "size_down": {"ideal": "Wähle die Größe [S] für mehr Halt.", "not_possible": "Basierend auf der Morphologie deines Fußes könnte die Größe [S] zu eng für dich sein."}}, "normal": {"ideal": "Kunden mit einer ähnlichen Körperform wie Ihrer haben Größe [S] dieses Artikels gekauft und waren zufrieden.", "not_possible_up": "Ihre Idealgröße konnte nicht ermittelt werden: basierend auf Ihrer Körperform wird die Größe [S] zu groß sein.", "not_possible_down": "Ihre Idealgröße konnte nicht ermittelt werden: basierend auf Ihrer Körperform wird die Größe [S] zu klein sein.", "not_possible_secondary": "Wir empfehlen <PERSON>hnen, eine Boutique zu besuchen, um eine maßgeschneiderte Begleitung zu erhalten."}, "size_up": {"ideal": "<PERSON><PERSON><PERSON> <PERSON> <b>we<PERSON><PERSON> Sitz</b> w<PERSON><PERSON><PERSON> <b><PERSON><PERSON><PERSON><PERSON> [S]</b>.", "not_possible": "Basierend auf Ihrer Körperform wird die <b><PERSON><PERSON><PERSON><PERSON> [S]</b> zu groß sein. Wir empfehlen <PERSON>hnen, eine kleinere Größe zu wählen."}, "size_down": {"ideal": "<PERSON><PERSON><PERSON> e<PERSON> <b>schlankere Passform</b> wähl<PERSON> Si<PERSON> <b><PERSON><PERSON><PERSON><PERSON> [S]</b>.", "not_possible": "Basierend auf Ihrer Körperform wird die <b>Gr<PERSON><PERSON> [S]</b> zu klein sein. Wir empfehlen Ihnen, eine größere Größe zu wählen."}}, "result": {"title": "Regular Fit", "description": "Perfekte Balance zwischen Stil und Komfort für einen sportlichen und lässigen Look."}, "unavailable": {"description": "Le<PERSON> ist die ausgewählte Größe nicht auf Lager.", "title": "Intenta de nuevo :"}, "button": "Grösse [S] Zum Warenkorb Hinzufügen", "skip": "<PERSON><PERSON><PERSON> beginnen"}}