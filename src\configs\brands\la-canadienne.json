{"name": "La Canadienne", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "8px", "borderColor": "#2E2E2E", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #2E2E2E"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#2E2E2E", "borderRadius": "4px", "borderColor": "#2E2E2E", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#2E2E2E", "fontColor": "#FFFFFF", "borderRadius": "4px", "borderColor": "#2E2E2E", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "8px", "right": "-11px", "activeColor": "#2E2E2E", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#2E2E2E"}}, "3": {"qrcode": {"backgroundColor": "#2E2E2E"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#2E2E2E", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#2E2E2E", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#E9E9E9", "borderWidth": "2px"}, "focused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textTransform": "none", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#B9B9B9", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Futura", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "none", "color": "#2E2E2E", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "none", "color": "#2E2E2E", "fontSize": "13px"}, "cta": {"unfocused": {"backgroundColor": "#DDDDDD", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "unset", "borderRadius": "4px", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#2E2E2E", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "unset", "borderRadius": "4px", "borderColor": "#2E2E2E", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#2E2E2E", "borderRadius": "4px", "fontSize": "12px", "fontWeight": "600", "fontColor": "#2E2E2E", "textTransform": "unset", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "12px", "fontWeight": "regular", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#2E2E2E", "borderWidth": "2px", "borderRadius": "12px", "fontWeight": "regular", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#2E2E2E"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#2E2E2E", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#2E2E2E", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#737373", "fontSize": "12px", "fontWeight": "600", "borderRadius": "0", "borderColor": "#E9E9E9", "borderWidth": "2px", "textTransform": "unset"}, "focused": {"backgroundColor": "#2E2E2E", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "600", "borderRadius": "0", "borderColor": "#2E2E2E", "borderWidth": "2px", "textTransform": "unset"}}, "skip": {"fontColor": "#2E2E2E", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}