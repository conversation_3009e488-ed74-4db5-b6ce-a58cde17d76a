import { FC } from "react";
import {
  getActualDescriptionApparel,
  lowerCaseFirstLetter,
  useIsMobile,
} from "../../../../utils";

import InfoIcon from "../../../../assets/icons/InfoIcon";

import { findBrandByDomain } from "../../../../configs/configLoader";
import { useTranslation } from "react-i18next";
import { SizeDataType } from "../../../../types/result";

import { resultScreenUI } from "../../../../configs";
import { useMediaQuery } from "react-responsive";

import "./index.css";

interface IPropsDescriptionResults {
  isSizeUnavailable: boolean;
  selectedVariant: string;
  selectedSize: SizeDataType | null;
  similarProducts: any;
  isSimilarProductsLoaded: boolean;
}

const DescriptionResults: FC<IPropsDescriptionResults> = ({
  isSizeUnavailable,
  selectedVariant,
  selectedSize,
  similarProducts,
  isSimilarProductsLoaded,
}) => {
  const isMobile = useIsMobile();

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: !isMobile ? 830 : 778,
    minWidth: 767,
  });

  const { t } = useTranslation("components/results/result");

  const brandDefined = findBrandByDomain();

  const isSRPUnavailable = brandDefined?.name === "SRP" && isSizeUnavailable;

  const isDesktopGerardDarel =
    brandDefined?.name === "Gerard Darel" && !isMobile;

  const urlParameters = new URLSearchParams(window.location.search);
  const isLingerie = urlParameters.get("lingerie") === "true";

  return brandDefined?.name === "Lacoste" && isLingerie ? null : (
    <div
      className="result-body-text"
      style={{
        borderBottom:
          brandDefined?.name === "SRP"
            ? "none"
            : isSizeUnavailable && similarProducts?.length
            ? "1px solid #E9E9E9"
            : "none",
        justifyContent: resultScreenUI.Description
          .textAlign as React.CSSProperties["justifyContent"],
        backgroundColor: isSRPUnavailable ? "#FDF4D5" : "transparent",
        paddingBottom: isDesktopGerardDarel
          ? "10px"
          : isSRPUnavailable
          ? "16px"
          : (isLargeHeightRelative &&
              isSizeUnavailable &&
              similarProducts?.length) ||
            (isMobile && isSizeUnavailable)
          ? "5px"
          : !isLargeHeightRelative &&
            isSizeUnavailable &&
            similarProducts?.length
          ? "25px"
          : 0,
        paddingTop: isSRPUnavailable ? "16px" : "0",
        paddingLeft: isSRPUnavailable ? "16px" : "0",
        paddingRight: isSRPUnavailable ? "16px" : "0",
        margin: brandDefined?.name === "SRP" ? "32px 0" : "15px 0",
        borderRadius: isSRPUnavailable ? "4px" : 0,
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          gap: "10px",
        }}
      >
        {isSizeUnavailable ? (
          <p
            style={{
              color:
                brandDefined?.name === "SRP"
                  ? "rgba(133, 100, 0, 1)"
                  : (resultScreenUI.Description
                      .fontColor as React.CSSProperties["color"]),
              fontSize:
                brandDefined?.name === "SRP"
                  ? "14px"
                  : (resultScreenUI.Description
                      .fontSize as React.CSSProperties["fontSize"]),
              fontWeight: resultScreenUI.Description
                .fontWeight as React.CSSProperties["fontWeight"],
              textAlign: resultScreenUI.Description
                .textAlign as React.CSSProperties["textAlign"],
              display: "flex",
              alignItems: "center",
              lineHeight: "20px",
            }}
          >
            {t("unavailable.description")}
          </p>
        ) : (
          <div
            style={{
              color:
                brandDefined?.name === "SRP" &&
                !similarProducts?.length &&
                isSimilarProductsLoaded &&
                isSRPUnavailable
                  ? "rgba(133, 100, 0, 1)"
                  : (resultScreenUI.Description
                      .fontColor as React.CSSProperties["color"]),
              fontSize:
                brandDefined?.name === "SRP"
                  ? "14px"
                  : (resultScreenUI.Description
                      .fontSize as React.CSSProperties["fontSize"]),
              fontWeight: resultScreenUI.Description
                .fontWeight as React.CSSProperties["fontWeight"],
              textAlign: resultScreenUI.Description
                .textAlign as React.CSSProperties["textAlign"],
              lineHeight: "20px",
              display: "flex",
              alignItems: "center",
              flexDirection: "column",
            }}
          >
            <span
              dangerouslySetInnerHTML={{
                __html:
                  brandDefined?.name === "Molli"
                    ? lowerCaseFirstLetter(
                        t(
                          getActualDescriptionApparel(
                            selectedVariant,
                            selectedSize
                          )
                        ).replace("[S]", selectedSize?.label || "")
                      )
                    : t(
                        getActualDescriptionApparel(
                          selectedVariant,
                          selectedSize
                        )
                      ).replace("[S]", selectedSize?.label || ""),
              }}
            />
          </div>
        )}
        {isSRPUnavailable ? (
          <InfoIcon
            style={{
              fontSize: "16px",
            }}
          />
        ) : null}
      </div>
    </div>
  );
};

export default DescriptionResults;
