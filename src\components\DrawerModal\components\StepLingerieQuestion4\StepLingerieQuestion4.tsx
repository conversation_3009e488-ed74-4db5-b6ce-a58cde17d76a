import React, { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { findBrandByDomain } from "../../../../configs/configLoader";
import { capitalizeFirstLetter, useIsMobile } from "../../../../utils";
import {
  activeStylesContinue,
  disabledStylesContinue,
  font,
  welcomeScreenUI,
} from "../../../../configs";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { hoverStylesContinue } from "../../../../configs/stylesLoader";

import "./index.css";

interface IPropsStepShoeQuestions {
  lingerieQuestion4: string | null;
  setLingerieQuestion4: React.Dispatch<React.SetStateAction<string | null>>;
  nextStep: () => void;
  subtitlesStyles: any;
}

type GenderButtonType = "focused" | "unfocused";

const StepLingerieQuestion4: FC<IPropsStepShoeQuestions> = ({
  lingerieQuestion4,
  setLingerieQuestion4,
  nextStep,
  subtitlesStyles,
}) => {
  const isMobile = useIsMobile();

  const { t } = useTranslation("components/lingerie-question");

  const brandDefined = findBrandByDomain();

  const [hoveredSize, setHoveredSize] = useState<string | null>(null);
  const [hovered, setHovered] = useState(false);

  const isIntersport = brandDefined?.name === "Intersport";
  const isLacoste = brandDefined?.name === "Lacoste";
  const isVB = brandDefined?.name === "Victoria Beckham";

  const SIZES = [
    { id: "very_narrow", key: "size1" },
    { id: "narrow", key: "size2" },
    { id: "standard", key: "size3" },
  ];

  const IntersportBorderColor = isIntersport ? "#A1A1A1" : "";

  const getButtonsStyles = (type: GenderButtonType) => {
    const defaultButtonsStyles = {
      width: "100%",
      backgroundColor: welcomeScreenUI.genderCTA[type].backgroundColor,
      borderRadius: welcomeScreenUI.genderCTA[type].borderRadius,
      borderColor: welcomeScreenUI.genderCTA[type].borderColor,
      borderWidth: welcomeScreenUI.genderCTA[type].borderWidth,
      fontSize: welcomeScreenUI.genderCTA[type].fontSize,
      fontWeight: welcomeScreenUI.genderCTA[type].fontWeight,
      textTransform:
        isLacoste && isMobile
          ? "uppercase"
          : (welcomeScreenUI.genderCTA[type]
              .textTransform as React.CSSProperties["textTransform"]),
      color: welcomeScreenUI.genderCTA[type].fontColor,
      fontFamily: isMobile
        ? `${font}, sans-serif`
        : `${font}, sans-serif !important`,
      letterSpacing: (welcomeScreenUI.genderCTA[type] as any)?.letterSpacing,
    };

    return defaultButtonsStyles;
  };

  const getButtonStyles = (item: string) => {
    return lingerieQuestion4 === item
      ? getButtonsStyles("focused")
      : getButtonsStyles("unfocused");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();

    if (e.key === "Tab") {
      e.preventDefault();

      if (lingerieQuestion4) {
        const heightInput = document.querySelector(
          "#height"
        ) as HTMLInputElement | null;
        if (heightInput) {
          heightInput?.focus();
        } else {
          // logger.log("Element with id 'height' not found.");
        }
      }
    }
  };

  const handleFocus = () => {
    // setSelectedGender(GENDERS.F);
  };

  const CTAStyles = {
    ...(!lingerieQuestion4 ? disabledStylesContinue : activeStylesContinue),
    ...(isIntersport && hovered ? hoverStylesContinue : {}),
    marginTop: isMobile ? "60px" : "30px",
    position: "relative" as React.CSSProperties["position"],
    marginBottom: isMobile ? "20px" : "0",
    textTransform:
      activeStylesContinue.textTransform === "capitalize"
        ? "none"
        : (activeStylesContinue.textTransform as React.CSSProperties["textTransform"]),
    padding: isVB ? "7px" : "16px",
    height: isVB ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered ? 1 : 0,
    transform: hovered ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div>
      <span
        className="description"
        style={{
          marginTop: "10px",
          ...subtitlesStyles,
          justifyContent:
            subtitlesStyles.textAlign as React.CSSProperties["justifyContent"],
          textAlign:
            subtitlesStyles.textAlign as React.CSSProperties["textAlign"],
          textTransform: (subtitlesStyles.textTransform === "capitalize"
            ? "none"
            : subtitlesStyles.textTransform) as React.CSSProperties["textTransform"],
        }}
      >
        {subtitlesStyles.textTransform === "capitalize"
          ? capitalizeFirstLetter(t("description"))
          : t("description")}
      </span>
      <div
        className="gender-buttons"
        style={{
          marginBottom: "30px",
          display: "flex",
          flexDirection: "column",
          gap: "20px",
        }}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        tabIndex={0}
      >
        {SIZES.map(({ id, key }) => {
          const styles = getButtonStyles(id);
          const textTransform =
            styles.textTransform === "capitalize"
              ? "none"
              : styles.textTransform;
          const label =
            styles.textTransform === "capitalize"
              ? capitalizeFirstLetter(t(`question3.sizes.${key}`))
              : t(`question3.sizes.${key}`);

          return (
            <button
              key={id}
              type="button"
              className={lingerieQuestion4 === id ? "selected" : ""}
              style={{
                ...styles,
                textTransform,
                borderColor:
                  hoveredSize === id && isIntersport
                    ? IntersportBorderColor
                    : styles?.borderColor,
              }}
              onClick={() => setLingerieQuestion4(id)}
              onMouseEnter={() => setHoveredSize(id)}
              onMouseLeave={() => setHoveredSize(null)}
            >
              {label}
            </button>
          );
        })}
      </div>
      <button
        type="button"
        disabled={!lingerieQuestion4}
        className={`continue-button breasts-continue`}
        onMouseEnter={(e) => {
          setHovered(true);
          if (lingerieQuestion4) {
            Object.assign(e.currentTarget.style, hoverStylesContinue);
          }
        }}
        onMouseLeave={(e) => {
          setHovered(false);
          if (lingerieQuestion4) {
            Object.assign(e.currentTarget.style, CTAStyles);
          }
        }}
        onClick={() => nextStep()}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {activeStylesContinue.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("continue", { ns: "components/intro" }))
            : t("continue", { ns: "components/intro" })}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>
    </div>
  );
};

export default StepLingerieQuestion4;
