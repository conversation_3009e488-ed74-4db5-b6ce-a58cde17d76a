{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#75140e", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#010f16", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#75140e", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#010f16", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #010f16"}}, "genderCTA": {"unfocused": {"backgroundColor": "#fffdf8", "fontColor": "#75140e", "borderRadius": "5px", "borderColor": "#75140e", "borderWidth": "1px", "fontSize": "16px", "fontWeight": "500", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#75140e", "fontColor": "#FFFFFF", "borderRadius": "5px", "borderColor": "#75140e", "borderWidth": "1px", "fontSize": "16px", "fontWeight": "500", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#010f16", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#010f16"}}, "3": {"qrcode": {"backgroundColor": "#010f16"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#75140e", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#75140e", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "16px", "fontColor": "#010F16", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#010F16", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "16px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#44883F", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "500", "fontSize": "16px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#010F16", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#010F16", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#010F16", "textTransform": "uppercase", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "<PERSON><PERSON><PERSON>", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#75140e", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#75140e", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#fffdf8", "fontWeight": "500", "fontSize": "16px", "fontColor": "#75140e", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "#75140e", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#75140e", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "16px", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "#75140e", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#75140e", "borderRadius": "5px", "fontSize": "12px", "fontWeight": "500", "fontColor": "#75140e", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#75140e", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "500", "fontSize": "16px", "fontColor": "#75140e", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#75140e", "borderWidth": "2px", "borderRadius": "5px", "fontWeight": "500", "fontSize": "16px", "fontColor": "#75140e", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#010F16"}, "unfocused": {"color": "#e6e6e8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#75140e", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#010F16", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#75140e", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#010F16", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#75140e", "fontSize": "16px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#75140e", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#75140e", "fontColor": "#FFFFFF", "fontSize": "16px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#75140e", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#010F16", "fontWeight": "400", "fontStyle": "underline", "fontSize": "12px"}}}}}