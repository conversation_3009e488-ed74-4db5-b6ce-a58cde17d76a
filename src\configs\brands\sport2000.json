{"name": "Sport 2000", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize", "letter-spacing": "1.26px"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize", "letter-spacing": "1.26px"}}, "units": {"fontWeight": "400", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "", "fontColor": "#FFFFFF"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#707070", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "14px", "fontColor": "#383838", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Open Sans", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "capitalize", "color": "#383838", "fontSize": "20px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#383838", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none", "letter-spacing": "1.26px"}, "focused": {"backgroundColor": "#000000", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "letter-spacing": "1.26px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#383838", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0px", "fontSize": "14px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "capitalize", "letter-spacing": "1.26px", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#707070", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#383838", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#383838", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#707070"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "#E6E6E6", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "14px", "fontWeight": "400", "borderRadius": "", "borderColor": "", "borderWidth": "1px", "textTransform": "capitalize", "textDecoration": "underline"}}, "skip": {"fontColor": "#383838", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}