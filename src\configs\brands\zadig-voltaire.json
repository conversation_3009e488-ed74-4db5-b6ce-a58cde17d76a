{"name": "Zadig & Voltaire", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#9D9D9D", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#000000", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "500", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "500", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "14px", "top": "6px", "right": "-11px", "activeColor": "#000000", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "500", "fontSize": "14px", "fontColor": "#9D9D9D", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#9D9D9D", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "500", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textTransform": "uppercase", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "600", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Inter", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#000000", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#000000", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#f5f5f5", "fontWeight": "500", "fontColor": "#9D9D9D", "fontSize": "14px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "1px solid #f5f5f5", "borderBottom": "1px solid #f5f5f5", "borderLeft": "1px solid #f5f5f5", "borderRight": "1px solid #f5f5f5"}, "focused": {"backgroundColor": "#000000", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0", "fontSize": "14px", "fontWeight": "600", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#9D9D9D"}, "unfocused": {"color": "#e6e6e8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}