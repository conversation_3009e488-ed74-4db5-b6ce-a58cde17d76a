{"name": "<PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#D9D9D9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "HafferSQ", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "uppercase", "color": "#000000", "fontSize": "14px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#707070", "fontSize": "12px"}, "cta": {"unfocused": {"backgroundColor": "#f0f0f0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#a0a0a0", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#a0a0a0", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#000000", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "1px"}}, "Politicy": {"fontSize": "10px", "fontColor": "grey", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0", "fontSize": "10px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#a0a0a0", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#a0a0a0", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#a0a0a0", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#a0a0a0", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}