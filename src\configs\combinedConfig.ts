import lacosteConfig from "./brands/lacoste.json";
import kenzoConfig from "./brands/kenzo.json";
import sportyAndRichConfig from "./brands/sporty-rich.json";
import placeDesTondancesConfig from "./brands/place-des-tendances.json";
import leSlipFrancaisConfig from "./brands/le-slip-francais.json";
import izacConfig from "./brands/izac.json";
import jottConfig from "./brands/jott.json";
import fromFutureConfig from "./brands/from-future.json";
import circleSportswearConfig from "./brands/circle-sportswear.json";
import rodierConfig from "./brands/rodier.json";
import laCanadienneConfig from "./brands/la-canadienne.json";
import gerardDarelConfig from "./brands/gerard-darel.json";
import ronDorffConfig from "./brands/ron-dorff.json";
import theKooplesConfig from "./brands/the-kooples.json";
import chloreConfig from "./brands/chlore.json";
import molliConfig from "./brands/molli.json";
import placeDuJourConfig from "./brands/place-du-jour.json";
import gualapConfig from "./brands/gualap.json";
import vogstoreConfig from "./brands/vogstore.json";
import srpConfig from "./brands/srp.json";
import hartfordConfig from "./brands/hartford.json";
import laPetiteEtoileConfig from "./brands/la-petite-etoile.json";
import misterKConfig from "./brands/mister-k.json";
import petroneConfig from "./brands/petrone.json";
import zadigVoltaireConfig from "./brands/zadig-voltaire.json";
import bensimonConfig from "./brands/bensimon.json";
import stellaSuzieConfig from "./brands/stella-suzie.json";
import antonelleConfig from "./brands/antonelle.json";
import uneJourAilleursConfig from "./brands/une-jour-ailleurs.json";
import kookaiConfig from "./brands/kookai.json";
import asphalteConfig from "./brands/asphalte.json";
import soeurConfig from "./brands/soeur.json";
import etParisConfig from "./brands/et-paris.json";
import faguoConfig from "./brands/faguo.json";
import projectXParisConfig from "./brands/project-x-paris.json";
import hastConfig from "./brands/hast.json";
import saajConfig from "./brands/saaj.json";
import bonneGueuleConfig from "./brands/bonne-gueule.json";
import devredConfig from "./brands/devred.json";
import theodoreConfig from "./brands/theodore.json";
import ltdcConfig from "./brands/ltdc.json";
import ninaRicciConfig from "./brands/nina-ricci.json";
import maisonKitsuneConfig from "./brands/maison-kitsun.json";
import victoriaBeckhamConfig from "./brands/victoria-beckham.json";
import rabanneConfig from "./brands/rabanne.json";
import weillConfig from "./brands/weill.json";
import sudExpressConfig from "./brands/sud-express.json";
import loomConfig from "./brands/loom.json";
import almeConfig from "./brands/alme.json";
import sport2000Config from "./brands/sport2000.json";
import loropianaConfig from "./brands/loropiana.json";
import givenchyConfig from "./brands/givenchy.json";
import ogierConfig from "./brands/ogier.json";
import louloudeSaisonConfig from "./brands/louloudeSaison.json";
import ericBompardConfig from "./brands/ericBompard.json";
import intersportConfig from "./brands/intersport.json";
import blackstoreConfig from "./brands/blackstore.json";
import vanessawuConfig from "./brands/vanessawu.json";
import bonpointConfig from "./brands/bonpoint.json";
import paprikaConfig from "./brands/paprika.json";
import fedeliCashmereConfig from "./brands/fedeli-cashmere.json";
import cultoConfig from "./brands/culto.json";
import pyrenexConfig from "./brands/pyrenex.json";
import bruggeConfig from "./brands/brugge.json";
import lecolletConfig from "./brands/lecollet.json";
import velourGarmentsConfig from "./brands/velour-garments.json";

export const combinedConfig = {
  lacoste: lacosteConfig,
  kenzo: kenzoConfig,
  sportyAndRich: sportyAndRichConfig,
  placeDesTondances: placeDesTondancesConfig,
  leSlipFrancais: leSlipFrancaisConfig,
  izac: izacConfig,
  jott: jottConfig,
  fromFuture: fromFutureConfig,
  circleSportswear: circleSportswearConfig,
  rodier: rodierConfig,
  laCanadienne: laCanadienneConfig,
  gerardDarel: gerardDarelConfig,
  ronDorff: ronDorffConfig,
  theKooples: theKooplesConfig,
  chlore: chloreConfig,
  molli: molliConfig,
  placeDuJour: placeDuJourConfig,
  gualap: gualapConfig,
  vogstore: vogstoreConfig,
  srp: srpConfig,
  hartford: hartfordConfig,
  laPetiteEtoile: laPetiteEtoileConfig,
  misterK: misterKConfig,
  petrone: petroneConfig,
  zadigVoltaire: zadigVoltaireConfig,
  bensimon: bensimonConfig,
  stellaSuzie: stellaSuzieConfig,
  antonelle: antonelleConfig,
  uneJourAilleurs: uneJourAilleursConfig,
  kookai: kookaiConfig,
  asphalte: asphalteConfig,
  soeur: soeurConfig,
  etParis: etParisConfig,
  faguo: faguoConfig,
  projectXParis: projectXParisConfig,
  hast: hastConfig,
  saaj: saajConfig,
  bonneGueule: bonneGueuleConfig,
  devred: devredConfig,
  theodore: theodoreConfig,
  ltdc: ltdcConfig,
  ninaRicci: ninaRicciConfig,
  maisonKitsune: maisonKitsuneConfig,
  victoriaBeckham: victoriaBeckhamConfig,
  rabanne: rabanneConfig,
  weill: weillConfig,
  sudExpress: sudExpressConfig,
  loom: loomConfig,
  alme: almeConfig,
  sport2000: sport2000Config,
  loropiana: loropianaConfig,
  givenchy: givenchyConfig,
  ogier: ogierConfig,
  louloudeSaison: louloudeSaisonConfig,
  ericBompard: ericBompardConfig,
  intersport: intersportConfig,
  blackstore: blackstoreConfig,
  vanessawu: vanessawuConfig,
  bonpoint: bonpointConfig,
  paprika: paprikaConfig,
  fedelicashmere: fedeliCashmereConfig,
  culto: cultoConfig,
  pyrenex: pyrenexConfig,
  brugge: bruggeConfig,
  lecollet: lecolletConfig,
  velourGarments: velourGarmentsConfig,
};
