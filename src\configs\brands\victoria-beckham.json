{"name": "<PERSON>ham", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "15px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "left", "fontStyle": "italic", "fontFamily": "<PERSON><PERSON>"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "13px", "fontColor": "#A3A1A7", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#000000", "marginBottom": "10px", "padding": "8px 10px", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "10px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "fontSize": "10px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "10px", "top": "-4px", "right": "-2px", "activeColor": "#000000", "inactiveColor": "#FFFFFF", "textTransform": "uppercase"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "10px", "fontColor": "#8C8B8B", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#8C8B8B", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "10px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "10px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textTransform": "uppercase", "textDecoration": "none", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Times New Roman", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#000000", "fontSize": "24px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#000000", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#000000", "fontWeight": "400", "fontSize": "10px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#000000", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "10px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#A3A1A7", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "", "borderColor": "", "borderRadius": "", "fontSize": "", "fontWeight": "", "fontColor": "", "textTransform": "", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#717171"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "10px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "10px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "10px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "", "fontSize": "10px"}}}}}