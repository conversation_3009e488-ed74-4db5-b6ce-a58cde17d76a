{"name": "<PERSON><PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "13px", "fontColor": "#666666", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": ""}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "60px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize", "letter-spacing": "1.12px"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "60px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize", "letter-spacing": "1.12px"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "", "fontColor": "#FFFFFF"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Maven <PERSON>", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#000000", "fontSize": "22px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#000000", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "60px", "borderColor": "#000000", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none", "letter-spacing": "1.12px"}, "focused": {"backgroundColor": "#000000", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "capitalize", "borderRadius": "60px", "borderColor": "#000000", "borderWidth": "1px", "letter-spacing": "1.12px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "60px", "fontSize": "14px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "25px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "25px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "25px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "25px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#F0F0F0", "fontColor": "#515151", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#0000001f", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#F0F0F0", "fontColor": "#515151", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "capitalize"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}