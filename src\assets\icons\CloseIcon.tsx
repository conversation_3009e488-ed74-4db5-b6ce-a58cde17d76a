import { SvgIcon, SvgIconProps } from "@mui/material";

const CloseIcon = (props: SvgIconProps) => (
  <SvgIcon
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1538_12372)">
      <path
        d="M13.2512 12.01L23.7138 1.54558C24.0599 1.19766 24.0599 0.635629 23.7138 0.287706C23.3677 -0.0602174 22.8087 -0.0602174 22.4626 0.287706L12 10.7254L1.53744 0.260942C1.16473 -0.0869808 0.605657 -0.0869808 0.259567 0.260942C-0.0865225 0.608865 -0.0865225 1.17089 0.259567 1.51882L10.7221 12.01L0.259567 22.4745C-0.0865225 22.8224 -0.0865225 23.3844 0.259567 23.7324C0.445923 23.9197 0.658902 24 0.898503 24C1.1381 24 1.35108 23.9197 1.53744 23.7324L12 13.2679L22.4626 23.7324C22.6489 23.8929 22.8619 24 23.1015 24C23.3411 24 23.5541 23.9197 23.7404 23.7324C24.0865 23.3844 24.0865 22.8224 23.7404 22.4745L13.2512 12.01Z"
        fill={props?.fill || "black"}
      />
    </g>
    <defs>
      <clipPath id="clip0_1538_12372">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </SvgIcon>
);

export default CloseIcon;
