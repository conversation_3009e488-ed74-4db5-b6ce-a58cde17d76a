{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "male", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#000000", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#2E2E2E", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #2E2E2E"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#2E2E2E", "borderRadius": "0", "borderColor": "#262626", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#262626", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#262626", "inactiveColor": "#A7a7a7"}}, "2": {"routeCTA": {"borderColor": "#262626", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#2e2e2e"}}, "3": {"qrcode": {"backgroundColor": "#2e2e2e"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#2e2e2e", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2e2e2e", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#E9E9E9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#262626", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2e2e2e", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Manrope", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "", "color": "#2E2E2E", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#2E2E2E", "fontSize": "13px"}, "cta": {"unfocused": {"backgroundColor": "#DDDDDD", "fontWeight": "400", "fontSize": "14px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "4px", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#2E2E2E", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "uppercase", "borderRadius": "4px", "borderColor": "transparent", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "", "borderColor": "", "borderRadius": "", "fontSize": "", "fontWeight": "", "fontColor": "", "textTransform": "", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#a7a7a7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2e2e2e", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#262626", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2e2e2e", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#2e2e2e"}, "unfocused": {"color": "#A3A1A7"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#2e2e2e", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#2e2e2e", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#262626", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#262626", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#262626", "fontSize": "12px", "fontWeight": "700", "borderRadius": "0", "borderColor": "#262626", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}