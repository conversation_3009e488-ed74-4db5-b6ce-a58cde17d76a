{"name": "<PERSON><PERSON><PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "16px", "fontColor": "#002e5d", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#acb8c4", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#002e5d", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#002e5d", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #002e5d"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#002e5d", "borderRadius": "0", "borderColor": "#002e5d", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#002e5d", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#002e5d", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "-11px", "activeColor": "#002e5d", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#002e5d", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#002e5d"}}, "3": {"qrcode": {"backgroundColor": "#002e5d"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#002e5d", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#002e5d", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#acb8c4", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#acb8c4", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "16px", "fontColor": "#57be70", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#57be70", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "16px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#002e5d", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#002e5d", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#acb8c4", "textTransform": "uppercase", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "400", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Inter", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#002e5d", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#002e5d", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#002e5d", "backgroundOpacity": "5%", "fontWeight": "700", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "0", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#002e5d", "fontWeight": "700", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "0"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#002e5d", "borderRadius": "0", "fontSize": "12px", "fontWeight": "400", "fontColor": "#002e5d", "textTransform": "capitalize", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#002e5d", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#002e5d", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#002e5d", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "500", "fontSize": "12px", "fontColor": "#002e5d", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#002e5d"}, "unfocused": {"color": "#e6e6e8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#002e5d", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#002e5d", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#002e5d", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#002e5d", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#002e5d", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#002e5d", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#002e5d", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#002e5d", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}