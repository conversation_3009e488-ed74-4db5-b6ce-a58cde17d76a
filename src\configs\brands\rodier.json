{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "200", "fontSize": "16px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "13px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "13px", "fontColor": "#231f20", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "8px", "borderColor": "#231f20", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #231f20"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#231f20", "borderRadius": "0", "borderColor": "#231f20", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "300", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#231f20", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#231f20", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "300", "textTransform": "uppercase"}}, "units": {"fontWeight": "300", "fontSize": "13px", "top": "0", "right": "-11px", "activeColor": "#231f20", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#231f20"}}, "3": {"qrcode": {"backgroundColor": "#231f20"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#231f20", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#231f20", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#E9E9E9", "borderWidth": "2px"}, "focused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#44883F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "300", "fontSize": "14px", "fontColor": "#231f20", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "300", "fontSize": "14px", "fontColor": "#231f20", "textAlign": "left"}, "productTitle": {"fontWeight": "300", "fontSize": "14px", "fontColor": "#231f20"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "14px", "fontColor": "#231f20", "textTransform": "uppercase", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#B9B9B9", "textAlign": "center", "textTransform": "uppercase"}}, "all": {"font": "<PERSON><PERSON><PERSON>", "titles": {"textAlign": "left", "fontWeight": "200", "textTransform": "uppercase", "color": "#231f20", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "200", "textTransform": "capitalize", "color": "#231f20", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#DDDDDD", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#231f20", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#231f20", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#231f20", "borderRadius": "0", "fontSize": "12px", "fontWeight": "400", "fontColor": "#231f20", "textTransform": "capitalize", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "300", "fontSize": "13px", "fontColor": "#231f20", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#231f20", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "300", "fontSize": "13px", "fontColor": "#231f20", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#231f20"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "13px", "fontColor": "#231f20", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "600", "fontSize": "13px", "fontColor": "#231f20", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#737373", "fontSize": "12px", "fontWeight": "600", "borderRadius": "0", "borderColor": "#E9E9E9", "borderWidth": "2px", "textTransform": "unset"}, "focused": {"backgroundColor": "#231f20", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "600", "borderRadius": "0", "borderColor": "#231f20", "borderWidth": "2px", "textTransform": "unset"}}, "skip": {"fontColor": "#231f20", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}