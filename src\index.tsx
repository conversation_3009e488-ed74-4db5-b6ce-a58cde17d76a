import ReactDOM from "react-dom/client";
import App from "./App";
import { UserContextProvider } from "./store/userContext";

import * as Sentry from "@sentry/react";

import "./fonts/index.css";
import "./index.css";

Sentry.init({
  dsn: "https://<EMAIL>/4509253762875472",
  // Setting this option to true will send default PII data to Sentry.
  // For example, automatic IP address collection on events
  sendDefaultPii: true,
  integrations: [
    Sentry.replayIntegration()
  ],
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 1.0 // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);

root.render(
  <UserContextProvider>
    <App />
  </UserContextProvider>
);
