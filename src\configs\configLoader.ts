import { ParentDomain, Subdomain } from "../types/domains";
import { brandsArray } from "./brandsArray";

// Function to extract query parameter from the URL
export const getQueryParam = (param: string) => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(param);
};

// Function to get the config based on domain
export const getConfig = () => {
  const domain = getQueryParam("domain");
  const brand = domain
    ? brandsArray.find((brand) => brand.domains.some((d) => d.name === domain))
    : null;

  if (brand) {
    return brand.config;
  } else {
    return (
      brandsArray.find((brand) => brand.name === "The Kooples")?.config ||
      brandsArray?.[0]?.config
    );
  }
};

export const findBrandByDomain = () => {
  const domain = getQueryParam("domain");

  if (!domain) return null;
  return brandsArray.find((brand) =>
    brand.domains.some((d) => d.name === domain)
  );
};

export const handleParentDomain = (): ParentDomain | null => {
  const domain = getQueryParam("domain");
  if (!domain) return null;

  for (const brand of brandsArray) {
    const matchedDomain = brand.domains.find((d: any) => d.name === domain);
    if (matchedDomain) {
      const regionMatch = matchedDomain.name.match(/\/([a-z]{2}-[a-z]{2})$/i);

      return {
        name: brand.name.toLowerCase().replace(/\s+/g, ""),
        subdomain: matchedDomain as Subdomain,
        unit_system: matchedDomain?.unit_system,
        language: matchedDomain?.language,
        region: regionMatch ? regionMatch[1].toLowerCase() : undefined,
      };
    }
  }

  return null;
};
