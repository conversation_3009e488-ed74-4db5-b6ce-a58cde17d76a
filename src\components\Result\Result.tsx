import React, { FC, useState, useEffect } from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import MeasuredBy from "../molecules/MeasuredBy";
import { activeStylesContinue, resultScreenUI, uxGender } from "../../configs";

import { CheckCircle as CheckCircleIcon } from "@mui/icons-material";
import CheckIconUnavailable from "../../assets/icons/CheckIconUnavailable";

import H_twh from "../../assets/result/H_twh.svg";
import H_ctw from "../../assets/result/H_ctw.svg";
import H_ctwh from "../../assets/result/H_ctwh.svg";
import H_h from "../../assets/result/H_h.svg";
import H_h_unisex from "../../assets/result/H_h_unisex.svg";
import H_c from "../../assets/result/H_c.svg";

import F_c from "../../assets/result/F_c.svg";
import F_cw from "../../assets/result/F_cw.svg";
import F_ctwh from "../../assets/result/F_ctwh.svg";
import F_wh from "../../assets/result/F_wh.svg";
import F_h_unisex from "../../assets/result/F_h_unisex.svg";

import example_tailes from "../../assets/result/example_tailes.png";
import example_tailes_2 from "../../assets/result/example_tailes_2.png";

import { removeLocalStore } from "../../store/localStoreUtils";
import { useUserContext } from "../../store/userContext";
import { ReducedResultType, SizeDataType } from "../../types/result";
import { GENDERS } from "../../constants/modal";
import ShoeResult from "./components/ShoeResult";
import { capitalizeFirstLetter, useIsMobile } from "../../utils";
import UnavailableGallery from "./components/UnavailableGallery";
import { findSimilarProducts } from "../../api/endpoints";
import { findBrandByDomain } from "../../configs/configLoader";
import { handleAnalytics } from "../../utils/tracking";

import InfoStyledIcon from "../../assets/icons/InfoStyledIcon";

import { logger } from "../../utils/logging";

import {
  postAddToCart,
  postClearMid,
  postCloseIframe,
  postSelectSize,
  postSendMid,
  postSendUid,
} from "../../utils/post";

import GeneratedSizes from "./components/GeneratedSizes";
import DescriptionResults from "./components/DescriptionResults/DescriptionResults";
import SizeSelectorsComponent from "./components/SizeSelectorsComponent";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { hoverStylesContinue } from "../../configs/stylesLoader";

import "./index.css";

type SelectorSizeType = "unfocused" | "focused" | "unavailable";

interface IPropsResult {
  step: any;
  reducedResult: ReducedResultType;
  selectedGender: string;
  productStockData: any;
  similarProducts: any;
  isSizeUnavailable: boolean;
  selectedSize: SizeDataType | null;
  setSelectedSize: React.Dispatch<React.SetStateAction<SizeDataType | null>>;
  setSimilarProducts: React.Dispatch<any>;
  isShoesProduct: boolean;
  isChildrenProduct: boolean;
  restart: () => void;
  recommendedSize: any;
}

const Result: FC<IPropsResult> = ({
  step,
  reducedResult,
  selectedGender,
  productStockData,
  similarProducts,
  isSizeUnavailable,
  selectedSize,
  setSelectedSize,
  setSimilarProducts,
  isShoesProduct,
  isChildrenProduct,
  recommendedSize,
  restart,
}) => {
  const isMobile = useIsMobile();
  const isVerySmallMobile = useMediaQuery({ maxWidth: 400 });

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: !isMobile ? 830 : 778,
    minWidth: 767,
  });

  const userContext = useUserContext() || undefined;
  const user = userContext?.user;
  const [pid, setPid] = useState<string | null>(userContext?.pid || null);

  const [hovered, setHovered] = useState(false);

  useEffect(() => {
    const localPid = userContext?.pid || null;
    if (localPid) setPid(localPid);
  }, [userContext]);

  const { t } = useTranslation("components/results/result");

  const fit_feedbacks = [t("size.size1"), t("size.size2"), t("size.size3")];

  const [selectedVariant, setSelectedVariant] = useState<string>("1");
  const [selectedSizeImage, setSelectedSizeImage] = useState<string>("");
  const [feedbacks, setFeedbacks] = useState<any>({});
  const [isSimilarProductsLoaded, setIsSimilarProductsLoaded] = useState(false);

  const brandDefined = findBrandByDomain();

  const urlParameters = new URLSearchParams(window.location.search);
  const domain = urlParameters.get("domain");
  const isLingerie = urlParameters.get("lingerie") === "true";

  const isDesktopGerardDarel =
    brandDefined?.name === "Gerard Darel" && !isMobile;

  useEffect(() => {
    if (reducedResult) {
      const entries = Object.entries(reducedResult);
      if (entries.length > 1) {
        setSelectedVariant(entries[1][0]);
      }
    }
  }, [reducedResult]);

  const handleFindSimilarProducts = async (
    variant_id: any,
    activeRequestId: any
  ) => {
    if (!isSizeUnavailable) return;

    const noVariantIdBrands = [
      "Place des Tendances",
      "Rodier",
      "La Canadienne",
    ];
    const variantIsUnavailable = brandDefined?.name
      ? noVariantIdBrands.includes(brandDefined.name)
      : false;

    const variantId = variantIsUnavailable ? null : variant_id;
    const asphalte = domain === "asphalte.com";

    if (domain && !asphalte && pid && productStockData?.length) {
      const currentRequestId = Symbol("requestId");
      activeRequestId = currentRequestId;

      const market = urlParameters.get("market");

      setSimilarProducts([]);
      setIsSimilarProductsLoaded(false);

      try {
        let similarProducts = await findSimilarProducts(
          domain,
          pid,
          variantId ?? null
        );

        if (market && domain.includes("zadig")) {
          similarProducts = similarProducts.map((product: any) => {
            return {
              ...product,
              product_url: product.product_url.replace("eu/fr", market),
            };
          });
        }

        if (
          (activeRequestId === currentRequestId ||
            (brandDefined?.name === "SRP" && similarProducts?.length)) &&
          Array.isArray(similarProducts) &&
          similarProducts.length > 0
        ) {
          setSimilarProducts(similarProducts);
        } else {
          setSimilarProducts([]);
        }
      } catch (error) {
        logger.error(`error: ${error}`);
        setSimilarProducts([]);
      } finally {
        setIsSimilarProductsLoaded(true);
      }
    }
  };

  useEffect(() => {
    if (!selectedVariant || !reducedResult) return;

    const size = reducedResult[selectedVariant];

    if (!size) {
      const sizeNext = reducedResult[0];
      if (sizeNext) setSelectedSize(sizeNext);
    } else {
      setSelectedSize(size);
    }

    const variantId = size?.variant_id;
    if (variantId?.toString().trim()) {
      postSelectSize(variantId);
    }
  }, [selectedVariant, reducedResult]);

  const findByLabelRank = (rank: number) => {
    if (!reducedResult) return null;

    const values = Object.values(reducedResult || {});

    if (values.length === 1) {
      if (rank === 1) {
        const onlyItem = values[0];
        return onlyItem?.label ? onlyItem : null;
      } else {
        return null;
      }
    }

    const foundItem = values.find((item) => item.label_rank === rank);
    return foundItem?.label ? foundItem : null;
  };

  const itemWithLabelRank0 = findByLabelRank(0);
  const itemWithLabelRank1 = findByLabelRank(1);
  const itemWithLabelRank2 = findByLabelRank(2);

  useEffect(() => {
    if (!selectedSize || !isSizeUnavailable) return;

    let activeRequestId = null;

    handleFindSimilarProducts(selectedSize?.variant_id, activeRequestId);

    return () => {
      activeRequestId = null;
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSizeUnavailable, selectedSize]);

  useEffect(() => {
    const focused = selectedVariant ? parseInt(selectedVariant, 10) : null;
    if (reducedResult && focused !== null) {
      const values = Object.values(reducedResult || {});

      const focusedFeeback =
        values.length === 1
          ? reducedResult?.[0]?.fit_indicators
          : reducedResult?.[focused]?.fit_indicators;

      if (focusedFeeback) {
        const result = focusedFeeback?.reduce(
          (acc: any, { limb, value }: any) => {
            acc[limb] = value - 1;
            return acc;
          },
          {}
        );
        setFeedbacks(result);
      }
    }
  }, [reducedResult, selectedVariant]);

  useEffect(() => {
    const gender = selectedGender || localStorage.getItem("gender") || "";

    if (feedbacks) {
      if (gender === GENDERS.M) {
        // result 1 - MALE
        if (
          feedbacks.hip >= 0 &&
          feedbacks.waist >= 0 &&
          feedbacks.chest === undefined
        ) {
          setSelectedSizeImage(H_twh);
        }

        // result 2 - MALE
        if (
          feedbacks.chest >= 0 &&
          feedbacks.waist >= 0 &&
          feedbacks.hip === undefined
        ) {
          setSelectedSizeImage(H_ctw);
        }

        // result 3 - MALE
        if (
          feedbacks.chest >= 0 &&
          feedbacks.waist === undefined &&
          feedbacks.hip === undefined
        ) {
          setSelectedSizeImage(H_c);
        }
      }

      if (gender === GENDERS.F) {
        // result 1 - FEMALE
        if (
          feedbacks.chest >= 0 &&
          feedbacks.waist >= 0 &&
          feedbacks.hip === undefined
        ) {
          setSelectedSizeImage(F_cw);
        }

        // result 2 - FEMALE
        if (
          feedbacks.hip >= 0 &&
          feedbacks.waist >= 0 &&
          feedbacks.chest === undefined
        ) {
          setSelectedSizeImage(F_wh);
        }

        // result 3 - FEMALE
        if (
          feedbacks.chest >= 0 &&
          feedbacks.waist === undefined &&
          feedbacks.hip === undefined
        ) {
          setSelectedSizeImage(F_c);
        }
      }

      // result 3 - MALE
      if (
        feedbacks.waist >= 0 &&
        feedbacks.hip === undefined &&
        feedbacks.chest === undefined
      ) {
        setSelectedSizeImage(H_h);
      }

      // unisex result
      if (
        feedbacks.hip >= 0 &&
        feedbacks.waist === undefined &&
        feedbacks.chest === undefined
      ) {
        setSelectedSizeImage(gender === GENDERS.M ? H_h_unisex : F_h_unisex);
      }

      // result 3 - ALL
      if (feedbacks.chest >= 0 && feedbacks.waist >= 0 && feedbacks.hip >= 0) {
        setSelectedSizeImage(gender === GENDERS.M ? H_ctwh : F_ctwh);
      }
    } else {
      // setSelectedSizeImage(selectedGender === GENDERS.F ? F_cw : H_h);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [feedbacks, selectedGender]);

  const isSRPUnavailable = brandDefined?.name === "SRP" && isSizeUnavailable;

  const getSelectorSizeStyles = (type: SelectorSizeType, variant: string) => {
    const sizeStyles = resultScreenUI.sizeSelector[type]!;

    const borderWidthNum = parseInt(sizeStyles.borderWidth);
    const cornerBorderWidth =
      variant === "1" ? `${borderWidthNum - 1}px` : `${borderWidthNum}px`;

    const values = Object.values(reducedResult || {});

    const SRPUnavailableSize = isSRPUnavailable && selectedVariant === variant;

    const selectorSizeStyles = {
      backgroundColor: SRPUnavailableSize
        ? "rgba(39, 38, 38, 0.08)"
        : (sizeStyles.backgroundColor as React.CSSProperties["backgroundColor"]),
      fontWeight: sizeStyles.fontWeight as React.CSSProperties["fontWeight"],
      fontSize: sizeStyles.fontSize as React.CSSProperties["fontSize"],
      textTransform:
        sizeStyles.textTransform as React.CSSProperties["textTransform"],
      color: SRPUnavailableSize
        ? "rgba(39, 38, 38, 0.32)"
        : (sizeStyles.fontColor as React.CSSProperties["color"]),
      borderTopLeftRadius:
        variant === "0"
          ? (sizeStyles.borderRadius as React.CSSProperties["borderRadius"])
          : 0,
      borderBottomLeftRadius:
        variant === "0"
          ? (sizeStyles.borderRadius as React.CSSProperties["borderRadius"])
          : 0,
      borderTopRightRadius:
        variant === "2"
          ? (sizeStyles.borderRadius as React.CSSProperties["borderRadius"])
          : 0,
      borderBottomRightRadius:
        variant === "2"
          ? (sizeStyles.borderRadius as React.CSSProperties["borderRadius"])
          : 0,
      borderWidthStyle: sizeStyles.borderWidth,
      borderTop: `${sizeStyles.borderWidth} solid ${
        SRPUnavailableSize ? "rgba(39, 38, 38, 0.32)" : sizeStyles.borderColor
      }`,
      borderBottom: `${sizeStyles.borderWidth} solid ${
        SRPUnavailableSize ? "rgba(39, 38, 38, 0.32)" : sizeStyles.borderColor
      }`,
      borderLeft:
        selectedSize?.possible === 0 && selectedVariant === "1"
          ? `${cornerBorderWidth} solid ${resultScreenUI.sizeSelector["unavailable"]?.borderColor}`
          : isSRPUnavailable
          ? `${cornerBorderWidth} solid rgba(2, 1, 1, 0.32)`
          : `${cornerBorderWidth} solid ${
              values.length === 1
                ? sizeStyles.borderColor
                : selectedVariant === "1" && variant === "2"
                ? resultScreenUI.sizeSelector["focused"]?.borderColor
                : sizeStyles.borderColor
            }`,
      borderRight:
        selectedSize?.possible === 0 && selectedVariant === "1"
          ? `${cornerBorderWidth} solid ${resultScreenUI.sizeSelector["unavailable"]?.borderColor}`
          : isSRPUnavailable
          ? `${cornerBorderWidth} solid rgba(39, 38, 38, 0.32)`
          : `${cornerBorderWidth} solid ${
              values.length === 1
                ? sizeStyles.borderColor
                : selectedVariant === "1" && variant === "0"
                ? resultScreenUI.sizeSelector["focused"]?.borderColor
                : sizeStyles.borderColor
            }`,
      letterSpacing: (sizeStyles as any)?.letterSpacing,
      fontStyle: "normal",
    };

    return selectorSizeStyles;
  };

  const firstSelectorStyles =
    selectedVariant === "0"
      ? getSelectorSizeStyles(
          selectedSize?.possible === 0 && !isSizeUnavailable
            ? "unavailable"
            : "focused",
          "0"
        )
      : getSelectorSizeStyles("unfocused", "0");

  const secondSelectorStyles =
    selectedVariant === "1"
      ? getSelectorSizeStyles(
          selectedSize?.possible === 0 ? "unavailable" : "focused",
          "1"
        )
      : getSelectorSizeStyles("unfocused", "1");

  const secondSelectorIdealStyles =
    selectedVariant === "1"
      ? getSelectorSizeStyles("focused", "1")
      : getSelectorSizeStyles("unfocused", "1");

  const thirdSelectorStyles =
    selectedVariant === "2"
      ? getSelectorSizeStyles(
          selectedSize?.possible === 0 && !isSizeUnavailable
            ? "unavailable"
            : "focused",
          "2"
        )
      : getSelectorSizeStyles("unfocused", "2");

  const skipTextTransform = resultScreenUI.restartCTA
    .textTransform as React.CSSProperties["textTransform"];

  const sendIds = () => {
    const uid = localStorage.getItem("uid");
    const mid = localStorage.getItem("mid");
    postSendMid(mid);
    if (uid && uid !== null && uid !== undefined) {
      postSendUid(uid);
    }
  };
  const clearMid = () => {
    postClearMid();
  };

  const addToCart = () => {
    let variantId = selectedSize?.variant_id;
    logger.log(`addToCart called: ${variantId}`);
    if (variantId) postAddToCart(variantId);
  };

  const getMarginTopSkip = () => {
    if (!isMobile && brandDefined?.name === "Soeur") return "20px";
    if (isDesktopGerardDarel) return "10px";
    if (isLargeHeightRelative) return "20px";
    if (isMobile) return "0px";

    return "20px";
  };

  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...activeStylesContinue,
    ...(isIntersport && hovered ? hoverStylesContinue : {}),
    textTransform:
      activeStylesContinue.textTransform === "capitalize"
        ? "none"
        : activeStylesContinue.textTransform,
    letterSpacing: (activeStylesContinue as any)?.letterSpacing,
    padding: brandDefined?.name === "Victoria Beckham" ? "7px" : "16px",
    height: brandDefined?.name === "Victoria Beckham" ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && selectedSize ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && selectedSize ? 1 : 0,
    transform: hovered && selectedSize ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return isShoesProduct || isChildrenProduct || isLingerie ? (
    <ShoeResult
      step={step}
      restart={restart}
      recommendation={recommendedSize}
      recommendedSize={recommendedSize}
      selectedVariant={selectedVariant}
      setSelectedVariant={setSelectedVariant}
      isSizeUnavailable={isSizeUnavailable}
      isChildrenProduct={isChildrenProduct}
      isShoesProduct={isShoesProduct}
    />
  ) : (
    <div
      className="result"
      style={{
        marginTop: isLargeHeightRelative ? "32px" : !isMobile ? "40px" : 0,
      }}
    >
      {brandDefined?.name !== "Victoria Beckham" ? (
        <>
          <div className="size">
            <h1
              style={{
                color:
                  isSizeUnavailable && brandDefined?.name !== "SRP"
                    ? (resultScreenUI.unavailable_size_text
                        .fontColor as React.CSSProperties["color"])
                    : (resultScreenUI.recommendedSize
                        .fontColor as React.CSSProperties["color"]),
                fontSize: isLargeHeightRelative
                  ? `calc(${resultScreenUI.recommendedSize.fontSize} - 25px)`
                  : brandDefined?.name !== "Lacoste" && isMobile
                  ? "40px"
                  : (resultScreenUI.recommendedSize
                      .fontSize as React.CSSProperties["fontSize"]),
                fontWeight: resultScreenUI.recommendedSize
                  .fontWeight as React.CSSProperties["fontWeight"],
                textAlign: resultScreenUI.recommendedSize
                  .textAlign as React.CSSProperties["textAlign"],
                marginTop: isDesktopGerardDarel
                  ? 0
                  : isLargeHeightRelative
                  ? 0
                  : isMobile
                  ? "10px"
                  : "54px",
                marginBottom: isLargeHeightRelative
                  ? 0
                  : isVerySmallMobile
                  ? "5px"
                  : isMobile
                  ? "10px"
                  : "12px",
                letterSpacing: (resultScreenUI.Description as any)
                  ?.letterSpacing,
              }}
            >
              {selectedSize?.label || ""}
            </h1>
            <div className="size-name">
              {selectedVariant === "1" && (
                <>
                  <h2
                    style={{
                      color: isSRPUnavailable
                        ? "rgba(39, 38, 38, 0.32)"
                        : ((secondSelectorIdealStyles.color === "#FFFFFF"
                            ? secondSelectorIdealStyles.backgroundColor
                            : secondSelectorIdealStyles.color) as React.CSSProperties["color"]),
                      fontSize: resultScreenUI.subtitles
                        .fontSize as React.CSSProperties["fontSize"],
                      fontWeight:
                        brandDefined?.name === "Zadig & Voltaire" ||
                        brandDefined?.name === "Lacoste"
                          ? 700
                          : (resultScreenUI.subtitles
                              .fontWeight as React.CSSProperties["fontWeight"]),
                      textAlign: resultScreenUI.subtitles
                        .textAlign as React.CSSProperties["textAlign"],
                    }}
                  >
                    {t("size.title")}
                  </h2>
                  {brandDefined?.name !== "Zadig & Voltaire" &&
                    brandDefined?.name !== "Lacoste" && (
                      <>
                        {isSRPUnavailable ? (
                          <CheckIconUnavailable
                            fill={
                              (secondSelectorIdealStyles.color === "#FFFFFF"
                                ? secondSelectorIdealStyles.backgroundColor
                                : secondSelectorIdealStyles.color) as React.CSSProperties["color"]
                            }
                          />
                        ) : (
                          <CheckCircleIcon
                            style={{
                              color: (secondSelectorIdealStyles.color ===
                              "#FFFFFF"
                                ? secondSelectorIdealStyles.backgroundColor
                                : secondSelectorIdealStyles.color) as React.CSSProperties["color"],
                            }}
                          />
                        )}
                      </>
                    )}
                </>
              )}
            </div>
            {!isLingerie ? (
              <SizeSelectorsComponent
                itemWithLabelRank0={itemWithLabelRank0}
                itemWithLabelRank1={itemWithLabelRank1}
                itemWithLabelRank2={itemWithLabelRank2}
                selectedVariant={selectedVariant}
                setSelectedVariant={setSelectedVariant}
                firstSelectorStyles={firstSelectorStyles}
                secondSelectorStyles={secondSelectorStyles}
                thirdSelectorStyles={thirdSelectorStyles}
                fit_feedbacks={fit_feedbacks}
                selectedSize={selectedSize}
                isSizeUnavailable={isSizeUnavailable}
              />
            ) : null}
          </div>
        </>
      ) : (
        <div className="size">
          {!isLingerie ? (
            <SizeSelectorsComponent
              itemWithLabelRank0={itemWithLabelRank0}
              itemWithLabelRank1={itemWithLabelRank1}
              itemWithLabelRank2={itemWithLabelRank2}
              selectedVariant={selectedVariant}
              setSelectedVariant={setSelectedVariant}
              firstSelectorStyles={firstSelectorStyles}
              secondSelectorStyles={secondSelectorStyles}
              thirdSelectorStyles={thirdSelectorStyles}
              fit_feedbacks={fit_feedbacks}
              selectedSize={selectedSize}
              isSizeUnavailable={isSizeUnavailable}
            />
          ) : null}
        </div>
      )}

      {brandDefined?.name !== "Velour Garments" &&
      brandDefined?.name !== "Victoria Beckham" ? (
        <>
          {resultScreenUI.generateResult && !isLingerie ? (
            <GeneratedSizes
              selectedSizeImage={selectedSizeImage}
              feedbacks={feedbacks}
              selectedVariant={selectedVariant}
              fit_feedbacks={fit_feedbacks}
              selectedSize={selectedSize}
            />
          ) : (
            <DescriptionResults
              isSizeUnavailable={isSizeUnavailable}
              selectedVariant={selectedVariant}
              selectedSize={selectedSize}
              similarProducts={similarProducts}
              isSimilarProductsLoaded={isSimilarProductsLoaded}
            />
          )}
        </>
      ) : null}

      {brandDefined?.name === "Velour Garments" && (
        <div
          className="result-body"
          style={{ borderColor: "transparent", height: "40px" }}
        ></div>
      )}

      {brandDefined?.name === "Lacoste" && (
        <div
          className="result-body"
          style={{
            height: "auto",
            padding: isMobile ? "10px 0" : "20px 0",
            display: "flex",
            flexDirection: "column",
            gap: "5px",
            marginBottom: "10px",
            border: "1px #E9E9E9 solid",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              alignItems: "center",
              padding: "0 20px",
            }}
          >
            <InfoStyledIcon
              style={{
                fontSize: "16px",
              }}
              fill={
                resultScreenUI.Description
                  .fontColor as React.CSSProperties["color"]
              }
            />
            <h3
              style={{
                margin: 0,
                fontSize: isVerySmallMobile ? "11px" : "15px",
                fontWeight: 700,
                color: resultScreenUI.Description
                  .fontColor as React.CSSProperties["color"],
                letterSpacing: (resultScreenUI.Description as any)
                  ?.letterSpacing,
              }}
            >
              {t("size_parameter_title").replace(
                "[S]",
                itemWithLabelRank1?.label || ""
              )}
            </h3>
          </div>
          <p
            style={{
              margin: 0,
              paddingLeft: "45px",
              paddingRight: isMobile ? "10px" : "50px",
              fontSize: isVerySmallMobile ? "11px" : "15px",
              fontWeight: 500,
              color: resultScreenUI.Description
                .fontColor as React.CSSProperties["color"],
              letterSpacing: (resultScreenUI.Description as any)?.letterSpacing,
            }}
          >
            {t("size_parameter_description").replace(
              "[S]",
              itemWithLabelRank1?.label || ""
            )}
          </p>
        </div>
      )}
      {/* if need to use for SRP use <UnavailableGallerySRP ... /> component  */}
      {similarProducts?.length &&
      isSizeUnavailable &&
      brandDefined?.name !== "SRP" &&
      brandDefined?.name !== "Asphalte" ? (
        <UnavailableGallery similarProducts={similarProducts} />
      ) : null}
      {selectedSize && selectedSize?.variant_id && !isSizeUnavailable && (
        <button
          type="button"
          className={`continue-button step-bottom-result`}
          onMouseEnter={(e) => {
            setHovered(true);
            Object.assign(e.currentTarget.style, hoverStylesContinue);
          }}
          onMouseLeave={(e) => {
            setHovered(false);
            Object.assign(e.currentTarget.style, CTAStyles);
          }}
          onClick={() => {
            if (selectedSize?.variant_id) {
              addToCart();
              sendIds();
              postCloseIframe();

              handleAnalytics("action", "size_atc", {
                key: "value",
                value: selectedSize?.label,
              });
            }
          }}
          style={CTAStyles}
        >
          <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
            {activeStylesContinue.textTransform === "capitalize" &&
            brandDefined?.name !== "SRP" &&
            brandDefined?.name !== "Theodore" &&
            brandDefined?.name !== "Project X Paris"
              ? t("button").replace(
                  "[S]",
                  selectedSize?.label?.toUpperCase() || ""
                )
              : t("button").replace(
                  "[S]",
                  selectedSize?.label?.toUpperCase() || ""
                )}

            {isIntersport && (
              <span style={arrowContainerStyle} aria-hidden>
                <ChevronRight style={iconStyle} />
              </span>
            )}
          </div>
        </button>
      )}
      <span
        className="skip"
        onClick={() => {
          clearMid();
          restart();

          handleAnalytics("action", "result_restart", null);

          removeLocalStore("mid");
          removeLocalStore("user");
          if (!uxGender) removeLocalStore("gender");

          if (localStorage.getItem("user")) {
            localStorage.setItem("user", JSON.stringify(user));
          }
        }}
        style={{
          backgroundColor:
            brandDefined?.name === "SRP"
              ? "transparent"
              : (resultScreenUI.restartCTA
                  .backgroundColor as React.CSSProperties["backgroundColor"]),
          fontWeight: resultScreenUI.restartCTA
            .fontWeight as React.CSSProperties["fontWeight"],
          fontSize:
            isMobile && brandDefined?.name === "SRP"
              ? "12px"
              : (resultScreenUI.restartCTA
                  .fontSize as React.CSSProperties["fontSize"]),
          color: resultScreenUI.restartCTA
            .fontColor as React.CSSProperties["color"],
          textTransform:
            skipTextTransform === "capitalize" ? "none" : skipTextTransform,
          borderRadius: resultScreenUI.restartCTA
            .borderRadius as React.CSSProperties["borderRadius"],
          border: resultScreenUI.restartCTA.borderWidth
            ? `${resultScreenUI.restartCTA.borderWidth} solid ${resultScreenUI.restartCTA.borderColor}`
            : "none",
          textDecoration: resultScreenUI.restartCTA
            .textDecoration as React.CSSProperties["borderRadius"],
          height: resultScreenUI.restartCTA.borderWidth ? "50px" : "auto",
          alignItems: "center",
          marginBottom: isMobile ? "20px" : !isMobile ? "50px " : 0,
          marginTop: getMarginTopSkip(),
          letterSpacing: (resultScreenUI.restartCTA as any)?.letterSpacing,
          fontStyle: "normal",
        }}
      >
        {skipTextTransform === "capitalize"
          ? capitalizeFirstLetter(t("skip"))
          : t("skip")}
      </span>
      {(brandDefined?.name === "Soeur" || brandDefined?.name === "Weill") &&
        !isSizeUnavailable && (
          <img
            src={
              brandDefined?.name === "Weill" ? example_tailes_2 : example_tailes
            }
            alt="example_tailes"
            style={{
              position: "relative",
              width: "100%",
              height: "auto",
              marginBottom: !isMobile ? "70px" : 0,
            }}
          />
        )}
      {brandDefined?.name !== "Lacoste" && <MeasuredBy step={step} />}
    </div>
  );
};

export default Result;
