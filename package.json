{"name": "drawer", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^6.0.0", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@sentry/cli": "^2.43.1", "@sentry/react": "^9.15.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "i18next": "^23.14.0", "i18next-browser-languagedetector": "^8.0.0", "lottie-react": "^2.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.0.1", "react-qr-code": "^2.0.15", "react-responsive": "^10.0.0", "react-scripts": "5.0.1", "scss": "^0.2.4", "styled-components": "^6.1.12", "typescript": "^4.4.2", "vercel": "^42.3.0", "web-vitals": "^2.1.0", "zod": "^3.23.8"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && yarn sentry:sourcemaps", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "prepare": "husky", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org kleep-rg --project drawer ./build && sentry-cli sourcemaps upload --org kleep-rg --project drawer ./build", "test:e2e": "npx playwright test --ui"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@playwright/test": "^1.53.1", "eslint": "8.57.0", "husky": "^9.1.7", "lint-staged": "^15.5.0"}}