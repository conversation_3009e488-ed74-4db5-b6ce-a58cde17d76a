import { FC, useState } from "react";

import Stepper from "../molecules/Stepper/Stepper";
import BellyChoice from "../BellyChoice";
import MeasuredBy from "../molecules/MeasuredBy";

import { useTranslation } from "react-i18next";

import { GENDERS, LANGUAGE_RULES, MODAL_STEPS } from "../../constants/modal";

import сuisses_1_male from "../../assets/questionnaire/сuisses/сuisses-1-male.svg";
import сuisses_2_male from "../../assets/questionnaire/сuisses/сuisses-2-male.svg";
import сuisses_3_male from "../../assets/questionnaire/сuisses/сuisses-3-male.svg";

import ogier_modern_1_male from "../../assets/questionnaire/сuisses/ogier/M-CUISSE_1.svg";
import ogier_modern_2_male from "../../assets/questionnaire/сuisses/ogier/M-CUISSE_2.svg";
import ogier_modern_3_male from "../../assets/questionnaire/сuisses/ogier/M-CUISSE_3.svg";

import PhotoCameraIcon from "../../assets/icons/PhotoCameraIcon";

import {
  titleStyles,
  disabledStylesContinue,
  activeStylesContinue,
  scanCTAStyle,
} from "../../configs";
import {
  capitalizeFirstLetter,
  getLangBase,
  isEmptyCTAStyle,
  useIsMobile,
  useKeyNavigation,
} from "../../utils";

import { findBrandByDomain } from "../../configs/configLoader";
import { useUserContext } from "../../store/userContext";
import { hoverStylesContinue } from "../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";

import "./index.css";

interface IPropsStepCuisses {
  step: any;
  gender: string;
  nextStep: () => void;
  setStep: (
    value: React.SetStateAction<{
      number: number;
    } | null>
  ) => void;
  setPreviousStep: React.Dispatch<
    React.SetStateAction<{
      number: number;
    } | null>
  >;
  value: {
    current: number;
  };
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
}

const StepCuisses: FC<IPropsStepCuisses> = ({
  step,
  gender,
  nextStep,
  setStep,
  setPreviousStep,
  value,
  subtitleStyles,
}) => {
  const isMobile = useIsMobile();
  const [selected, setSelected] = useState<number | undefined>(value.current);

  const userContext = useUserContext() || null;

  const { t } = useTranslation("components/cuisses");
  const { t: torsoTranslation } = useTranslation("components/torso");

  const [hovered, setHovered] = useState(false);

  const brandDefined = findBrandByDomain();

  const urlParameters = new URLSearchParams(window.location.search);

  const lang = urlParameters.get("lang");
  const market = urlParameters.get("market");

  const isIntersport = brandDefined?.name === "Intersport";
  const isVB = brandDefined?.name === "Victoria Beckham";
  const isSRP = brandDefined?.name === "SRP";
  const isHast = brandDefined?.name === "Hast";
  const isKenzo = brandDefined?.name === "Kenzo";
  const isLacoste = brandDefined?.name === "Lacoste";
  const isTheodore = brandDefined?.name === "Theodore";

  const langBase = market
    ? getLangBase(market)
    : lang
    ? getLangBase(lang)
    : null;

  const langRules =
    LANGUAGE_RULES[langBase as keyof typeof LANGUAGE_RULES] ||
    LANGUAGE_RULES.default;

  const scanTextTransform =
    scanCTAStyle.textTransform as React.CSSProperties["textTransform"];

  const continueButtonStyles = !selected
    ? disabledStylesContinue
    : activeStylesContinue;

  const cuissesStyles = {
    border: `1px solid ${scanCTAStyle.borderColor}`,
    borderWidth: isLacoste ? "1px 0 1px 0" : scanCTAStyle.borderWidth,
    borderRadius: scanCTAStyle.borderRadius,
    borderColor: scanCTAStyle.borderColor,
    fontSize: scanCTAStyle.fontSize,
    fontWeight: scanCTAStyle.fontWeight,
    color: scanCTAStyle.fontColor,
    textTransform:
      scanTextTransform === "capitalize" ? "none" : scanTextTransform,
    marginTop: isMobile ? "10px" : "0px",
    letterSpacing: (scanCTAStyle as any)?.letterSpacing,
  };

  const maleSilhouettesMap: Record<string, (string | null)[]> = {
    Ogier: [
      null,
      ogier_modern_1_male,
      ogier_modern_2_male,
      ogier_modern_3_male,
    ],
    Default: [null, сuisses_1_male, сuisses_2_male, сuisses_3_male],
  };

  const handleMaleImage = (item: number) => {
    const defaultSet = maleSilhouettesMap["Default"] ?? [];
    const relatedSilhouettes =
      maleSilhouettesMap[brandDefined?.name || "Default"] || defaultSet;

    return (
      relatedSilhouettes[item] ?? relatedSilhouettes[1] ?? defaultSet[1] ?? ""
    );
  };

  useKeyNavigation({
    selected,
    setSelected,
    valueRef: value,
    onEnter: nextStep,
  });

  const CTAStyles: React.CSSProperties = {
    ...continueButtonStyles,
    ...(isIntersport && hovered && selected ? hoverStylesContinue : {}),
    marginTop: "20px",
    textTransform:
      continueButtonStyles.textTransform === "capitalize"
        ? "none"
        : continueButtonStyles.textTransform,
    padding: isVB ? "7px" : "16px",
    height: isVB ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && selected ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && selected ? 1 : 0,
    transform: hovered && selected ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <>
      <div
        className="belly-description"
        style={{
          ...subtitleStyles,
          textAlign: titleStyles.textAlign as React.CSSProperties["textAlign"],
          justifyContent:
            titleStyles.textAlign as React.CSSProperties["justifyContent"],
          textTransform:
            subtitleStyles.textTransform === "capitalize"
              ? "none"
              : (subtitleStyles.textTransform as React.CSSProperties["textTransform"]),
        }}
      >
        <p
          style={{
            margin: 0,
          }}
        >
          {subtitleStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("description"))
            : t("description")}
        </p>
      </div>
      <div
        className="belly"
        style={{
          marginTop: isMobile ? "20px" : "40px",
          gap: isMobile ? "10px" : "30px",
        }}
      >
        <div className="body">
          <BellyChoice
            image={handleMaleImage(1)}
            onClick={() => {
              value.current = 1;
              setSelected(1);
            }}
            text={t("size.one")}
            isSelected={selected === 1}
            type="cuisses"
          />
          <BellyChoice
            image={handleMaleImage(2)}
            onClick={() => {
              value.current = 2;
              setSelected(2);
            }}
            text={t("size.two")}
            isSelected={selected === 2}
            type="cuisses"
          />
          <BellyChoice
            image={handleMaleImage(3)}
            onClick={() => {
              value.current = 3;
              setSelected(3);
            }}
            text={t("size.three")}
            isSelected={selected === 3}
            type="cuisses"
          />
        </div>
        <div className="controls">
          {!isSRP && !isVB && (
            <Stepper
              stepsNum={isHast ? 4 : gender === GENDERS.M ? 3 : 2}
              step={3}
              key={1}
            />
          )}
          <button
            type="button"
            disabled={!selected}
            className={`continue-button step-belly-button`}
            onMouseEnter={(e) => {
              setHovered(true);
              if (selected) {
                Object.assign(e.currentTarget.style, hoverStylesContinue);
              }
            }}
            onMouseLeave={(e) => {
              setHovered(false);
              if (selected) {
                Object.assign(e.currentTarget.style, continueButtonStyles);
              }
            }}
            onClick={() => nextStep()}
            style={CTAStyles}
          >
            <div
              style={{ display: "inline-flex", alignItems: "center", gap: 0 }}
            >
              {isTheodore
                ? t("continue")
                : continueButtonStyles.textTransform === "capitalize"
                ? capitalizeFirstLetter(
                    isHast ? torsoTranslation("continue") : t("continue")
                  )
                : isHast
                ? torsoTranslation("continue")
                : t("continue")}

              {isIntersport && (
                <span style={arrowContainerStyle} aria-hidden>
                  <ChevronRight style={iconStyle} />
                </span>
              )}
            </div>
          </button>

          {!!scanCTAStyle &&
          !isEmptyCTAStyle(scanCTAStyle) &&
          !(isKenzo && langRules?.unit === "imperial") ? (
            <div
              style={{
                display: "flex",
                width: "calc(100% - 3px)",
                height: "auto",
                maxHeight: "48px",
                backgroundColor: "#FFFFFF",
                cursor: "pointer",
                padding: isMobile ? "11px 0" : "12px 0",
                ...cuissesStyles,
              }}
              className="scan-CTA-button"
              onClick={async () => {
                if (userContext) {
                  const resultNewMeasure = await userContext.createNewMeasure(
                    "scan"
                  );
                  if (resultNewMeasure) {
                    setStep(MODAL_STEPS.QR_CODE);
                    setPreviousStep(MODAL_STEPS.CUISSES);
                  }
                }
              }}
            >
              <div
                style={{
                  display: "flex",
                  gap: "10px",
                  textDecoration:
                    scanCTAStyle.fontStyle === "underline"
                      ? "none"
                      : scanCTAStyle.fontStyle,
                  alignItems: "center",
                  width: "100%",
                  justifyContent: "center",
                  padding: !isMobile ? 0 : "0 24px",
                }}
              >
                <div
                  style={{
                    minWidth: "30px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <PhotoCameraIcon fill={scanCTAStyle.fontColor} />
                </div>
                <span
                  style={{
                    fontSize: isMobile
                      ? `calc(${scanCTAStyle.fontSize} - 1px`
                      : scanCTAStyle.fontSize,
                    letterSpacing: (scanCTAStyle as any)?.letterSpacing,
                    wordBreak: "break-word",
                  }}
                  className={`${
                    scanCTAStyle.fontStyle === "underline"
                      ? "scan-text underline"
                      : ""
                  }`}
                >
                  {scanTextTransform === "capitalize"
                    ? capitalizeFirstLetter(
                        t("scan", { ns: "components/belly" })
                      )
                    : t("scan", { ns: "components/belly" })}
                </span>
              </div>
            </div>
          ) : null}
        </div>
        <MeasuredBy step={step} />
      </div>
    </>
  );
};

export default StepCuisses;
