import { API_ENDPOINT, GENDERS } from "../constants/modal";
import {
  IPushAnalytics,
  IPushAnalyticsResponse,
  IQuestionsPushBody,
  IUserLocation,
  IUserPushAnalytics,
  IVisionCheckBody,
} from "../types/analytics";
import { logger } from "../utils/logging";
import { postSendMid, postSendUid } from "../utils/post";
import {
  questionMalePushAnswersSchema,
  questionFemalePushAnswersSchema,
} from "../validation/pushAnswers";

type MeasureType = "question" | "scan";

const uuidRegex =
  /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

const urlParameters = new URLSearchParams(window.location.search);
const mock = urlParameters.get("mock") === "true";
const isLingerie = urlParameters.get("lingerie") === "true";
const market = urlParameters.get("market");

export const isOnline = () => {
  return navigator.onLine;
};

export const newUser = async (domain: string, retries = 3, delay = 50) => {
  logger.log("Endpoint /new-user called");

  let attempt = 0;

  if (mock) return;

  if (!isOnline()) {
    attempt++;
    if (attempt > retries) {
      logger.log("No internet connection after retries");
    }
    await new Promise((resolve) => setTimeout(resolve, delay));
  }

  if (domain) {
    while (attempt <= retries) {
      try {
        localStorage.setItem("domain", domain);

        const response = await fetch(`${API_ENDPOINT}new-user`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            retailer: domain,
            device_id: "abcdefghiju",
          }),
        });

        if (!response.ok) {
          return false;
        }

        const res = await response.json();

        const uid = res["user_id"];
        if (uid && uid !== null && uid !== undefined) {
          localStorage.setItem("uid", uid);
          postSendUid(uid);
          return uid;
        } else {
          throw new Error("No user_id in response");
        }
      } catch (error) {
        attempt++;
        if (attempt > retries) {
          throw error;
        }
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  } else {
    logger.warn("Missing domain in parameters");
  }
};

export const recommend = async (
  pid: string | null,
  productStockData: any,
  variantId: string | null,
  variantCol: string | null,
  retries = 3,
  delay = 50
): Promise<any> => {
  logger.log("Endpoint /recommend called");

  let attempt = 0;

  if (pid) {
    while (attempt <= retries) {
      try {
        if (!isOnline()) {
          attempt++;
          if (attempt > retries) {
            logger.log("No internet connection after retries");
            return [];
          }
          await new Promise((resolve) => setTimeout(resolve, delay));
          continue;
        }

        const mid = localStorage.getItem("mid") || null;

        if (mid && uuidRegex.test(mid)) {
          const urlParameters = new URLSearchParams(window.location.search);

          let variant_id = variantId || urlParameters.get("variantId") || null;
          const domain = urlParameters.get("domain");
          if (!variant_id && domain === "asphalte.com" && productStockData) {
            const asphalteColor =
              variantCol || urlParameters.get("asphalteColor") || null;
            if (!asphalteColor) {
              variant_id = productStockData[0].variant_reference;
            } else {
              const filteredStocks = productStockData.filter(
                (item: any) => item?.color === asphalteColor
              );
              if (filteredStocks?.length > 0)
                variant_id = filteredStocks[0].variant_reference;
            }
          }

          const response = await fetch(
            `${API_ENDPOINT}recommend?version=1.0.4`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                measure_public_id: mid,
                product_reference: pid,
                ...(variant_id && { variant_reference_active: variant_id }),
              }),
            }
          );

          if (!response.ok) {
            logger.log(`Server error: ${response.status}`);
            return [];
          }

          const res = await response.json();

          const isRecoExist = !!res?.recommendation;

          if (isRecoExist) {
            const uid = localStorage.getItem("uid");
            const mid = localStorage.getItem("mid");
            if (uid && uid !== null && uid !== undefined) {
              postSendUid(uid);
            }
            postSendMid(mid);
            return {
              success: isRecoExist,
              data: res?.recommendation,
            };
          } else {
            logger.log("No recommendation available");
            return [];
          }
        } else {
          return [];
        }
      } catch (error: any) {
        attempt++;
        logger.error(`Attempt ${attempt} failed: ${error.message}`);
        if (attempt > retries) {
          return [];
        }

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  } else {
    logger.warn("Recommendation uncomputable: no pid is found");
  }

  return [];
};

export const newMeasure = async (
  measure_type: MeasureType,
  user_id?: string,
  retailer_customer_tracking_id?: string | null,
  retries = 3,
  delay = 50
) => {
  logger.log("Endpoint /new-measure called", measure_type);

  let attempt = 0;

  if (mock) return true;

  const uid = localStorage.getItem("uid") || user_id || null;

  if (!uid || !uuidRegex.test(uid)) {
    logger.log("Invalid or missing user ID");
    return false;
  }

  if (uid && measure_type) {
    while (attempt <= retries) {
      try {
        const response = await fetch(`${API_ENDPOINT}new-measure`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: uid,
            measure_type: measure_type,
            retailer_customer_tracking_id,
          }),
        });

        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }

        const res = await response.json();

        const mid = res["measure_id"];
        if (mid) {
          if (uid && uid !== null && uid !== undefined) {
            postSendUid(uid);
          }
          return mid;
        } else {
          throw new Error("No measure_id in response");
        }
      } catch (error: any) {
        attempt++;

        if (attempt > retries) {
          return false;
        }

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  return false;
};

export const scanCheckMeasurements = async () => {
  logger.log("Endpoint /scan-check-measurements called");

  const mid = localStorage.getItem("mid") || null;
  if (!mid) {
    return false;
  }

  try {
    const response = await fetch(`${API_ENDPOINT}scan-check-measurements`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        measure_id: mid,
      }),
    });

    if (!response.ok) {
      logger.log(`Server error: ${response.status}`);
    }

    const res = await response.json();

    return res["completed"] ?? false;
  } catch (error: any) {
    if (
      error.message.includes("Failed to fetch") ||
      error.message.includes("No internet connection")
    ) {
      throw error;
    }

    return false;
  }
};

export const visionCheck = async (user_id: string) => {
  logger.log("Endpoint /shoe/measure/vision/check called");

  if (!user_id) return;

  try {
    const response = await fetch(`${API_ENDPOINT}shoe/measure/vision/check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        user: {
          id: user_id,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    return await response.json();
  } catch (error: any) {
    if (
      error.message.includes("Failed to fetch") ||
      error.message.includes("No internet connection")
    ) {
      throw error;
    }
    logger.log(error);
    throw new Error("Unexpected error in visionCheck");
  }
};

export const questionPushAnswers = async (
  answers: object,
  isSpecificProject?: boolean
) => {
  logger.log("Endpoint /question-push-trigger called");

  if (mock) return;

  const userId = localStorage.getItem("uid");
  const mid = localStorage.getItem("mid") || null;

  if (mid && uuidRegex.test(mid)) {
    const body = {
      measure_id: mid,
      answers: answers,
    };

    const specificBody = {
      user: {
        id: userId || "",
      },
      answers: answers,
    };

    try {
      if (!isSpecificProject) {
        const gender = (answers as any).gender;
        if (gender === GENDERS.M) {
          questionMalePushAnswersSchema.parse(body);
        } else if (gender === GENDERS.F) {
          questionFemalePushAnswersSchema.parse(body);
        } else {
          throw new Error("Invalid gender value");
        }
      }

      const url = isSpecificProject
        ? `${API_ENDPOINT}measure/${
            isLingerie ? "lingerie" : "children"
          }/push-trigger-question`
        : `${API_ENDPOINT}question-push-trigger`;

      const success = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(isSpecificProject ? specificBody : body),
      })
        .then((res) => res.json())
        .then((res) => res)
        .catch((e) => {
          logger.log(`Error: questionPushAnswersAndTrigger - ${e}`);
          return false;
        });

      return !!success;
    } catch (error) {
      logger.error(`Validation Error: ${error}`);
      return false;
    }
  } else {
    return false;
  }
};

export const checkStocks = async (
  domain: string,
  pid: string | null,
  brandName?: string | undefined
) => {
  logger.log("Endpoint /stocks called");

  if (mock) return;

  if (pid) {
    const success = await fetch(`${API_ENDPOINT}stocks`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        retailer_domain: domain,
        product_reference: pid,
        market: brandName === "Zadig & Voltaire" ? market : undefined,
      }),
    })
      .then((res) => res.json())
      .then((res) => {
        if (res?.data) {
          return res?.data;
        } else {
          return null;
        }
      })
      .catch((e) => {
        logger.log(`Error: recommend -  ${e}`);
        return null;
      });
    return success;
  } else {
    return false;
  }
};

export const findSimilarProducts = async (
  domain: string,
  pid: string,
  variant_id: string | null
) => {
  logger.log("Endpoint /similar-products called");

  if (mock) return;

  const lang = urlParameters.get("lang");
  const country_code =
    urlParameters.get("countryCode") || urlParameters.get("market");

  const success = await fetch(`${API_ENDPOINT}similar-products`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      retailer: domain,
      product_reference: pid,
      variant_reference: variant_id,
      lang: lang || "fr",
      ...(country_code && { country_code: country_code }),
    }),
  })
    .then((res) => res.json())
    .then((res) => {
      if (res) {
        return res;
      } else {
        return null;
      }
    })
    .catch((e) => {
      logger.log(`Error: recommend -  ${e}`);
      return null;
    });
  return success;
};

const getDeviceInfo = () => {
  const type: "mobile" | "tablet" | "desktop" =
    window.innerWidth < 768
      ? "mobile"
      : window.innerWidth < 1024
      ? "tablet"
      : "desktop";
  const model = navigator.userAgent;
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;

  return { type, model, screenWidth, screenHeight };
};

const getBrowserInfo = () => {
  const type = navigator.userAgent;
  const lang = navigator.language || navigator.languages[0];

  return { type, lang };
};

const CACHE_EXPIRATION_TIME = 24 * 60 * 60 * 1000; // 24 hours

const getLocationInfo = async (): Promise<IUserLocation> => {
  const cachedLocation = localStorage.getItem("userLocation");
  const cachedTimestamp = localStorage.getItem("locationCacheTimestamp");

  if (cachedLocation && cachedTimestamp) {
    const cacheAge = Date.now() - parseInt(cachedTimestamp, 10);

    if (cacheAge < CACHE_EXPIRATION_TIME) {
      return JSON.parse(cachedLocation);
    }
  }

  try {
    // const response = await fetch("https://ipinfo.io/*************?token=3f61147e938fcd");
    // const locationData = await response.json();

    // const userLocation: IUserLocation = {
    //   ip: locationData.ip || "unknown",
    //   country: locationData.country || "unknown",
    //   region: locationData.region || "unknown",
    //   timezone: locationData.timezone || "unknown",
    // };

    // localStorage.setItem("userLocation", JSON.stringify(userLocation));
    // localStorage.setItem("locationCacheTimestamp", Date.now().toString());

    const userLocation: IUserLocation = {
      ip: "unknown",
      country: "unknown",
      region: "unknown",
      timezone: "unknown",
    };

    return userLocation;
  } catch (error) {
    logger.log(`Failed to fetch location data: ${error}`);

    return {
      ip: "unknown",
      country: "unknown",
      region: "unknown",
      timezone: "unknown",
    };
  }
};

export const pushAnalytics = async ({
  event_type,
  event_name,
  data,
}: IPushAnalytics) => {
  logger.log("Endpoint /analytics/push called");

  const device = getDeviceInfo();
  const browser = getBrowserInfo();
  const location = await getLocationInfo();

  const userInfo: IUserPushAnalytics = {
    device,
    browser,
    location,
  };

  const domain = localStorage.getItem("domain");
  const mid = localStorage.getItem("mid");
  const userId = localStorage.getItem("uid");

  if (!domain)
    logger.warn(`Cannot track event '${event_name}': no domain defined`);
  else {
    const hostname = window.location.hostname;
    const is_test = hostname.includes("localhost") || hostname.includes("dev");

    const bodyInfo: IPushAnalyticsResponse = {
      event: event_name,
      session_id: "",
      user_id: userId,
      measure_id: mid,
      customer_id: null,
      retailer: domain,
      timestamp: Date.now(),
      data: {
        is_test,
        event_type,
        event_name,
        user: userInfo,
        data,
      },
    };

    try {
      const response = await fetch(`${API_ENDPOINT}analytics/push`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(bodyInfo),
      });

      if (!response.ok) {
        logger.log(`Server error: ${response.status}`);
      }

      const res = await response.json();

      return res["completed"] ?? false;
    } catch (error: any) {
      if (
        error.message.includes("Failed to fetch") ||
        error.message.includes("No internet connection")
      ) {
        logger.error(`Network error occurred: ${error.message}`);
        return false;
      }

      logger.error(`Unexpected error occurred: ${error}`);
      return false;
    }
  }
};

export const pushGtag = async ({
  event_type,
  event_name,
  data,
}: IPushAnalytics) => {
  const device = getDeviceInfo();
  const browser = getBrowserInfo();
  const location = await getLocationInfo();

  const userInfo: IUserPushAnalytics = {
    device,
    browser,
    location,
  };

  const domain = localStorage.getItem("domain");
  const mid = localStorage.getItem("mid");
  const userId = localStorage.getItem("uid");

  if (!domain)
    logger.warn(`Cannot track event '${event_name}': no domain defined`);
  else {
    const hostname = window.location.hostname;
    const is_test = hostname.includes("localhost") || hostname.includes("dev");

    const bodyInfo: IPushAnalyticsResponse = {
      event: event_name,
      session_id: "",
      user_id: userId,
      measure_id: mid,
      customer_id: null,
      retailer: domain,
      timestamp: Date.now(),
      data: {
        is_test,
        event_type,
        event_name,
        user: userInfo,
        data,
      },
    };

    try {
      window.gtag("event", event_name, bodyInfo);
      return true;
    } catch (error: any) {
      logger.error(`[Kleep/gTag] Unexpected error occurred: ${error}`);
      return false;
    }
  }
};

export const shoeChildrenRecommend = async (
  product_id: string,
  isChildrenProduct: boolean
) => {
  const user_id = localStorage.getItem("uid");

  try {
    const url = isChildrenProduct
      ? `${API_ENDPOINT}recommendation/children/recommend`
      : `${API_ENDPOINT}shoe/recommendation/recommend`;

    const body = {
      data: {
        user: {
          id: user_id,
        },
        product: {
          reference: product_id,
        },
      },
    };

    if (user_id) {
      const success = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(isChildrenProduct ? body?.data : body),
      })
        .then((res) => res.json())
        .then((res) => {
          logger.log("shoeChildrenRecommend", res);
          return !res?.success
            ? false
            : {
                success: res?.success,
                data: res?.data?.recommendation,
              };
        })
        .catch((e) => {
          logger.log("Error: shoeChildrenRecommend", e);
          return false;
        });
      return success;
    } else {
      return false;
    }
  } catch (error) {
    logger.error(`Error with shoeChildrenRecommend: ${error}`);
    return false;
  }
};

export const visionTrigger = async (userObject: IVisionCheckBody) => {
  try {
    if (userObject?.user?.id) {
      const success = await fetch(
        `${API_ENDPOINT}shoe/measure/vision/trigger`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(userObject),
        }
      )
        .then((res) => res.json())
        .then((res) => res?.success)
        .catch((e) => {
          logger.log("Error: visionTrigger - ", e);
          return false;
        });
      return success;
    } else {
      return false;
    }
  } catch (error) {
    logger.error(`Error with visionTrigger: ${error}`);
    return false;
  }
};

export const visionInit = async (user_id: string) => {
  try {
    if (user_id) {
      const success = await fetch(`${API_ENDPOINT}shoe/measure/vision/init`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user: {
            id: user_id,
          },
        }),
      })
        .then((res) => res.json())
        .then((res) => res)
        .catch((e) => {
          logger.log("Error: visionInit -", e);
          return false;
        });
      return success;
    } else {
      return false;
    }
  } catch (error) {
    logger.error(`Error with visionInit: ${error}`);
    return false;
  }
};

export const questionPushTrigger = async (userObject: IQuestionsPushBody) => {
  try {
    if (userObject?.user?.id) {
      const success = await fetch(
        `${API_ENDPOINT}shoe/measure/question/push-trigger`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(userObject),
        }
      )
        .then((res) => res.json())
        .then((res) => res?.success)
        .catch((e) => {
          logger.log("Error: questionPushTrigger - ", e);
          return false;
        });
      return success;
    } else {
      return false;
    }
  } catch (error) {
    logger.error(`Error with questionPushTrigger: ${error}`);
    return false;
  }
};
