const RotatedArrowIcon = ({ open }: { open: boolean }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 10 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        transform: open ? "rotate(0deg)" : "rotate(180deg)",
        transition: "transform 0.3s ease",
        display: "block",
        marginLeft: "6px",
        position: "relative",
        right: "10px",
      }}
    >
      <path d="M5 0.5L10 5.5L0 5.5L5 0.5Z" fill="#2E2E2E" />
    </svg>
  );
};

export default RotatedArrowIcon;
