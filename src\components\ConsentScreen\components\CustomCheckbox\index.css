.consent {
  outline: none;
}

.consent .consent-description {
  display: flex;
  flex-direction: column;
  margin-top: 30px;
  max-width: 400px;
}

.consent .consent-description p {
  text-align: left;
  font-size: 13px;
  font-weight: 400;
  margin: 0;
  color: #2e2e2e;
}

.consent .check-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
  max-width: 400px;
}

.consent .check-container p {
  font-size: 13px;
  font-weight: 400;
  margin: 0;
  color: #2e2e2e;
}

@media (max-width: 767px) {
  .consent .check-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0;
  }
}
