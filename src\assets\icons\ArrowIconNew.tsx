import { SvgIcon, SvgIconProps } from "@mui/material";

const ArrowIconNew = (props: SvgIconProps) => (
  <SvgIcon
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.05806 5.55806C9.30214 5.31398 9.69786 5.31398 9.94194 5.55806L15.9419 11.5581C16.186 11.8021 16.186 12.1979 15.9419 12.4419L9.94194 18.4419C9.69786 18.686 9.30214 18.686 9.05806 18.4419C8.81398 18.1979 8.81398 17.8021 9.05806 17.5581L14.6161 12L9.05806 6.44194C8.81398 6.19786 8.81398 5.80214 9.05806 5.55806Z"
      fill="#272626"
    />
  </SvgIcon>
);

export default ArrowIconNew;
