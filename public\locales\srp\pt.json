{"components/intro": {"title": "Encontre o tamanho ideal para cada um dos seus itens graças a IA", "description": "Algumas perguntas para te conhecer melhor.", "topTitle": "<PERSON><PERSON><PERSON>", "steps": {"consent": "KENZO X KLEEP", "gender": "Encontre o tamanho ideal para cada um dos seus itens graças a IA", "intro_mobile": "Encontre o tamanho ideal para cada um dos seus itens graças a IA", "intro": "Encontre o tamanho ideal para cada um dos seus itens graças a IA", "device_select": "Encontre o tamanho ideal para cada um dos seus itens graças a IA", "device_select_shoe": "Encontre o tamanho ideal do seu calçado", "shoe_gender": "Por favor, especifique seu gênero", "shoe_sizes": "Qual tamanho de sapato você costuma usar?", "shoe_questions": "Qual é a largura dos seus pés?", "result_shoe": "<PERSON><PERSON> ta<PERSON><PERSON> de sapa<PERSON> ideal", "qr_code": "Encontre o tamanho ideal para cada um dos seus itens graças a IA", "qr_code_shoe": "Digitalização rápida e precisa", "lingerie_question_1": "Especifique o fecho que você mais usa", "lingerie_question_2": "Indique como se sente em relação à faixa sob o busto", "lingerie_question_3": "Indique como se sente em relação às taças", "lingerie_question_4": "Indique como se sente em relação às alças", "belly": "Escolha o formato da sua barriga", "torso": {"male": "Escolha o formato do seu peitoral", "female": "Escolha o formato do seu quadril"}, "cuisses": "Escolha o formato das suas coxas", "breasts": "Escolha o tamanho do seu busto", "error": "Ops, ocorreu um erro", "error_outOfRange": "Ihre Idealgröße konnte für dieses Kleidungsstück nicht ermittelt werden.", "result": "<PERSON><PERSON><PERSON> recomendado", "unavailable": "<PERSON>u tamanho recomendado está fora de estoque", "antibracketing": "DÚVIDA SOBRE O TAMANHO?"}, "gender": {"title": "Você é", "description": "<PERSON>so nos ajuda a entender melhor o seu tamanho", "male": "<PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "children": {"male": "Rapaz", "female": "<PERSON><PERSON><PERSON>"}}, "height": {"title": "Altura*", "mobiletitle": "<PERSON><PERSON><PERSON>", "variation": "ALTURA*", "unit": "cm", "errorcm": "Por favor, indique sua altura.", "errorfeet": "Por favor, indique sua altura.", "placeholder": "ex: 160", "placeholderfeet": "ex: 5'", "placeholderinches": "ex: 9\"", "children": {"placeholder": "ex: 105", "placeholderfeet": "ex: 3'", "placeholderinches": "ex: 4\""}}, "weight": {"title": "Peso*", "variation": "PESO*", "unit": "kg", "errorkg": "Por favor, indique seu peso.", "errorlbs": "Por favor, indique seu peso.", "placeholderKG": "ex: 60", "placeholderLBS": "ex: 154", "children": {"placeholderKG": "ex: 16", "placeholderLBS": "ex: 35"}}, "age": {"title": "Idade*", "variation": "Idade*", "unit": "ans", "error": "Por favor, indique sua idade.", "placeholder": "ex: 30 anos", "placeholdermonths": "ex: 12", "children": {"placeholder": "ex: 4 anos", "year": "<PERSON><PERSON>", "month": "Meses"}}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "pp_part1": "<PERSON>o continuar, você concorda em", "pp_link": "<PERSON><PERSON> continua<PERSON>, você aceita nossa <a href=\"https://kleep.ai/privacy-policy\">Política de Privacidade</a>."}, "components/results/result": {"mark1": "<PERSON><PERSON> a<PERSON>", "mark2": "<PERSON><PERSON><PERSON> perfeito", "size": {"title": "<PERSON><PERSON><PERSON>", "size1": "<PERSON>s justo", "size2": "Ideal", "size3": "Caimento mais largo", "variations": {"size1": "Regular", "size2": "Oversized", "size3": "<PERSON><PERSON><PERSON> perfeito", "size4": "<PERSON><PERSON> a<PERSON>", "size5": "Levemente apertado", "size6": "<PERSON><PERSON>", "size7": "Deliberadamente largo", "size8": "Levemente solto", "size9": "Solto", "size10": "<PERSON><PERSON> solto"}}, "description": {"shoe": {"normal": {"ideal": "Clientes com o mesmo comprimento e largura do pé compraram o tamanho [S] e ficaram satisfeitos.", "not_possible": ""}, "size_up": {"ideal": "Escolha o tamanho [S] para mais conforto.", "not_possible": "Com base na morfologia do seu pé, o tamanho [S] pode ser grande demais para você."}, "size_down": {"ideal": "Escolha o tamanho [S] para mais suporte.", "not_possible": "Com base na morfologia do seu pé, o tamanho [S] pode ser pequeno demais para você."}}, "normal": {"ideal": "Milhares de cliente com a sua silhueta compraram o tamanho [S] desse item e ficaram satisfeitos.", "not_possible_up": "Não foi possível identificar o seu tamanho ideal: com base na sua morfologia, o tamanho [S] será muito grande.", "not_possible_down": "Não foi possível identificar o seu tamanho ideal: com base na sua morfologia, o tamanho [S] será muito pequeno.", "not_possible_secondary": "Recomendamos que visite uma das nossas lojas para obter apoio personalizado."}, "size_up": {"ideal": "Para um ajuste <b>mais solto</b>, esco<PERSON><PERSON> o <b>ta<PERSON><PERSON> [<PERSON>]</b>.", "not_possible": "Com base na sua morfologia, o <b>ta<PERSON><PERSON> [S]</b> será muito grande. Recomendamos escolher um tamanho menor."}, "size_down": {"ideal": "Para um ajuste <b>mais justo</b>, esco<PERSON>ha o <b>ta<PERSON><PERSON> [<PERSON>]</b>.", "not_possible": "Com base na sua morfologia, o <b>ta<PERSON><PERSON> [S]</b> será muito pequeno. Recomendamos escolher um tamanho maior."}}, "result": {"title": "Ajuste regular", "description": "Perfeito equilíbrio entre estilo e conforto para um visual esportivo e casual."}, "unavailable": {"description": "Infelizmente, o tamanho selecionado está fora de estoque.", "title": "Produtos similares disponíveis neste tamanho :"}, "button": "<PERSON><PERSON><PERSON><PERSON> [<PERSON>] ao car<PERSON>ho", "skip": "Tentar de novo"}}