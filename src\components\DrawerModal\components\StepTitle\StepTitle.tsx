import { useTranslation } from "react-i18next";
import { findBrandByDomain } from "../../../../configs/configLoader";
import { MODAL_STEPS } from "../../../../constants/modal";
import { capitalizeFirstLetter, useIsMobile } from "../../../../utils";

import "./index.css";

interface StepTitleProps {
  step: any;
  isShoesProduct: boolean;
  isSRP: boolean;
  outOfRange: boolean;
  titleStyles: any;
  titleTextTransform: string;
  fontSizeValue: string;
  topValue: string;
  marginValue: string;
  MODAL_STEPS_TITLE: string[];
}

const StepTitle = ({
  step,
  isShoesProduct,
  isSRP,
  outOfRange,
  titleStyles,
  titleTextTransform,
  fontSizeValue,
  topValue,
  marginValue,
  MODAL_STEPS_TITLE,
}: StepTitleProps) => {
  const isMobile = useIsMobile();

  const { t } = useTranslation("components/intro");

  const brandDefined = findBrandByDomain();

  if (!step || isMobile || brandDefined?.name === "Victoria Beckham")
    return null;

  const OUT_OF_RANGE_TITLE = t("steps.error_outOfRange");

  const text =
    outOfRange && step?.number === MODAL_STEPS.ERROR.number
      ? OUT_OF_RANGE_TITLE.replace(/&nbsp;/g, "\u00A0")
      : titleTextTransform === "capitalize"
      ? capitalizeFirstLetter(MODAL_STEPS_TITLE[step?.number]).replace(
          /&nbsp;/g,
          "\u00A0"
        )
      : MODAL_STEPS_TITLE[step?.number].replace(/&nbsp;/g, "\u00A0");

  return (
    <h2
      data-testid="drawer-title"
      style={{
        textAlign: titleStyles.textAlign,
        fontWeight: titleStyles.fontWeight,
        textTransform:
          titleTextTransform === "capitalize"
            ? "none"
            : (titleTextTransform as React.CSSProperties["textTransform"]),
        color: isShoesProduct ? "#2E2E2E" : titleStyles.color,
        fontSize: fontSizeValue,
        minHeight: "25px",
        position: isSRP ? "absolute" : "relative",
        left: isSRP ? "25px" : "",
        width: isSRP ? "calc(100% - 40px)" : "100%",
        top: topValue,
        margin: marginValue,
        letterSpacing: titleStyles?.letterSpacing,
      }}
    >
      {text}
    </h2>
  );
};

export default StepTitle;
