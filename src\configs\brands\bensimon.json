{"name": "<PERSON><PERSON><PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "16px", "fontColor": "#3d4557", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#545454", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#3d4557", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#3d4557", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #3d4557"}}, "genderCTA": {"unfocused": {"backgroundColor": "#8b8f9a", "fontColor": "#000000", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#3d4557", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "5px", "right": "-11px", "activeColor": "#3d4557", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#3d4557"}}, "3": {"qrcode": {"backgroundColor": "#3d4557"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#3d4557", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#3d4557", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3d4557", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#d8d8d8", "borderWidth": "2px"}, "focused": {"backgroundColor": "#3d4557", "fontWeight": "400", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#3d4557", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#3d4557", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#3d4557", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#3d4557", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Fira Sans", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "capitalize", "color": "#3d4557", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "none", "color": "#3d4557", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#8b8f9a", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#3d4557", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#3d4557", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#3d4557", "borderRadius": "0", "fontSize": "12px", "fontWeight": "400", "fontColor": "#3d4557", "textTransform": "capitalize", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#3d4557", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3d4557", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#3d4557", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3d4557", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#3d4557"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#3d4557", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3d4557", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#3d4557", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3d4557", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#3d4557", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#3d4557", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#3d4557", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#3d4557", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}