{"name": "Rabanne", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "18px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#DDDDDD", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "5px", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "5px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "16px", "fontWeight": "400", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "5px", "borderColor": "", "borderWidth": "0px", "fontSize": "16px", "fontWeight": "400", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "16px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "5px", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "70px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "16px", "fontColor": "#000000", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "#DDDDDD", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "16px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "16px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "16px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#DDDDDD", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "<PERSON><PERSON>", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#000000", "fontSize": "24px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#000000", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#DDDDDD", "fontWeight": "600", "fontSize": "18px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#000000", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "18px", "textTransform": "uppercase", "borderRadius": "5px", "borderColor": "", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#000000", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "5px", "fontSize": "16px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#DDDDDD"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "16px", "fontWeight": "600", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "16px", "fontWeight": "600", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "textTransform": "uppercase", "textDecoration": "underline"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "", "fontSize": "16px"}}}}}