import { MODAL_STEPS } from "../constants/modal";
import { i18n } from "../i18n";
import "../i18n";

export const initializeLanguage = () => {
  const allowedLanguages = [
    "fr",
    "gb",
    "en",
    "de",
    "ja",
    "it",
    "pt",
    "ko",
    "es",
    "nl",
    "dk",
    "fi",
    "se",
    "pl",
  ];
  const defaultLanguage = "en";
  const url = new URL(window.location.href);
  const params = new URLSearchParams(window.location.search);

  const lang = params.get("lang")?.split("-")[0].toLowerCase();

  const langRes =
    (lang && allowedLanguages.includes(lang) && lang) || defaultLanguage;

  if (!params.has("lang")) {
    params.append("lang", langRes);
    url.search = params.toString();
    window.history.replaceState({}, "", url.toString());
  }

  i18n.changeLanguage(langRes);

  return langRes;
};

export const convertHeightToFeetAndInches = (heightInCm: string) => {
  const heightStr = heightInCm.toString();

  // If the input length is less than 2, pad with leading zero
  const paddedHeightStr = heightStr.padStart(2, "0");

  // Extract first digit as feet and second digit as inches
  const feetValue = paddedHeightStr[0];
  const inchesValue = paddedHeightStr[1];

  return { feetValue, inchesValue };
};

export const convertFeetAndInchesToCm = (
  feetValue: string,
  inchesValue: string
) => {
  // Combine feet and inches into a single string
  const heightStr = `${feetValue}${inchesValue}`;

  return heightStr;
};

export const INCHtoCM = (v: any) => {
  const [feet, inches] = v.split(" ").map(Number);
  return (feet * 30.48 + inches * 2.54).toFixed(2).toString();
};

export const LBStoKG = (v: number) => (v * 0.453592).toFixed(2).toString();

export const getStepMaps = (isMobile: boolean) => {
  const stepLabels = ["ABOUT YOU", "HIPS", "STOMACH", "CHEST", "YOUR SIZE"];

  const labelToKeyMap: Record<string, keyof typeof MODAL_STEPS> = {
    "ABOUT YOU": isMobile ? "INTRO_MOBILE" : "INTRO",
    HIPS: "BELLY",
    STOMACH: "TORSO",
    CHEST: "BREASTS",
    "YOUR SIZE": "RESULT",
  };

  const stepMap = stepLabels.map((label) => {
    const key = labelToKeyMap[label];
    const stepNumber = MODAL_STEPS[key].number;

    return { label, stepNumber };
  });

  return stepMap;
};

export const getInputStyles = ({
  id,
  isMobile,
  isAndroid,
  brandDefined,
  font,
  inputStyles,
  inputStyleSelector,
  borderColor,
}: {
  id: string;
  isMobile: boolean;
  isAndroid: boolean;
  brandDefined?: { name?: string } | null;
  font: string;
  inputStyles: React.CSSProperties;
  inputStyleSelector: { borderBottom?: string; border?: string };
  borderColor: string;
}): React.CSSProperties => {
  const isSRP = brandDefined?.name === "SRP";
  const isVB = brandDefined?.name === "Victoria Beckham";
  const isOtherBrand = !isSRP && !isVB;
  const isBlackstore = brandDefined?.name === "Blackstore";

  const marginBottom = !isMobile && isOtherBrand ? inputStyles.marginBottom : 0;

  const borderBottom =
    inputStyleSelector.borderBottom === "none"
      ? "none"
      : `1px solid ${borderColor}`;

  const border =
    inputStyleSelector.borderBottom === "none" ? "none" : inputStyles.border;

  const width = isMobile
    ? isVB
      ? "calc(100% - 32px)"
      : "calc(100% - 42px)"
    : "100%";

  const maxWidth =
    isBlackstore && !isMobile
      ? "calc(100% - 20px)"
      : isMobile && (isVB || isOtherBrand)
      ? isVB
        ? "calc(100% - 32px)"
        : "calc(100% - 42px)"
      : inputStyleSelector.borderBottom === "none" || isSRP || isVB
      ? "calc(100% - 20px)"
      : "100%";

  const fontFamily =
    !isAndroid && font !== "Futura" ? `${font}, sans-serif !important` : "";

  return {
    ...inputStyles,
    marginBottom,
    borderBottom,
    border,
    borderLeft: isVB && id === "inches" ? "none" : border,
    width,
    maxWidth,
    position: "relative",
    outline: "none",
    fontFamily,
  };
};

export const getSubdomainPrefixUpper = (domain: string): string => {
  const parts = domain.split(".");
  if (parts.length > 2) {
    return parts[0].toUpperCase();
  }
  return "";
};

export const getLangBase = (lang: string): string =>
  lang?.split(/[-/]/)[1]?.toLowerCase() ||
  lang?.split(/[-/]/)[0]?.toLowerCase() ||
  "default";

const brandsWithArrow = ["Blackstore"];

export const isWithArrow = (brandName: string | undefined) => {
  return brandName && brandsWithArrow.includes(brandName);
};

export const getCloseButtonSize = (
  isMobile: boolean,
  brandName: string | undefined,
  standartSize: { mobile: string; desktop: string },
  customSize: { mobile: string; desktop: string }
): string => {
  const isBlackstore = brandName === "Blackstore";
  const isIntersport = brandName === "Intersport";

  return isBlackstore || isIntersport
    ? isMobile
      ? customSize.mobile
      : customSize.desktop
    : isMobile
    ? standartSize.mobile
    : standartSize.desktop;
};
