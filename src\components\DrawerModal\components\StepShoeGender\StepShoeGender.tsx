import React, { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { findBrandByDomain } from "../../../../configs/configLoader";
import { capitalizeFirstLetter, useIsMobile } from "../../../../utils";
import {
  activeStylesContinue,
  disabledStylesContinue,
  font,
  welcomeScreenUI,
} from "../../../../configs";
import { GENDERS } from "../../../../constants/modal";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { hoverStylesContinue } from "../../../../configs/stylesLoader";
import GenderButton from "../../../StepIntroDesktop/components/GenderBlock/GenderButton";

import "./index.css";

interface IPropsStepShoeGender {
  selectedShoeGender: string | null;
  setSelectedShoeGender: React.Dispatch<React.SetStateAction<string | null>>;
  nextStep: () => void;
  subtitlesStyles: any;
}

type GenderButtonType = "focused" | "unfocused";

const StepShoeGender: FC<IPropsStepShoeGender> = ({
  selectedShoeGender,
  setSelectedShoeGender,
  nextStep,
  subtitlesStyles,
}) => {
  const isMobile = useIsMobile();
  const { t } = useTranslation("components/intro");
  const brandDefined = findBrandByDomain();

  const [hovered, setHovered] = useState<string | null>(null);
  const [hoveredCTA, setHoveredCTA] = useState(false);

  const getButtonsStyles = (type: GenderButtonType) => ({
    width: "100%",
    backgroundColor: welcomeScreenUI.genderCTA[type].backgroundColor,
    borderRadius: welcomeScreenUI.genderCTA[type].borderRadius,
    borderColor: welcomeScreenUI.genderCTA[type].borderColor,
    borderWidth: welcomeScreenUI.genderCTA[type].borderWidth,
    fontSize: welcomeScreenUI.genderCTA[type].fontSize,
    fontWeight: welcomeScreenUI.genderCTA[type].fontWeight,
    textTransform:
      brandDefined?.name === "Lacoste" && isMobile
        ? "uppercase"
        : (welcomeScreenUI.genderCTA[type]
            .textTransform as React.CSSProperties["textTransform"]),
    color: welcomeScreenUI.genderCTA[type].fontColor,
    fontFamily: `${font}, sans-serif${isMobile ? "" : " !important"}`,
    letterSpacing: (welcomeScreenUI.genderCTA[type] as any)?.letterSpacing,
  });

  const femaleButtonStyles =
    selectedShoeGender === GENDERS.F
      ? getButtonsStyles("focused")
      : getButtonsStyles("unfocused");

  const maleButtonStyles =
    selectedShoeGender === GENDERS.M
      ? getButtonsStyles("focused")
      : getButtonsStyles("unfocused");

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();

    if (e.key === "Tab") {
      e.preventDefault();

      const selectGender = !selectedShoeGender
        ? GENDERS.F
        : selectedShoeGender === GENDERS.F
        ? GENDERS.M
        : GENDERS.M;

      setSelectedShoeGender(selectGender);
    }
  };

  const handleFocus = () => {
    setSelectedShoeGender(GENDERS.F);
  };

  const genderMaleKey = "gender.male";
  const genderFemaleKey = "gender.female";

  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...(!selectedShoeGender ? disabledStylesContinue : activeStylesContinue),
    ...(isIntersport && hoveredCTA ? hoverStylesContinue : {}),
    marginTop: isMobile ? "60px" : "30px",
    position: "relative" as const,
    marginBottom: isMobile ? "20px" : "0",
    textTransform:
      activeStylesContinue.textTransform === "capitalize"
        ? "none"
        : (activeStylesContinue.textTransform as React.CSSProperties["textTransform"]),
    padding: brandDefined?.name === "Victoria Beckham" ? "7px" : "16px",
    height: brandDefined?.name === "Victoria Beckham" ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hoveredCTA && selectedShoeGender ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hoveredCTA && selectedShoeGender ? 1 : 0,
    transform:
      hoveredCTA && selectedShoeGender ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div>
      {t("gender.description") && (
        <span
          className="description"
          style={{
            marginTop: "10px",
            ...subtitlesStyles,
            justifyContent:
              subtitlesStyles.textAlign as React.CSSProperties["justifyContent"],
            textAlign:
              subtitlesStyles.textAlign as React.CSSProperties["textAlign"],
            textTransform: (subtitlesStyles.textTransform === "capitalize"
              ? "none"
              : subtitlesStyles.textTransform) as React.CSSProperties["textTransform"],
          }}
        >
          {subtitlesStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("gender.description"))
            : t("gender.description")}
        </span>
      )}

      <div
        className="gender-buttons"
        style={{ marginBottom: "30px" }}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        tabIndex={0}
      >
        <GenderButton
          gender={GENDERS.F}
          selectedGender={selectedShoeGender}
          baseStyles={femaleButtonStyles}
          hovered={hovered}
          setHovered={setHovered}
          brandName={brandDefined?.name}
          label={
            femaleButtonStyles.textTransform === "capitalize"
              ? capitalizeFirstLetter(t(genderFemaleKey))
              : t(genderFemaleKey)
          }
          onSelect={() => setSelectedShoeGender(GENDERS.F)}
        />

        <GenderButton
          gender={GENDERS.M}
          selectedGender={selectedShoeGender}
          baseStyles={maleButtonStyles}
          hovered={hovered}
          setHovered={setHovered}
          brandName={brandDefined?.name}
          label={
            maleButtonStyles.textTransform === "capitalize"
              ? capitalizeFirstLetter(t(genderMaleKey))
              : t(genderMaleKey)
          }
          onSelect={() => setSelectedShoeGender(GENDERS.M)}
        />
      </div>

      <button
        type="button"
        disabled={!selectedShoeGender}
        className={`continue-button breasts-continue`}
        onMouseEnter={(e) => {
          setHoveredCTA(true);
          if (selectedShoeGender) {
            Object.assign(e.currentTarget.style, hoverStylesContinue);
          }
        }}
        onMouseLeave={(e) => {
          setHoveredCTA(false);
          if (selectedShoeGender) {
            Object.assign(e.currentTarget.style, CTAStyles);
          }
        }}
        onClick={nextStep}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {activeStylesContinue.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("continue", { ns: "components/intro" }))
            : t("continue", { ns: "components/intro" })}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>
    </div>
  );
};

export default StepShoeGender;
