{"name": "JOTT", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "14px", "fontColor": "#000000", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "18px", "borderColor": "#000000", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "14px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "700", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px"}, "focused": {"backgroundColor": "#000000", "fontWeight": "700", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "700", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "<PERSON>", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "none", "color": "#000000", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "none", "color": "#000000", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#000000", "fontWeight": "700", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#000000", "fontWeight": "700", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0px", "fontSize": "12px", "fontWeight": "600", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "3px", "borderRadius": "0px", "fontWeight": "600", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "13px", "fontWeight": "700", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "13px", "fontWeight": "700", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}