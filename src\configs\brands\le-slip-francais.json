{"name": "Le Slip <PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#03053E", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#03053E", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#03053E", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #03053E"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#03053E", "borderRadius": "44px", "borderColor": "#e2e2e2", "borderWidth": "1px", "fontSize": "15px", "fontWeight": "500", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#03053E", "fontColor": "#F6F4EE", "borderRadius": "44px", "borderColor": "", "borderWidth": "", "fontSize": "15px", "fontWeight": "500", "textTransform": "capitalize"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#03053E", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#03053E"}}, "3": {"qrcode": {"backgroundColor": "#03053E"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#03053E", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "13px", "fontColor": "#03053E", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#E9E9E9", "borderWidth": "2px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "13px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#03053E", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#03053E", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#03053E", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Archivo", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#03053E", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#03053E", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "15px", "fontColor": "#03053E", "textTransform": "capitalize", "borderRadius": "44px", "borderColor": "#e2e2e2", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#03053E", "fontWeight": "500", "fontColor": "#F6F4EE", "fontSize": "15px", "textTransform": "capitalize", "borderRadius": "44px", "borderColor": "#03053E", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#03053E", "borderRadius": "44px", "fontSize": "13px", "fontWeight": "600", "fontColor": "#2e2e2e", "textTransform": "capitalize", "fontStyle": "underline"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "14px", "fontWeight": "500", "fontSize": "15px", "fontColor": "#03053E", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#03053E", "borderWidth": "2px", "borderRadius": "14px", "fontWeight": "500", "fontSize": "15px", "fontColor": "#03053E", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#03053E"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "500", "fontSize": "13px", "fontColor": "#03053E", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "500", "fontSize": "13px", "fontColor": "#03053E", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#03053E", "fontSize": "13px", "fontWeight": "500", "borderRadius": "5px", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#03053E", "fontColor": "#FFFFFF", "fontSize": "13px", "fontWeight": "500", "borderRadius": "5px", "borderColor": "#03053E", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#03053E", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}