{"name": "<PERSON><PERSON><PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#1d1d1d", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#8c8c8c", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#1d1d1d", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#1d1d1d", "marginBottom": 0, "padding": "", "borderBottom": "1px solid #1d1d1d"}}, "genderCTA": {"unfocused": {"backgroundColor": "#d3d3d3", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#d3d3d3", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#1d1d1d", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#1d1d1d", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "6px", "right": "-11px", "activeColor": "#1d1d1d", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#1d1d1d", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#1d1d1d"}}, "3": {"qrcode": {"backgroundColor": "#1d1d1d"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#1d1d1d", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#1d1d1d", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "500", "fontSize": "14px", "fontColor": "#8c8c8c", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#8c8c8c", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "500", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#8c8c8c", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#1d1d1d", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#8c8c8c", "textTransform": "uppercase", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "600", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Futura", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "uppercase", "color": "#1d1d1d", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#1d1d1d", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#d3d3d3", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#1d1d1d", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#1d1d1d", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#1d1d1d", "borderRadius": "0", "fontSize": "12px", "fontWeight": "500", "fontColor": "#1d1d1d", "textTransform": "capitalize", "fontStyle": "bold"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#8c8c8c", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "500", "fontSize": "12px", "fontColor": "#1d1d1d", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#1d1d1d", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "500", "fontSize": "12px", "fontColor": "#1d1d1d", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#8c8c8c"}, "unfocused": {"color": "#e6e6e8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#1d1d1d", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "16px", "fontColor": "#1d1d1d", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#1d1d1d", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "16px", "fontColor": "#1d1d1d", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#1d1d1d", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#8c8c8c", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#1d1d1d", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#1d1d1d", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#1d1d1d", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}