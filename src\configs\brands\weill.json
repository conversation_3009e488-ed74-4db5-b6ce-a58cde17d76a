{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "left", "letterSpacing": "1.8px"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "12px", "fontColor": "#A3A1A7", "textAlign": "left", "letterSpacing": "0.72px"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left", "letterSpacing": "0.72px"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize", "letterSpacing": "0.72px"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0px", "borderColor": "", "borderWidth": "0px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize", "letterSpacing": "0.72px"}}, "units": {"fontWeight": "400", "fontSize": "14px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#707070"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#707070", "borderWidth": "1px", "letterSpacing": "0.72px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px", "letterSpacing": "0.72px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px", "letterSpacing": "0.72px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left", "letterSpacing": "0.72px"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left", "letterSpacing": "0.72px"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "", "borderWidth": "0px", "letterSpacing": "0.72px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize", "letterSpacing": "0.72px"}}, "all": {"font": "Futura", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#capitalize", "fontSize": "20px", "letterSpacing": "0.72px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#707070", "fontSize": "12px", "letterSpacing": "0.72px"}, "cta": {"unfocused": {"backgroundColor": "#707070", "fontWeight": "400", "fontSize": "14px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none", "letterSpacing": "0.72px"}, "focused": {"backgroundColor": "#000000", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "letterSpacing": "0.72px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#707070", "fontWeight": "regular", "textTransform": "capitalize", "letterSpacing": "0.72px"}}, "questions": {"scanCTA": {"borderWidth": "", "borderColor": "", "borderRadius": "", "fontSize": "", "fontWeight": "", "fontColor": "", "textTransform": "", "fontStyle": "", "letterSpacing": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#707070", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize", "letterSpacing": "0.72px"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize", "letterSpacing": "0.72px"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#000000"}, "unfocused": {"color": "#707070"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "transparent", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center", "letterSpacing": "0.72px"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "transparent", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center", "letterSpacing": "0.72px"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "capitalize", "letterSpacing": "0.72px"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "14px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "textTransform": "capitalize", "textDecoration": "underline", "letterSpacing": "0.72px"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "", "fontSize": "12px", "letterSpacing": "0.72px"}}}}}