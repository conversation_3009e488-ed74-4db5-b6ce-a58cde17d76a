{"name": "Lacoste", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#002D18", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "16px", "fontColor": "#002D18", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#002D18", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #002D18"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#002D18", "borderRadius": "0", "borderColor": "#F0F0F0", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "unset"}, "focused": {"backgroundColor": "#002D18", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#002D18", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "unset"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#002D18", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#002D18"}}, "3": {"qrcode": {"backgroundColor": "#002D18"}}, "7": {"generateResult": true, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#002D18", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "13px", "fontColor": "#002D18", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#002D18", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#E9E9E9", "borderWidth": "2px"}, "focused": {"backgroundColor": "#002D18", "fontWeight": "600", "fontSize": "13px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#002D18", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "13px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#002D18", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#002D18", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#002D18", "textTransform": "none", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#B9B9B9", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Figtree", "titles": {"textAlign": "left", "fontWeight": "600", "color": "#002D18", "fontSize": "18px", "textTransform": "none"}, "subtitles": {"textAlign": "left", "fontWeight": "500", "color": "#002D18", "fontSize": "14px", "textTransform": "none"}, "cta": {"unfocused": {"backgroundColor": "#E8EEEC", "fontWeight": "400", "fontColor": "#6F857B", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#CBD7D1", "borderWidth": "1px", "borderTop": "1px solid #CBD7D1", "borderBottom": "1px solid #CBD7D1", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#002D18", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#002D18", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#002D18", "borderRadius": "0", "fontSize": "12px", "fontWeight": "400", "fontColor": "#002D18", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "none", "fontWeight": "regular", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#2E2E2E", "borderWidth": "2px", "borderRadius": "none", "fontWeight": "regular", "fontSize": "13px", "fontColor": "#2E2E2E", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#002D18"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#002D18", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#002D18", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#002D18", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#002D18", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#002D18", "fontSize": "12px", "fontWeight": "600", "borderRadius": "2px", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "unset"}, "focused": {"backgroundColor": "#002D18", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "600", "borderRadius": "2px", "borderColor": "#002D18", "borderWidth": "1px", "textTransform": "unset"}}, "skip": {"fontColor": "#002D18", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}