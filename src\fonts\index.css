/* Futura */
@font-face {
  font-family: "Futura";
  src: local("Futura"), url(./Futura/Futura-Medium.otf) format("truetype");
}

@font-face {
  font-family: "Futura";
  src: local("Futura Extra-Bold"),
    url(./Futura/FuturaExtraBold.otf) format("truetype");
  font-weight: 800; /* Extra-Bold */
  font-style: normal;
}

/* Graphik */
@font-face {
  font-family: "Graphik";
  src: local("Graphik Regular"),
    url(./Graphik/GraphikRegular.otf) format("truetype");
  font-weight: 400; /* Regular */
  font-style: normal;
}

@font-face {
  font-family: "Graphik";
  src: local("Graphik Medium"),
    url(./Graphik/GraphikMedium.otf) format("truetype");
  font-weight: 500; /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Graphik";
  src: local("Graphik Medium"),
    url(./Graphik/GraphikSemibold.otf) format("truetype");
  font-weight: 600; /* SemiBold */
  font-style: normal;
}

@font-face {
  font-family: "Graphik";
  src: local("Graphik Bold"), url(./Graphik/GraphikBold.otf) format("truetype");
  font-weight: 700; /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Graphik";
  src: local("Graphik ExtraBold"),
    url(./Graphik/GraphikExtraBold.otf) format("truetype");
  font-weight: 800; /* ExtraBold */
  font-style: normal;
}

/* Times New Roman */
@font-face {
  font-family: "Times New Roman Custom";
  src: url(./TimesNewRoman/TimesNewRomanCustom.woff2) format("woff2");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* Arial */
@font-face {
  font-family: "Arial Custom";
  src: url(./Arial/Arial.woff2) format("woff2");
  font-weight: normal;
  font-style: normal;
}

/* Optima */
@font-face {
  font-family: "Optima";
  src: local("Optima Regular"), url(./Optima/OPTIMA.TTF) format("truetype");
  font-weight: 400; /* Regular */
  font-style: normal;
}

@font-face {
  font-family: "Optima";
  src: local("Optima Medium"),
    url(./Optima/Optima_Medium.ttf) format("truetype");
  font-weight: 500; /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Optima";
  src: local("Optima SemiBold"),
    url(./Optima/Optima_Medium.ttf) format("truetype");
  font-weight: 600; /* SemiBold */
  font-style: normal;
}

@font-face {
  font-family: "Optima";
  src: local("Optima Bold"), url(./Optima/OPTIMA_B.TTF) format("truetype");
  font-weight: 700; /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Optima";
  src: local("Optima ExtraBold"), url(./Optima/OPTIMA.TTF) format("truetype");
  font-weight: 800; /* ExtraBold */
  font-style: normal;
}

/* HafferSQ */
@font-face {
  font-family: "HafferSQ";
  src: local("HafferSQ Regular"),
    url(./HafferSQ/HafferSQ-Regular.ttf) format("truetype");
  font-weight: 400; /* Regular */
  font-style: normal;
}

/* HafferSQ */
@font-face {
  font-family: "Vectora LT Std";
  src: local("Vectora LT Std Regular"),
    url(./Vectora_LT_Std/Vectora_LT_Std.otf) format("truetype");
  font-weight: 400; /* Regular */
  font-style: normal;
}

/* Dinot */
@font-face {
  font-family: "Dinot";
  src: local("Dinot Light"),
    url(./Dinot/font-DINOT-Light.woff2) format("truetype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Dinot";
  src: local("Dinot Regular"), url(./Dinot/font-DINOT.woff2) format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Dinot";
  src: local("Dinot Medium"),
    url(./Dinot/font-DINOT-Medium.woff2) format("truetype");
  font-weight: 500; /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Dinot";
  src: local("Dinot SemiBold"),
    url(./Dinot/font-DINEngschriftStd.woff2) format("truetype");
  font-weight: 600; /* SemiBold */
  font-style: normal;
}

@font-face {
  font-family: "Dinot";
  src: local("Dinot Bold"), url(./Dinot/font-DINOT.woff2) format("truetype");
  font-weight: 700; /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Dinot";
  src: local("Dinot ExtraBold"),
    url(./Dinot/font-DINOT.woff2) format("truetype");
  font-weight: 800; /* ExtraBold */
  font-style: normal;
}

/* Brown */
@font-face {
  font-family: "Brown";
  src: local("Brown Light"), url(./Brown/BrownLLWeb-Light.woff2) format("woff2");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Brown";
  src: local("Brown Regular"),
    url(./Brown/BrownLLWeb-Regular.woff2) format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Brown";
  src: local("Brown Medium"),
    url(./Brown/BrownLLWeb-Medium.woff2) format("woff2");
  font-weight: 500; /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Brown";
  src: local("Brown Bold"), url(./Brown/BrownLLWeb-Bold.woff2) format("woff2");
  font-weight: 700; /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Brown";
  src: local("Brown Black"), url(./Brown/BrownLLSub-Black.woff2) format("woff2");
  font-weight: 800; /* Bold */
  font-style: normal;
}

/* Gilmer */
@font-face {
  font-family: "Gilmer";
  src: local("Gilmer Light"), url(./Gilmer/Gilmer_Light.otf) format("truetype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Gilmer";
  src: local("Gilmer Regular"),
    url(./Gilmer/Gilmer_Regular.otf) format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Gilmer";
  src: local("Gilmer Medium"),
    url(./Gilmer/Gilmer_Medium.otf) format("truetype");
  font-weight: 500; /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Gilmer";
  src: local("Gilmer Semi Bold"),
    url(./Gilmer/Gilmer_Medium.otf) format("truetype");
  font-weight: 600; /* Semi bold */
  font-style: normal;
}

@font-face {
  font-family: "Gilmer";
  src: local("Gilmer Bold"), url(./Gilmer/Gilmer_Bold.otf) format("truetype");
  font-weight: 700; /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Gilmer";
  src: local("Gilmer Black"), url(./Gilmer/Gilmer_Heavy.otf) format("truetype");
  font-weight: 800; /* Bold */
  font-style: normal;
}
