type LogType = "log" | "warn" | "error";

interface ILogger {
  (type: LogType, ...args: any[]): Promise<void>;
  log(...args: any[]): Promise<void>;
  warn(...args: any[]): Promise<void>;
  error(...args: any[]): Promise<void>;
}

type LogArgs = any[];

export const logger: ILogger = async (type, ...args: LogArgs) => {
  const urlParams = new URLSearchParams(window.location.search);
  const debug =
    urlParams.get("debug") === "true" ||
    window.location.host.includes("localhost") ||
    window.location.host.includes("dev");

  const prefix = `[KLEEP] [Sizing iFrame]`;

  let force = false;
  if (
    args.length > 0 &&
    typeof args[args.length - 1] === "object" &&
    args[args.length - 1]?.force
  ) {
    force = true;
    args = args.slice(0, -1);
  }

  if (debug || force) {
    if (type === "log") console.log(prefix, ...args);
    else if (type === "warn") console.warn(prefix, ...args);
    else if (type === "error") console.error(prefix, ...args);
  }
};

logger.log = async (...args: LogArgs) => logger("log", ...args);
logger.warn = async (...args: LogArgs) => logger("warn", ...args);
logger.error = async (...args: LogArgs) => logger("error", ...args);
