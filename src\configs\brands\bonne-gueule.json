{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "male", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#151515", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#A3A1A7", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#151515", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#151515", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #151515"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#151515", "borderRadius": "0", "borderColor": "#151515", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#151515", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "600", "textTransform": "capitalize"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#151515", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#151515", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#151515"}}, "3": {"qrcode": {"backgroundColor": "#151515"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#151515", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#151515", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "12px", "fontColor": "#151515", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#DCDCDC", "borderWidth": "1px"}, "focused": {"backgroundColor": "#151515", "fontWeight": "600", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#151515", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#151515", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#151515", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Chivo", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "capitalize", "color": "#151515", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#151515", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#EBECEC", "fontWeight": "600", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#151515", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0", "fontSize": "13px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#151515", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#151515", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#151515", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "500", "fontSize": "12px", "fontColor": "#151515", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#151515"}, "unfocused": {"color": "#A3A1A7"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#151515", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#151515", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#151515", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#151515", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#151515", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#DCDCDC", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#151515", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "500", "borderRadius": "0", "borderColor": "#151515", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#151515", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}