import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";
import MeasuredBy from "../molecules/MeasuredBy";

import {
  activeStylesContinue,
  welcomeScreenUI,
  titleStyles as titleStylesBase,
  subtitlesStyles,
} from "../../configs";
import { capitalizeFirstLetter, postClearMid, useIsMobile } from "../../utils";
import { findBrandByDomain } from "../../configs/configLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { hoverStylesContinue } from "../../configs/stylesLoader";

import "./index.css";

interface IPropsResultError {
  step: any;
  nextStep: () => void;
  isShoesProduct: boolean;
  outOfRange: boolean;
}

const ResultError: FC<IPropsResultError> = ({
  step,
  nextStep,
  isShoesProduct,
  outOfRange,
}) => {
  const isMobile = useIsMobile();

  const error_description = `error.description${
    outOfRange ? "_outOfRange" : ""
  }`;

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: !isMobile ? 830 : 778,
    minWidth: 767,
  });

  const { t } = useTranslation("components/results/error");

  const [hovered, setHovered] = useState(false);

  const brandDefined = findBrandByDomain();

  const titleTextTransform = welcomeScreenUI.input_fields.title
    .textTransform as React.CSSProperties["textTransform"];

  const titlesStyles = {
    fontWeight: "400",
    textAlign: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["textAlign"],
    textTransform:
      titleTextTransform === "capitalize" ? "none" : titleTextTransform,
    color: titleStylesBase.color as React.CSSProperties["color"],
    fontSize: "14px",
    justifyContent: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["justifyContent"],
    fontStyle: (welcomeScreenUI.input_fields.title as any)
      .fontStyle as React.CSSProperties["fontStyle"],
    fontFamily: (welcomeScreenUI.input_fields.title as any)
      .fontFamily as React.CSSProperties["fontFamily"],
  };

  useEffect(() => {
    if (outOfRange) postClearMid();
  }, [outOfRange]);

  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...activeStylesContinue,
    ...(isIntersport && hovered ? hoverStylesContinue : {}),
    textTransform: "none" as React.CSSProperties["textTransform"],
    whiteSpace: "nowrap",
    padding: brandDefined?.name === "Victoria Beckham" ? "7px" : "16px",
    height: brandDefined?.name === "Victoria Beckham" ? "30px" : "auto",
    marginBottom: isMobile ? "20px" : "0px",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered ? 1 : 0,
    transform: hovered ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div
      className="result-error"
      style={{
        marginTop: isShoesProduct
          ? "30px"
          : isLargeHeightRelative
          ? "30px"
          : isMobile
          ? 0
          : "40px",
      }}
    >
      <span
        className="description"
        style={{
          ...titlesStyles,
          marginTop:
            !isMobile && brandDefined?.name === "SRP"
              ? "60px"
              : isLargeHeightRelative
              ? 0
              : isMobile
              ? 0
              : "25px",
          marginBottom: isShoesProduct ? "30px" : "40px",
          textTransform: (subtitlesStyles.textTransform === "capitalize"
            ? "none"
            : subtitlesStyles.textTransform) as React.CSSProperties["textTransform"],
        }}
      >
        {isShoesProduct
          ? t("shoeMessage")
          : subtitlesStyles.textTransform === "capitalize"
          ? capitalizeFirstLetter(t(error_description))
          : t(error_description)}
      </span>

      <button
        type="button"
        className={`continue-button`}
        onMouseEnter={(e) => {
          setHovered(true);
          Object.assign(e.currentTarget.style, hoverStylesContinue);
        }}
        onMouseLeave={(e) => {
          setHovered(false);
          Object.assign(e.currentTarget.style, CTAStyles);
        }}
        onClick={() => nextStep()}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {activeStylesContinue.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("skip"))
            : t("skip")}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>

      <MeasuredBy step={step} />
    </div>
  );
};

export default ResultError;
