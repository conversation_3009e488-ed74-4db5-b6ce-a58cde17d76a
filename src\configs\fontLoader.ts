import { logger } from "../utils/logging";

// Function to dynamically add Google Font link to the <head> and wait until it's loaded
const CUSTOM_FONTS = [
  "Fu<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Times New Roman",
  "Optima",
  "HafferSQ",
  "Vectora LT Std",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
];

export const loadGoogleFont = (fontStr: string): Promise<void> => {
  const { name, italic } = parseFontString(fontStr);

  if (CUSTOM_FONTS.includes(fontStr)) {
    logger.log(
      `The font ${name} is a custom font and doesn't require loading from Google Fonts.`
    );
    return Promise.resolve();
  }

  const italicParam = italic ? "1" : "0";

  const fontUrl = `https://fonts.googleapis.com/css2?family=${name.replace(
    / /g,
    "+"
  )}:ital,wght@${italicParam},100;${italicParam},200;${italicParam},300;${italicParam},400;${italicParam},500;${italicParam},600;${italicParam},700;${italicParam},800;${italicParam},900&display=swap`;

  return new Promise((resolve, reject) => {
    const link = document.createElement("link");
    link.href = fontUrl;
    link.rel = "stylesheet";
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load font: ${name}`));
    document.head.appendChild(link);
  });
};

const parseFontString = (fontStr: string) => {
  const parts = fontStr.split(",").map((part) => part.trim().toLowerCase());
  const italic = parts.includes("italic");
  const bold = parts.includes("bold");
  const name = parts
    .filter((part) => part !== "italic" && part !== "bold")
    .join(" ")
    .replace(/\s+/g, " ")
    .replace(/\b\w/g, (c) => c.toUpperCase());

  return { name, italic, bold };
};

// Function to extract font-family name from the URL
export const extractFontFamily = (url: string) => {
  const match = url.match(/family=([^:&,]+)/);
  if (match) {
    return match[1].replace(/\+/g, " ");
  }
  return url;
};

// Apply the font to all text elements, including html and body
export const applyFontFromConfig = async (fontStr: string) => {
  try {
    const { name, italic } = parseFontString(fontStr);
    await loadGoogleFont(fontStr);
    const fontFamily = extractFontFamily(fontStr);

    const style = document.createElement("style");
    style.innerHTML = `
      * {
        font-family: "${fontFamily}", sans-serif !important;
      }

      .fa-regular, .far, .fa, [class^="fa-"], [class*=" fa-"], svg text {
        font-family: "${fontFamily}", sans-serif !important;
        font-weight: 400;
      }

      :host, :root {
        --fa-font-solid: normal 900 1em/1 "${fontFamily}", sans-serif !important;
      }

      ::before, ::after {
        font-family: "${fontFamily}", sans-serif !important;
      }
    `;

    document.head.appendChild(style);

    document.documentElement.style.fontFamily = `${name}, sans-serif`;
    document.documentElement.style.fontStyle = italic ? "italic" : "normal";
    document.body.style.fontFamily = `${name}, sans-serif`;
    document.body.style.fontStyle = italic ? "italic" : "normal";
  } catch (error) {
    logger.error(`Failed to load the font: ${error}`);
  }
};
