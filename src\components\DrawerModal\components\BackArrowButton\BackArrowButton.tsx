import { IconButton } from "@mui/material";
import { ArrowBackOutlined as ArrowBackIcon } from "@mui/icons-material";

import { MODAL_STEPS } from "../../../../constants/modal";
import { getCloseButtonSize, useIsMobile } from "../../../../utils";
import { useMediaQuery } from "react-responsive";
import { uxGender } from "../../../../configs";
import { findBrandByDomain } from "../../../../configs/configLoader";

import "./index.css";

interface BackArrowButtonProps {
  step: any | null;
  isShoesProduct: boolean;
  brandName?: string;
  handleGoBack: () => void;
}

export const BackArrowButton = ({
  step,
  isShoesProduct,
  brandName,
  handleGoBack,
}: BackArrowButtonProps) => {
  const isMobile = useIsMobile();

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: 830,
    minWidth: 767,
  });

  const isMediumHeightRelative = useMediaQuery({
    maxHeight: 678,
    minWidth: 767,
  });

  const brandDefined = findBrandByDomain();

  const isBlackstore = brandDefined?.name === "Blackstore";
  const isSportyAndRich = brandDefined?.name === "Sporty & Rich";

  const shouldShow =
    step &&
    step?.number >= 0 &&
    ![
      MODAL_STEPS.DEVICE_SELECT.number,
      MODAL_STEPS.CONSENT.number,
      MODAL_STEPS.GENDER.number,
      MODAL_STEPS.INTRO.number,
      uxGender ? MODAL_STEPS.INTRO_MOBILE.number : false,
      MODAL_STEPS.ERROR.number,
      MODAL_STEPS.RESULT.number,
      MODAL_STEPS.UNAVAILABLE_RESULT.number,
      MODAL_STEPS.ANTI_BRACKETING.number,
    ].includes(step?.number);

  if (!shouldShow) return null;

  const getTopValue = (): string => {
    if (isBlackstore) return "5px";
    if (isShoesProduct && !isSportyAndRich) return "10px";
    return "15px";
  };

  const getWrapperTop = (): string => {
    if (isLargeHeightRelative && !isSportyAndRich) return "5px";
    if (isMediumHeightRelative && isSportyAndRich) return "5px";
    return "inherit";
  };

  const closeButtonSize = getCloseButtonSize(
    isMobile,
    brandName,
    { mobile: "16px", desktop: "18px" }, // standart
    { mobile: "27px", desktop: "30px" } // custom
  );

  return (
    <div
      className="left-arrow"
      style={{ position: "absolute", left: "20px", top: getWrapperTop() }}
    >
      <IconButton
        sx={{
          zIndex: 1,
          padding: "3px",
          borderRadius: "5px",
          position: "fixed",
          top: getTopValue(),
        }}
        onClick={handleGoBack}
      >
        <ArrowBackIcon
          style={{
            color: "black",
            width: closeButtonSize,
            height: closeButtonSize,
          }}
        />
      </IconButton>
    </div>
  );
};

export default BackArrowButton;
