import { FC } from "react";
import { useTranslation } from "react-i18next";

import { findBrandByDomain } from "../../../configs/configLoader";
import { policityStyles, uxGender, welcomeScreenUI } from "../../../configs";
import { useIsMobile } from "../../../utils";
import { MODAL_STEPS } from "../../../constants/modal";
import LTDC_logo from "../../../assets/brands/logo-LTDS.png";
import KLEEP_Black_Logo from "../../../assets/brands/KLEEP_Black_Logo.png";

import "./index.css";

interface IPropsMeasuredBy {
  step: any;
}

const MeasuredBy: FC<IPropsMeasuredBy> = ({ step }) => {
  const { t } = useTranslation("components/intro");

  const isMobile = useIsMobile();

  const brandDefined = findBrandByDomain();

  const urlParameters = new URLSearchParams(window.location.search);
  const isChildren = urlParameters.get("category") === "children" || urlParameters.get("kids") === "true";

  const customLinkStyles = {
    fontSize: policityStyles.fontSize,
    color: policityStyles.fontColor,
    fontWeight: policityStyles.fontWeight,
    textTransform: policityStyles.textTransform,
    letterSpacing: (policityStyles as any)?.letterSpacing,
  };

  const isNinaRicciFirstScreen =
    brandDefined?.name === "Nina Ricci" &&
    (step?.number === MODAL_STEPS.INTRO.number ||
      step?.number === MODAL_STEPS.GENDER.number ||
      step?.number === MODAL_STEPS.INTRO_MOBILE.number);

  return isChildren ? null : (
    <>
      {!isMobile &&
        brandDefined?.name === "LTDC" &&
        step?.number !== MODAL_STEPS.INTRO.number && (
          <div
            style={{
              position: "absolute",
              bottom: "50px",
              left: "50%",
              transform: "translateX(-50%)",
            }}
          >
            <img
              src={LTDC_logo}
              alt="LTDC logo"
              style={{
                width: "200px",
                height: "auto",
              }}
            />
          </div>
        )}
      {!isNinaRicciFirstScreen ? (
        <>
          {(!isMobile
            ? step?.number === MODAL_STEPS.INTRO.number
            : !uxGender
            ? step?.number === MODAL_STEPS.GENDER.number
            : step?.number === MODAL_STEPS.INTRO_MOBILE.number) && (
            <div
              className="policy"
              style={{
                display: "flex",
                justifyContent: "center",
                width: isMobile ? "100%" : "calc(100% - 50px)",
                position: isMobile ? "relative" : "fixed",
                bottom:
                  isMobile && brandDefined?.name === "SRP"
                    ? "30px"
                    : isMobile && brandDefined?.name === "Victoria Beckham"
                    ? "5px"
                    : isMobile && brandDefined?.name !== "Victoria Beckham"
                    ? "0px"
                    : "45px",
                color: customLinkStyles.color,
                fontSize: isMobile ? "10px" : customLinkStyles.fontSize,
                zIndex: 999999,
                fontStyle: "normal",
              }}
              dangerouslySetInnerHTML={{
                __html: t("pp_link")
                  .replace(/<a(.*?)>/g, (match: any) => {
                    return `<a style="${Object.entries(customLinkStyles)
                      .map(([key, value]) => `${key}: ${value}`)
                      .join("; ")}" target="_blank"${match.slice(2)}`;
                  })
                  .replace(/<\/a>\./g, ".</a>"),
              }}
            />
          )}
        </>
      ) : null}
      {!isNinaRicciFirstScreen ? (
        <div
          className="measured-by"
          style={{
            position: isMobile ? "relative" : "fixed",
            bottom: "0",
            left: "54%",
            top: "inherit",
            color:
              brandDefined?.name === "KENZO"
                ? welcomeScreenUI.genderCTA.focused.backgroundColor
                : brandDefined?.name === "SRP"
                ? "#767474"
                : customLinkStyles.color,
            fontSize: isMobile ? "10px" : customLinkStyles.fontSize,
            fontWeight: "500",
            zIndex: 9999,
            height: isMobile ? "20px" : "auto",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "4px",
              fontSize: isMobile ? "10px" : customLinkStyles.fontSize,
              fontStyle: "normal",
            }}
          >
            {t("measured_by") !== "measured_by"
              ? t("measured_by")
              : "Measured by"}
            <img
              src={KLEEP_Black_Logo}
              alt="KLEEP_Logo"
              style={{
                width: "80px",
                height: "auto",
                display: "block",
                objectFit: "contain",
                position: "relative",
                right: "20px",
                bottom: "1px",
                zIndex: 999999,
              }}
            />
          </div>
        </div>
      ) : null}
    </>
  );
};

export default MeasuredBy;
