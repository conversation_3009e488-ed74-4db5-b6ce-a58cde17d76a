import { logger } from "./logging";

export const postSendUid = async (uid: string | null) => {
  logger.log("Post Message: sendUid");
  window.parent.postMessage({ data: "uid", uid: uid }, "*");
};

export const postCloseIframe = async () => {
  logger.log("Post Message: closeIframe", { force: true });

  window.parent.postMessage({ action: "closeIframe" }, "*");
};

export const postGetSizes = async () => {
  logger.log("Post Message: getSizes");
  window.parent.postMessage({ action: "getSizes" }, "*");
};

export const postSendMid = async (mid: string | null) => {
  logger.log("Post Message: sendMid");
  window.parent.postMessage({ data: "mid", mid: mid }, "*");
};

export const postClearMid = async () => {
  logger.log("Post Message: clearMid");
  window.parent.postMessage({ action: "clearMid" }, "*");
};

export const postClearUid = async () => {
  logger.log("Post Message: clearUid");
  window.parent.postMessage({ action: "clearUid" }, "*");
};

export const postSelectSize = async (selectedSize: string) => {
  // logger.log(`Post Message: selectSize is deactivated`);
  logger.log("Post Message: selectSize");
  window.parent.postMessage({ action: "selectSize", size: selectedSize }, "*");
};

export const postAddToCart = async (variantId: string) => {
  logger.log(`Post Message: addToCart ${variantId}`);
  window.parent.postMessage({ action: "addToCart", variantId: variantId }, "*");
};

export const postGetMid = async () => {
  logger.log(`No MID found in local storage, requesting from PM`);
  window.parent.postMessage({ action: "getMid" }, "*");
};
