import React, { useCallback } from "react";
import StepLingerieQuestion1 from "../../StepLingerieQuestion1";
import StepLingerieQuestion2 from "../../StepLingerieQuestion2";
import StepLingerieQuestion3 from "../../StepLingerieQuestion3";
import StepLingerieQuestion4 from "../../StepLingerieQuestion4";

import { MODAL_STEPS } from "../../../../../constants/modal";
import { useUserContext } from "../../../../../store/userContext";
import { logger } from "../../../../../utils";

interface LingerieStepsProps {
  step: any;
  setStep: (step: any) => void;
  subtitlesStyles?: any;
  sendQuestionAnswers?: () => void | Promise<void>;
  lingerieQuestion1: string | null;
  setLingerieQuestion1: React.Dispatch<React.SetStateAction<string | null>>;
  lingerieQuestion2: string | null;
  setLingerieQuestion2: React.Dispatch<React.SetStateAction<string | null>>;
  lingerieQuestion3: string | null;
  setLingerieQuestion3: React.Dispatch<React.SetStateAction<string | null>>;
  lingerieQuestion4: string | null;
  setLingerieQuestion4: React.Dispatch<React.SetStateAction<string | null>>;
}

const noop = () => {};

const LingerieSteps: React.FC<LingerieStepsProps> = ({
  step,
  setStep,
  subtitlesStyles,
  sendQuestionAnswers = noop,
  lingerieQuestion1,
  setLingerieQuestion1,
  lingerieQuestion2,
  setLingerieQuestion2,
  lingerieQuestion3,
  setLingerieQuestion3,
  lingerieQuestion4,
  setLingerieQuestion4,
}) => {
  const userContext = useUserContext();

  const safeSendQuestionAnswers = useCallback(async () => {
    try {
      await sendQuestionAnswers?.();
    } catch (err) {
      // eslint-disable-next-line no-console
      logger.error("sendQuestionAnswers failed", err);
    }
  }, [sendQuestionAnswers]);

  return (
    <>
      {step?.number === MODAL_STEPS.LINGERIE_QUESTION_1.number && (
        <StepLingerieQuestion1
          lingerieQuestion1={lingerieQuestion1}
          setLingerieQuestion1={setLingerieQuestion1}
          nextStep={() => setStep(MODAL_STEPS.LINGERIE_QUESTION_2)}
          subtitlesStyles={subtitlesStyles}
        />
      )}

      {step?.number === MODAL_STEPS.LINGERIE_QUESTION_2.number && (
        <StepLingerieQuestion2
          lingerieQuestion2={lingerieQuestion2}
          setLingerieQuestion2={setLingerieQuestion2}
          nextStep={() => setStep(MODAL_STEPS.LINGERIE_QUESTION_3)}
          subtitlesStyles={subtitlesStyles}
        />
      )}

      {step?.number === MODAL_STEPS.LINGERIE_QUESTION_3.number && (
        <StepLingerieQuestion3
          lingerieQuestion3={lingerieQuestion3}
          setLingerieQuestion3={setLingerieQuestion3}
          nextStep={() => setStep(MODAL_STEPS.LINGERIE_QUESTION_4)}
          subtitlesStyles={subtitlesStyles}
        />
      )}

      {step?.number === MODAL_STEPS.LINGERIE_QUESTION_4.number && (
        <StepLingerieQuestion4
          lingerieQuestion4={lingerieQuestion4}
          setLingerieQuestion4={setLingerieQuestion4}
          nextStep={async () => {
            if (typeof userContext?.createNewMeasure === "function") {
              await userContext.createNewMeasure("question");
            }
            await safeSendQuestionAnswers();
          }}
          subtitlesStyles={subtitlesStyles}
        />
      )}
    </>
  );
};

export default React.memo(LingerieSteps);
