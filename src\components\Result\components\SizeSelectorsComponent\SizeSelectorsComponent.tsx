import { FC } from "react";
import { capitalizeFirstLetter, useIsMobile } from "../../../../utils";
import { SizeDataType } from "../../../../types/result";
import { findBrandByDomain } from "../../../../configs/configLoader";
import { resultScreenUI } from "../../../../configs";

import "./index.css";

interface IPropsSizeSelectorsComponent {
  itemWithLabelRank0: SizeDataType | null | undefined;
  itemWithLabelRank1: SizeDataType | null | undefined;
  itemWithLabelRank2: SizeDataType | null | undefined;
  selectedVariant: string;
  setSelectedVariant: React.Dispatch<React.SetStateAction<string>>;
  firstSelectorStyles: any;
  secondSelectorStyles: any;
  thirdSelectorStyles: any;
  fit_feedbacks: string[];
  selectedSize: SizeDataType | null;
  isSizeUnavailable: boolean;
}

type SelectorSizeType = "unfocused" | "focused" | "unavailable";

const SizeSelectorsComponent: FC<IPropsSizeSelectorsComponent> = ({
  itemWithLabelRank0,
  itemWithLabelRank1,
  itemWithLabelRank2,
  selectedVariant,
  setSelectedVariant,
  firstSelectorStyles,
  secondSelectorStyles,
  thirdSelectorStyles,
  fit_feedbacks,
  selectedSize,
  isSizeUnavailable,
}) => {
  const brandDefined = findBrandByDomain();
  const isMobile = useIsMobile();

  const getSelectorSizeStyles = (type: SelectorSizeType, variant: string) => {
    const sizeStyles = resultScreenUI.sizeSelector[type]!;

    const selectorSizeStyles = {
      backgroundColor:
        sizeStyles.backgroundColor as React.CSSProperties["backgroundColor"],
      fontWeight: sizeStyles.fontWeight as React.CSSProperties["fontWeight"],
      fontSize: sizeStyles.fontSize as React.CSSProperties["fontSize"],
      textTransform:
        sizeStyles.textTransform as React.CSSProperties["textTransform"],
      color: sizeStyles.fontColor as React.CSSProperties["color"],
      border: `1px solid ${sizeStyles?.borderColor}`,
      letterSpacing: (sizeStyles as any)?.letterSpacing,
      fontStyle: "normal",
    };

    return selectorSizeStyles;
  };

  const firstStyles =
    selectedVariant === "0"
      ? getSelectorSizeStyles(
          selectedSize?.possible === 0 && !isSizeUnavailable
            ? "unavailable"
            : "focused",
          "0"
        )
      : getSelectorSizeStyles("unfocused", "0");

  const secondStyles =
    selectedVariant === "1"
      ? getSelectorSizeStyles(
          selectedSize?.possible === 0 ? "unavailable" : "focused",
          "1"
        )
      : getSelectorSizeStyles("unfocused", "1");

  const thirdStyles =
    selectedVariant === "2"
      ? getSelectorSizeStyles(
          selectedSize?.possible === 0 && !isSizeUnavailable
            ? "unavailable"
            : "focused",
          "2"
        )
      : getSelectorSizeStyles("unfocused", "2");

  return brandDefined?.name === "Victoria Beckham" ? (
    <div
      className="sizes-menu"
      style={{
        marginTop: isMobile ? "40px" : "30px",
        marginBottom: isMobile ? "40px" : "30px",
        gap: isMobile ? "35px" : "34px",
      }}
    >
      {itemWithLabelRank0 && (
        <div>
          <div
            className={`menu-item ${selectedVariant === "0" && "active"}`}
            onClick={() => {
              if (!!itemWithLabelRank0) setSelectedVariant("0");
            }}
            style={{
              ...firstStyles,
              cursor: !itemWithLabelRank0 ? "default" : "pointer",
              textTransform:
                firstStyles?.textTransform === "capitalize"
                  ? "none"
                  : firstStyles?.textTransform,
              display: "flex",
              justifyContent: "center",
              textAlign: "center",
              alignItems: "center",
              width: "auto",
              minWidth: "85px",
              height: "auto",
              minHeight: "85px",
              padding: "20px 10px",
            }}
          >
            <p
              style={{
                fontSize: "36px",
                margin: 0,
                position: "relative",
              }}
            >
              {!itemWithLabelRank0 ? "" : itemWithLabelRank0?.label}
            </p>
          </div>
          <p
            style={{
              width: "100%",
              whiteSpace: "nowrap",
              fontSize: "10px",
            }}
          >
            {!itemWithLabelRank0
              ? ""
              : firstStyles?.textTransform === "capitalize"
              ? capitalizeFirstLetter(fit_feedbacks[0])
              : itemWithLabelRank0?.variant_id
              ? fit_feedbacks[0]
              : ""}
          </p>
        </div>
      )}

      {itemWithLabelRank1 && (
        <div>
          <div
            className={`menu-item ${selectedVariant === "1" && "active"}`}
            onClick={() => setSelectedVariant("1")}
            style={{
              ...secondStyles,
              cursor: "pointer",
              textTransform:
                secondStyles?.textTransform === "capitalize"
                  ? "none"
                  : secondStyles?.textTransform,
              display: "flex",
              justifyContent: "center",
              textAlign: "center",
              alignItems: "center",
              width: "auto",
              minWidth: "85px",
              height: "auto",
              minHeight: "85px",
              padding: "20px 10px",
            }}
          >
            <p
              style={{
                fontSize: "36px",
                margin: 0,
                position: "relative",
              }}
            >
              {!itemWithLabelRank1 ? "" : itemWithLabelRank1?.label}
            </p>
          </div>
          <p
            style={{
              width: "100%",
              whiteSpace: "nowrap",
              fontSize: "10px",
            }}
          >
            {secondStyles?.textTransform === "capitalize"
              ? capitalizeFirstLetter(fit_feedbacks[1])
              : fit_feedbacks[1]}
          </p>
        </div>
      )}

      {itemWithLabelRank2 && (
        <div>
          <div
            className={`menu-item ${selectedVariant === "2" && "active"}`}
            onClick={() => {
              if (!!itemWithLabelRank2) setSelectedVariant("2");
            }}
            style={{
              ...thirdStyles,
              cursor: !itemWithLabelRank2 ? "default" : "pointer",
              textTransform:
                thirdStyles?.textTransform === "capitalize"
                  ? "none"
                  : thirdStyles?.textTransform,
              display: "flex",
              justifyContent: "center",
              textAlign: "center",
              alignItems: "center",
              width: "auto",
              minWidth: "85px",
              height: "auto",
              minHeight: "85px",
              padding: "20px 10px",
            }}
          >
            <p
              style={{
                fontSize: "36px",
                margin: 0,
                position: "relative",
              }}
            >
              {!itemWithLabelRank2 ? "" : itemWithLabelRank2?.label}
            </p>
          </div>
          <p
            style={{
              width: "100%",
              whiteSpace: "nowrap",
              fontSize: "10px",
            }}
          >
            {!itemWithLabelRank2
              ? ""
              : thirdStyles?.textTransform === "capitalize"
              ? capitalizeFirstLetter(fit_feedbacks[2])
              : itemWithLabelRank2?.variant_id
              ? fit_feedbacks[2]
              : ""}
          </p>
        </div>
      )}
    </div>
  ) : (
    <div className="sizes-menu">
      <div
        className={`menu-item ${selectedVariant === "0" && "active"}`}
        onClick={() => {
          if (!!itemWithLabelRank0) setSelectedVariant("0");
        }}
        style={{
          ...firstSelectorStyles,
          cursor: !itemWithLabelRank0 ? "default" : "pointer",
          textTransform:
            firstSelectorStyles?.textTransform === "capitalize"
              ? "none"
              : firstSelectorStyles?.textTransform,
        }}
      >
        <p>
          {!itemWithLabelRank0
            ? ""
            : firstSelectorStyles?.textTransform === "capitalize"
            ? capitalizeFirstLetter(fit_feedbacks[0])
            : itemWithLabelRank0?.variant_id
            ? fit_feedbacks[0]
            : ""}
        </p>
      </div>
      <div
        className={`menu-item ${selectedVariant === "1" && "active"}`}
        onClick={() => setSelectedVariant("1")}
        style={{
          ...secondSelectorStyles,
          cursor: "pointer",
          textTransform:
            secondSelectorStyles?.textTransform === "capitalize"
              ? "none"
              : secondSelectorStyles?.textTransform,
        }}
      >
        <p>
          {secondSelectorStyles?.textTransform === "capitalize"
            ? capitalizeFirstLetter(fit_feedbacks[1])
            : fit_feedbacks[1]}
        </p>
      </div>
      <div
        className={`menu-item ${selectedVariant === "2" && "active"}`}
        onClick={() => {
          if (!!itemWithLabelRank2) setSelectedVariant("2");
        }}
        style={{
          ...thirdSelectorStyles,
          cursor: !itemWithLabelRank2 ? "default" : "pointer",
          textTransform:
            thirdSelectorStyles?.textTransform === "capitalize"
              ? "none"
              : thirdSelectorStyles?.textTransform,
        }}
      >
        <p>
          {!itemWithLabelRank2
            ? ""
            : thirdSelectorStyles?.textTransform === "capitalize"
            ? capitalizeFirstLetter(fit_feedbacks[2])
            : itemWithLabelRank2?.variant_id
            ? fit_feedbacks[2]
            : ""}
        </p>
      </div>
    </div>
  );
};

export default SizeSelectorsComponent;
