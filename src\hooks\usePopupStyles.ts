// hooks/usePopupStyles.ts
import { useMemo } from "react";
import { MODAL_STEPS } from "../constants/modal";
import { useIsMobile } from "../utils";
import { useMediaQuery } from "react-responsive";
import { findBrandByDomain } from "../configs/configLoader";

interface UsePopupStylesProps {
  font: string;
  titleStyles: {
    fontSize: string;
    textTransform?: string;
  };
  step?: { number: number } | null;
  isShoesProduct: boolean;
  lang: string | null;
}

export const usePopupStyles = ({
  font,
  titleStyles,
  step,
  isShoesProduct,
  lang,
}: UsePopupStylesProps) => {
  const isMobile = useIsMobile();

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: 830,
    minWidth: 767,
  });
  const isMediumHeightRelative = useMediaQuery({
    maxHeight: 678,
    minWidth: 767,
  });

  const brandDefined = findBrandByDomain();
  const brandName = brandDefined?.name;

  const isSportyAndRich = brandName === "Sporty & Rich";
  const isSRP = brandName === "SRP";

  const isFirstIntroScreen =
    step?.number === MODAL_STEPS.INTRO.number ||
    step?.number === MODAL_STEPS.GENDER.number ||
    step?.number === MODAL_STEPS.INTRO_MOBILE.number;

  const titleTextTransform =
    (titleStyles.textTransform as React.CSSProperties["textTransform"]) || "";

  // ===== Top Value
  const topValue = useMemo(() => {
    if (isShoesProduct && isSRP) return "45px";
    if (isShoesProduct && !isSportyAndRich) return "10px";
    if (isLargeHeightRelative && !isSportyAndRich && !isSRP) return "20px";
    if (isMediumHeightRelative && isSportyAndRich) return "20px";
    if (isSRP) {
      if (lang === "de") return "40px";
      if (step?.number !== MODAL_STEPS.ERROR.number) return "40px";
    }
    return "20px";
  }, [
    isShoesProduct,
    isSRP,
    isSportyAndRich,
    isLargeHeightRelative,
    isMediumHeightRelative,
    step,
    lang,
  ]);

  // ===== Margin Value
  const marginValue = useMemo(() => {
    if (isShoesProduct && !isSportyAndRich) return "10px 0 0 0";
    return "13px 0";
  }, [
    isLargeHeightRelative,
    isMediumHeightRelative,
    isShoesProduct,
    isSportyAndRich,
  ]);

  // ===== Font Size
  const fontSizeValue = useMemo(() => {
    const shouldReduceFont = isMediumHeightRelative && isSportyAndRich;

    return shouldReduceFont
      ? `calc(${titleStyles.fontSize} - 2px)`
      : titleStyles.fontSize;
  }, [
    isLargeHeightRelative,
    isMediumHeightRelative,
    isSportyAndRich,
    titleStyles.fontSize,
  ]);

  // ===== Helpers
  const getMarginTop = () => {
    if (
      isMobile &&
      brandName === "KENZO" &&
      step?.number === MODAL_STEPS.CONSENT.number
    ) {
      return 0;
    }

    if (isMobile) {
      return isSRP ? "20px" : "10px";
    }

    return 0;
  };

  const getMarginBottom = () => {
    if (
      isMobile &&
      brandName === "KENZO" &&
      step?.number === MODAL_STEPS.CONSENT.number
    ) {
      return 0;
    }

    if (!isMobile && isShoesProduct) {
      return "20px";
    }

    if (
      step?.number === MODAL_STEPS.RESULT.number ||
      step?.number === MODAL_STEPS.UNAVAILABLE_RESULT.number ||
      step?.number === MODAL_STEPS.ERROR.number ||
      (isShoesProduct && !isSportyAndRich)
    ) {
      return 0;
    }

    if (step?.number === MODAL_STEPS.BREASTS.number) {
      return "10px";
    }

    return "20px";
  };

  // ===== Header Styles
  const headerStyles: React.CSSProperties = {
    marginTop: getMarginTop(),
    marginBottom: getMarginBottom(),
    width: isSRP ? "calc(100% + 50px)" : "100%",
    position: isSRP ? "relative" : "initial",
    right: isSRP ? "25px" : "0",
    bottom: isSRP ? "20px" : "0",
    backgroundColor: isSRP ? "#FFFFFF" : "inherit",
    minHeight: isSRP ? "45px" : "auto",
    borderRadius: !isMobile && isSRP ? "8px" : 0,
  };

  // ===== Popup Styles
  const popupStyles: React.CSSProperties = {
    fontFamily: `${font}, sans-serif${!isMobile ? " !important" : ""}`,
    backgroundColor: brandName === "SRP" ? "#F7F7F7" : "#FFFFFF",
    paddingTop: isMobile && brandName === "SRP" ? 0 : "20px",
    borderRadius:
      isMobile && brandName === "Victoria Beckham"
        ? "10px 10px 0px 0px"
        : !isMobile && brandName === "SRP"
        ? "8px 0px 0px 8px"
        : 0,
    height: "auto",
    maxHeight:
      isMobile && isSRP
        ? ""
        : isMobile && !isSRP
        ? "calc(100dvh - 80px)"
        : "100%",
    minHeight:
      isMobile &&
      brandName === "KENZO" &&
      step?.number === MODAL_STEPS.CONSENT.number
        ? "94dvh"
        : isMobile && step?.number === MODAL_STEPS.GENDER.number
        ? "42dvh"
        : isMobile && step?.number === MODAL_STEPS.INTRO_MOBILE.number
        ? "72dvh"
        : isMobile && isShoesProduct && isFirstIntroScreen
        ? "62dvh"
        : "52dvh",
    overflowY: "auto",
    WebkitOverflowScrolling: "touch",
  };

  return {
    titleTextTransform,
    topValue,
    marginValue,
    fontSizeValue,
    headerStyles,
    popupStyles,
    isSRP,
  };
};
