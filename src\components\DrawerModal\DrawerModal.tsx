import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";

import DrawerSteps from "./components/DrawerSteps";

import {
  MODAL_STEPS,
  ERROR_EMPTY,
  GENDERS,
  VALIDATION_RULES,
  BASE_SIZES,
  LANGUAGE_RULES,
} from "../../constants/modal";

import {
  convertFeetAndInchesToCm,
  convertHeightToFeetAndInches,
  getLangBase,
  getStepMaps,
  getSubdomainPrefixUpper,
  INCHtoCM,
  initializeLanguage,
  LBStoKG,
  useIsMobile,
} from "../../utils";
import { useUserContext } from "../../store/userContext";
import "../../i18n";

import {
  checkStocks,
  questionPushTrigger,
  recommend,
  shoeChildrenRecommend,
} from "../../api/endpoints";
import { Box } from "@mui/material";

import { logger } from "../../utils/logging";

import {
  font,
  getQueryParam,
  resultScreenUI,
  titleStyles,
  uxConsentScreen,
  uxGender,
  uxRoute,
} from "../../configs";
import { i18n, loadTranslations } from "../../i18n";
import { ReducedResultType, SizeDataType } from "../../types/result";
import { removeLocalStore } from "../../store/localStoreUtils";
import {
  findBrandByDomain,
  handleParentDomain,
} from "../../configs/configLoader";
import {
  getStepByNumber,
  handleAnalytics,
  handleTrackingStep,
} from "../../utils/tracking";

import { postCloseIframe, postGetSizes } from "../../utils/post";
import {
  MOCK_RECOMMENDATION,
  MOCK_SIZE,
  MOCK_STOCKS_DATA,
} from "../../constants/sizes";

import SrpTitleGuide from "./components/SrpTitleGuide";
import StepTitle from "./components/StepTitle";
import VictoriaBeckhamGuide from "./components/VictoriaBeckhamGuide";
import BackArrowButton from "./components/BackArrowButton";
import CloseIconButton from "./components/CloseIconButton";
import MobileStepTitle from "./components/MobileStepTitle";

import { usePopupStyles } from "../../hooks/usePopupStyles";

import "./index.css";

const DrawerModal = () => {
  const isMobile = useIsMobile();

  const userContext = useUserContext() || undefined;

  const shoeSelectedSize = useUserContext()?.selectedSize;

  const updateSelectedSize = useUserContext()?.updateSelectedSize;

  const [mid, setMid] = useState<string | null>(userContext?.mid || null);
  const [uid, setUid] = useState<string | null>(userContext?.uid || null);
  const [pid, setPid] = useState<string | null>(userContext?.pid || null);

  const { t } = useTranslation("components/intro");

  const localGender = localStorage.getItem("gender") || "";

  const [selectedGender, setSelectedGender] = useState(
    !uxGender ? "" : uxGender === "male" ? GENDERS.M : GENDERS.F
  );

  const [selectedShoeGender, setSelectedShoeGender] = useState<string | null>(
    null
  );

  const [selectedRoute, setSelectedRoute] = useState(uxRoute || "");

  const [shoeQuestion, setShoeQuestion] = useState<string | null>(null);

  const [lingerieQuestion1, setLingerieQuestion1] = useState<string | null>(
    null
  );
  const [lingerieQuestion2, setLingerieQuestion2] = useState<string | null>(
    null
  );
  const [lingerieQuestion3, setLingerieQuestion3] = useState<string | null>(
    null
  );
  const [lingerieQuestion4, setLingerieQuestion4] = useState<string | null>(
    null
  );

  const urlParameters = new URLSearchParams(window.location.search);

  const domain = urlParameters.get("domain");
  const lang = urlParameters.get("lang");
  const mock = urlParameters.get("mock") === "true";
  const forcedError = urlParameters.get("error") === "true";
  const isLingerie = urlParameters.get("lingerie") === "true";

  const [outOfRange, setOutOfRange] = useState(false);
  const OUT_OF_RANGE_TITLE = t("steps.error_outOfRange");

  const [recommendedSize, setRecommendedSize] = useState<any>();
  const [reducedResult, setReducedResult] =
    useState<ReducedResultType | null>();
  const [needValidate, setNeedValidate] = useState(false);
  const [productStockData, setProductStockData] = useState<any | null>(null);
  const [variantId, setVariantId] = useState<string | null>(null);
  const [variantCol, setVariantCol] = useState<string | null>(null);
  const stocksRetrieved = useRef(false);
  const [similarProducts, setSimilarProducts] = useState<any | null>(null);
  const [isSizeUnavailable, setIsSizeUnavailable] = useState<boolean>(false);
  const [selectedSize, setSelectedSize] = useState<SizeDataType | null>(null);
  const [isTranslationsLoaded, setIsTranslationsLoaded] = useState(false);
  const [isShoesProduct, setIsShoesProduct] = useState(false);
  const [isChildrenProduct, setIsChildrenProduct] = useState(false);
  const [productType, setProductType] = useState<string | null>(null);
  const [isScanError, setIsScanError] = useState(false);
  const [policyChecked, setPolicyChecked] = useState(false);

  const MODAL_STEPS_TITLE = [
    t("steps.consent"),
    t("steps.gender"),
    t("steps.intro_mobile"),
    t("steps.intro"),
    t(isShoesProduct ? "steps.device_select_shoe" : "steps.device_select"),
    t("steps.shoe_gender"),
    t("steps.shoe_sizes"),
    t("steps.shoe_questions"),
    t(isShoesProduct ? "steps.qr_code_shoe" : "steps.qr_code"),
    t("steps.belly"),
    t(selectedGender === GENDERS.M ? "steps.torso.male" : "steps.torso.female"),
    t("steps.cuisses"),
    t(isShoesProduct ? "steps.shoe_sizes" : "steps.breasts"),
    t("steps.sleeves"),
    t("steps.lingerie_question_1"),
    t("steps.lingerie_question_2"),
    t("steps.lingerie_question_3"),
    t("steps.lingerie_question_4"),
    t("steps.error"),
    t(isShoesProduct ? "steps.result_shoe" : "steps.result"),
    t("steps.unavailable"),
    t("steps.antibracketing"),
  ];

  const parentDomain = handleParentDomain();

  const [previousStep, setPreviousStep] = useState<{
    number: number;
  } | null>(null);

  const [unit, setUnit] = useState(
    parentDomain?.unit_system === "imperial" ? "feet" : "cm"
  );
  const [height, setHeight] = useState("");
  const [feet, setFeet] = useState("");
  const [inches, setInches] = useState("");

  const [weight, setWeight] = useState<string | undefined>("");
  const [unitWeight, setUnitWeight] = useState(
    parentDomain?.unit_system === "imperial" ? "lbs" : "kg"
  );

  // age values
  const [unitAge, setUnitAge] = useState("year");
  const [age, setAge] = useState("");
  const [ageChildrenYears, setAgeChildrenYears] = useState("");
  const [ageChildrenMonths, setAgeChildrenMonths] = useState("");

  const [step, setStep] = useState<{ number: number } | null>(null);

  const brandDefined = findBrandByDomain();

  const [sizeCountry, setSizeCountry] = useState<string>("");

  const [disableContinue, setDisableContinue] = useState(false);
  const [error, setError] = useState(ERROR_EMPTY);

  const bellyValue = useRef<number>(0);
  const torsoValue = useRef<number>(0);
  const cuissesValue = useRef<number>(0);
  const sleeveValue = useRef<number | undefined>();
  const sizeValue = useRef<string | null>("");
  const cupValue = useRef<string | null>("");

  const popupRef = useRef<HTMLDivElement | null>(null);

  const resultLoading = useRef<boolean>(false);

  const [defaultStep, setDefaultStep] = useState<{ number: number } | null>(
    null
  );

  const {
    titleTextTransform,
    topValue,
    marginValue,
    fontSizeValue,
    headerStyles,
    popupStyles,
    isSRP,
  } = usePopupStyles({
    font,
    titleStyles,
    step,
    isShoesProduct,
    lang,
  });

  useEffect(() => {
    handleTrackingStep(step?.number);
  }, [step]);

  useEffect(() => {
    const mobileDefaultStep = isMobile ? MODAL_STEPS.GENDER : MODAL_STEPS.INTRO;
    const mobileGenderStep =
      isMobile && uxGender ? MODAL_STEPS.INTRO_MOBILE : mobileDefaultStep;

    const resolvedStep = isShoesProduct
      ? MODAL_STEPS.DEVICE_SELECT
      : uxConsentScreen
      ? MODAL_STEPS.CONSENT
      : mobileGenderStep;

    setDefaultStep(resolvedStep);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShoesProduct, uxConsentScreen, isMobile, uxGender]);

  const handleDefaultLocalization = async () => {
    const defaultLang = "fr";

    try {
      await loadTranslations(defaultLang);
      i18n.changeLanguage(defaultLang);
      setIsTranslationsLoaded(true);
    } catch (error) {
      logger.error(`Error loading default translations: ${error}`);
    }
  };

  const handleLocalization = async (language: string) => {
    try {
      await loadTranslations(language);
      i18n.changeLanguage(language);
      setIsTranslationsLoaded(true);
    } catch (error) {
      await handleDefaultLocalization();
    }
  };

  useEffect(() => {
    if (isTranslationsLoaded) return;

    const language = initializeLanguage();

    handleLocalization(language);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTranslationsLoaded]);

  useEffect(() => {
    const localPid = userContext?.pid;
    const localUid = userContext?.uid;
    const localMid = userContext?.mid;

    if (localPid) setPid(localPid);
    if (localUid) setUid(localUid);
    if (localMid) setMid(localMid);
  }, [userContext]);

  useEffect(() => {
    const subdomain = getSubdomainPrefixUpper(parentDomain?.name || "");
    const validCountries = BASE_SIZES.map((size: any) => size.name);
    const countryCode = urlParameters.get("countryCode");

    const resolveCountry = (): string => {
      if (lang === "fr") return "FR";

      if (subdomain && brandDefined?.name === "Lacoste") {
        if (subdomain === "GB") return "UK";
        if (validCountries.includes(subdomain)) return subdomain;
        return "EU";
      }

      return validCountries.includes(subdomain) ? subdomain : "EU";
    };

    const langBase = lang ? getLangBase(lang) : null;

    const langRules =
      LANGUAGE_RULES[langBase as keyof typeof LANGUAGE_RULES] ||
      LANGUAGE_RULES.default;

    const resolvedBraSize =
      countryCode === "GB"
        ? "UK"
        : (parentDomain as any)?.defaultBraSize ||
          langRules.bra ||
          resolveCountry();

    const resolvedUnitSystem =
      langRules.unit || parentDomain?.unit_system || "metric";

    setSizeCountry(isShoesProduct ? "EU" : resolvedBraSize);
    setUnit(resolvedUnitSystem === "imperial" ? "feet" : "cm");
    setUnitWeight(resolvedUnitSystem === "imperial" ? "lbs" : "kg");

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [domain, parentDomain, brandDefined, lang, isShoesProduct]);

  useEffect(() => {
    const isFootwear =
      urlParameters.get("category") === "footwear" ||
      urlParameters.get("footwear") === "true";
    const isChildren =
      urlParameters.get("category") === "children" ||
      urlParameters.get("kids") === "true";

    setIsShoesProduct(false);
    setIsChildrenProduct(false);

    if (isChildren) {
      setProductType("children");
      setIsChildrenProduct(true);
    }

    if (isFootwear) {
      setProductType("shoe");
      setIsShoesProduct(true);
    } else {
      setProductType("clothing");
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      !resultLoading.current &&
      uid &&
      pid &&
      !mock &&
      !(
        userContext?.midType.current === "scan" &&
        !userContext?.isScanChecked.current
      ) &&
      !(
        userContext?.midType.current === "question" &&
        !userContext?.isQuestionPushed.current
      )
    ) {
      logger.log("useEffect GTRS called");
      goToResultScreen(false);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    uid,
    pid,
    mid,
    productStockData,
    variantId,
    variantCol,
    productType,
    mock,
  ]);

  useEffect(() => {
    const cachedDomain = localStorage.getItem("domain");
    const currentDomain = getQueryParam("domain");

    if (currentDomain) {
      localStorage.setItem("domain", currentDomain);
    }

    if (mock) {
      if (userContext && currentDomain)
        userContext.createNewUser(currentDomain);

      return;
    }

    if (userContext) {
      if (currentDomain && cachedDomain !== currentDomain) {
        removeLocalStore("uid");
        removeLocalStore("mid");
        removeLocalStore("domain");
        userContext.createNewUser(currentDomain);
      } else if (!uid && userContext?.uid) {
        setUid(userContext?.uid);
      } else if (!uid && currentDomain) {
        userContext.createNewUser(currentDomain);
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uid, userContext, uxGender, mock]);

  useEffect(() => {
    if (
      isMobile &&
      brandDefined?.name === "Velour Garments" &&
      step?.number === MODAL_STEPS.GENDER.number
    ) {
      setStep(MODAL_STEPS.INTRO_MOBILE);
    }

    if (typeof productType === "string") {
      if (
        productType === "shoe" &&
        !recommendedSize &&
        !shoeSelectedSize &&
        !isScanError &&
        !step
      ) {
        setStep(MODAL_STEPS.DEVICE_SELECT);
      } else {
        // first screen for mobile
        if (isMobile && step?.number === MODAL_STEPS.INTRO.number) {
          setStep(MODAL_STEPS.GENDER);
        }

        if (!isMobile && step?.number === MODAL_STEPS.GENDER.number) {
          setStep(MODAL_STEPS.INTRO);
        }

        if (productType === "clothing" && !step?.number) {
          setStep(defaultStep);
        }

        if (productType === "children" && !step?.number) {
          setStep(defaultStep);
        }
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    step,
    isShoesProduct,
    recommendedSize,
    defaultStep,
    isScanError,
    selectedRoute,
    shoeSelectedSize,
  ]);

  useEffect(() => {
    if (recommendedSize || shoeSelectedSize) {
      setStep(MODAL_STEPS.RESULT);
    }
  }, [recommendedSize, shoeSelectedSize]);

  const handleFieldChange = (e: any, type: string) => {
    const isCustomField =
      type === "age" || type === "feet" || type === "inches";
    const value = isCustomField ? e : e.target.value;

    switch (type) {
      case "height":
        setHeight(value);
        if (unit === "feet") {
          convertHeightToFeetAndInches(value);
        }
        break;
      case "feet":
        setFeet(value);
        convertFeetAndInchesToCm(value, inches);
        break;
      case "inches":
        setInches(value);
        convertFeetAndInchesToCm(feet, value);
        break;
      case "weight":
        setWeight(value);
        break;
      case "age":
        setAge(value);
        break;
      case "year":
        setAgeChildrenYears(value);
        break;
      case "month":
        setAgeChildrenMonths(value);
        break;
      default:
        break;
    }
  };

  const handleUnitChange = (newUnit: string) => {
    if (newUnit !== null) {
      setUnit(newUnit);
      if (newUnit === "feet" && height) {
        const { feetValue, inchesValue } = convertHeightToFeetAndInches(height);
        setFeet(feetValue);
        setInches(inchesValue);
      } else if (newUnit === "cm" && (feet || inches)) {
        const heightStr = convertFeetAndInchesToCm(feet, inches);
        setHeight(heightStr);
      }
    }
  };

  const handleUnitWeightChange = (newUnit: string) => {
    if (newUnit !== null) {
      setUnitWeight(newUnit);
    }
  };

  const handleUnitAgeChange = (newUnit: string) => {
    if (newUnit !== null) {
      setUnitAge(newUnit);
    }
  };

  useEffect(() => {
    if (!uxGender && localGender) {
      setSelectedGender(localGender);
    }
  }, [localGender]);

  const handleGenderSelect = (gender: string) => {
    setSelectedGender(gender);
    localStorage.setItem("gender", gender.replace(`"`, ""));
  };

  useEffect(() => {
    if (recommendedSize || shoeSelectedSize) {
      let adjustedSize = Object.values(
        recommendedSize || shoeSelectedSize
      ) as SizeDataType[];

      if (mock) {
        adjustedSize = MOCK_SIZE;
      }

      const emptyElement = {
        label: "",
        variant_id: "",
        possible: 0,
        label_rank: 0,
        fit_indicators: [],
      };

      adjustedSize = adjustedSize.sort((a, b) => a?.label_rank - b?.label_rank);

      if (adjustedSize.length < 3) {
        if (
          adjustedSize[0]?.label_rank === 1 &&
          adjustedSize[1]?.label_rank === 2
        ) {
          adjustedSize = [emptyElement, ...adjustedSize];
        } else if (
          adjustedSize[0]?.label_rank === 0 &&
          adjustedSize[1]?.label_rank === 1
        ) {
          adjustedSize.push(emptyElement);
        }
      }

      const reducedResultRes: any = {};
      if (adjustedSize[0]) reducedResultRes[0] = adjustedSize[0];
      if (adjustedSize[1]) reducedResultRes[1] = adjustedSize[1];
      if (adjustedSize[2]) reducedResultRes[2] = adjustedSize[2];

      if (
        adjustedSize[1]?.possible === 0 &&
        (brandDefined?.name === "Zadig & Voltaire" ||
          brandDefined?.name === "Loom" ||
          brandDefined?.name === "Lacoste" ||
          brandDefined?.name === "Hast" ||
          brandDefined?.name === "Intersport")
      ) {
        setOutOfRange(true);
        setStep(MODAL_STEPS.ERROR);
      } else setReducedResult(reducedResultRes);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [recommendedSize, shoeSelectedSize, mock]);

  const fieldsValidation = () => {
    const newError = {
      height: { error: false, message: "" },
      weight: { error: false, message: "" },
      age: { error: false, message: "" },
      ageChildren: { error: false, message: "" },
    };

    const heightValue = parseInt(height, 10);
    const localFeet = feet ? Number(feet.split(" ")[0]) : null;
    const localInches = inches ? Number(inches.split(" ")[0]) : null;
    const localWeight = weight ? Number(weight.split(" ")[0]) : null;
    const localAge = age ? Number(age.split(" ")[0]) : null;

    const localAgeChildrenYears = ageChildrenYears
      ? Number(ageChildrenYears.split(" ")[0])
      : null;

    const localAgeChildrenMonths = ageChildrenMonths
      ? Number(ageChildrenMonths.split(" ")[0])
      : null;

    const rule = VALIDATION_RULES;

    if (
      unit === "cm" &&
      height !== "" &&
      ((heightValue < rule.height.min && !isChildrenProduct) ||
        heightValue > rule.height.max)
    ) {
      newError.height = { error: true, message: t("height.errorcm") };
    }

    if (
      (feet !== "" && unit === "feet" && localFeet === 0) ||
      (!feet && inches !== "" && unit === "feet" && localInches === 0)
    ) {
      newError.height = { error: true, message: t("height.errorfeet") };
    }

    if (unit === "feet" && localFeet !== null && localInches !== null) {
      const totalInches = localFeet * 12 + localInches; // convert feet and inches to total inches
      const minHeightInches = rule.height.feet.min * 12 + rule.height.feet.max; // 4 feet 5 inches = 53 inches
      const maxHeightInches =
        rule.height.inches.min * 12 + rule.height.inches.max; // 6 feet 9 inches = 81 inches

      if (
        (totalInches < minHeightInches && !isChildrenProduct) ||
        totalInches > maxHeightInches ||
        localFeet === 0
      ) {
        newError.height = { error: true, message: t("height.errorfeet") };
      }
    }

    if (weight !== "") {
      if (
        (unitWeight === "kg" &&
          localWeight &&
          ((localWeight < rule.weight.kg.min && !isChildrenProduct) ||
            localWeight > rule.weight.kg.max)) ||
        localWeight === 0
      ) {
        newError.weight = { error: true, message: t("weight.errorkg") };
      } else if (
        unitWeight === "lbs" &&
        localWeight &&
        ((localWeight < rule.weight.lbs.min && !isChildrenProduct) ||
          localWeight > rule.weight.lbs.max)
      ) {
        newError.weight = { error: true, message: t("weight.errorlbs") };
      }
    }

    if (isChildrenProduct) {
      if (ageChildrenMonths !== "") {
        if (
          (localAgeChildrenMonths && localAgeChildrenMonths >= 24) ||
          localAgeChildrenMonths === 0
        ) {
          newError.ageChildren = { error: true, message: t("age.error") };
        }
      }

      if (ageChildrenYears !== "") {
        if (
          (localAgeChildrenYears && localAgeChildrenYears >= 18) ||
          localAgeChildrenYears === 0
        ) {
          newError.ageChildren = { error: true, message: t("age.error") };
        }
      }
    } else {
      if (age !== "") {
        if (
          (localAge && (localAge < rule.age.min || localAge > rule.age.max)) ||
          localAge === 0
        ) {
          newError.age = { error: true, message: t("age.error") };
        }
      }
    }

    setError(newError);
  };

  // all fields validation
  useEffect(() => {
    if (needValidate) fieldsValidation();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [needValidate, height, unit, weight, unitWeight, age, feet, inches]);

  const handleGoBack = () => {
    if (isMobile && step?.number === MODAL_STEPS.DEVICE_SELECT.number) {
      setStep(MODAL_STEPS.INTRO_MOBILE);
    } else if (!isMobile && step?.number === MODAL_STEPS.DEVICE_SELECT.number) {
      setStep(MODAL_STEPS.INTRO);
    } else if (step?.number === MODAL_STEPS.QR_CODE.number) {
      if (isShoesProduct) {
        setStep(MODAL_STEPS.DEVICE_SELECT);
      } else if (
        !previousStep &&
        (uxRoute === "question_pushScan" || uxRoute === "question_only")
      ) {
        setStep(isMobile ? MODAL_STEPS.INTRO_MOBILE : MODAL_STEPS.INTRO);
      } else if (previousStep !== null) {
        setStep(previousStep);
        setPreviousStep(null);
      } else if (uxRoute === "default") {
        setStep(MODAL_STEPS.DEVICE_SELECT);
      }
    } else if (step?.number === MODAL_STEPS.BELLY.number) {
      if (uxRoute === "question_only") {
        setStep(isMobile ? MODAL_STEPS.INTRO_MOBILE : MODAL_STEPS.INTRO);
      } else if (uxRoute === "default") {
        setStep(MODAL_STEPS.DEVICE_SELECT);
      }
    } else if (!uxGender && step?.number === MODAL_STEPS.INTRO_MOBILE.number) {
      setStep(MODAL_STEPS.GENDER);
    } else if (step?.number === MODAL_STEPS.TORSO.number) {
      setStep(MODAL_STEPS.BELLY);
    } else if (step?.number === MODAL_STEPS.CUISSES.number) {
      setStep(MODAL_STEPS.TORSO);
    } else if (step?.number === MODAL_STEPS.SLEEVES.number) {
      setStep(MODAL_STEPS.CUISSES);
    } else if (step?.number === MODAL_STEPS.BREASTS.number) {
      if (isShoesProduct) {
        setStep(MODAL_STEPS.SHOE_GENDER);
      } else {
        setStep(
          uxRoute === "none" && selectedGender === GENDERS.F
            ? defaultStep
            : MODAL_STEPS.TORSO
        );
      }
    } else if (step?.number === MODAL_STEPS.RESULT.number) {
      restart();
    } else if (step?.number === MODAL_STEPS.ERROR.number) {
      restart();
    } else if (step?.number === MODAL_STEPS.SHOE_GENDER.number) {
      setStep(MODAL_STEPS.DEVICE_SELECT);
    } else if (step?.number === MODAL_STEPS.SHOE_QUESTIONS.number) {
      setStep(MODAL_STEPS.BREASTS);
    } else if (step?.number === MODAL_STEPS.LINGERIE_QUESTION_1.number) {
      setStep(MODAL_STEPS.BREASTS);
    } else if (step?.number === MODAL_STEPS.LINGERIE_QUESTION_2.number) {
      setStep(MODAL_STEPS.LINGERIE_QUESTION_1);
    } else if (step?.number === MODAL_STEPS.LINGERIE_QUESTION_3.number) {
      setStep(MODAL_STEPS.LINGERIE_QUESTION_2);
    } else if (step?.number === MODAL_STEPS.LINGERIE_QUESTION_4.number) {
      setStep(MODAL_STEPS.LINGERIE_QUESTION_3);
    }

    if (step && step?.number) {
      handleAnalytics("action", "go_back", {
        key: "current_step",
        value: getStepByNumber(step.number),
      });
    }

    setPreviousStep(null);
  };

  const computeDisableContinue = (): boolean => {
    const hasValidationErrors = Object.values(error).some(
      (field) => field.error
    );
    const stableFieldsNotFound = !weight || !age || !selectedGender;

    if (isChildrenProduct) {
      const allFieldsNotFound =
        (unit === "feet" ? !feet || !inches : !height) ||
        !weight ||
        !selectedGender;
      const isAddedAllFields =
        unitAge === "month" ? !ageChildrenMonths : !ageChildrenYears;

      return hasValidationErrors || allFieldsNotFound || isAddedAllFields;
    } else {
      if (isMobile) {
        if (step === MODAL_STEPS.GENDER) {
          return !selectedGender;
        }
        if (step === MODAL_STEPS.INTRO_MOBILE) {
          return unit === "feet"
            ? !feet || !inches || hasValidationErrors || stableFieldsNotFound
            : !height || stableFieldsNotFound || hasValidationErrors;
        }
      } else {
        return unit === "feet"
          ? !feet || !inches || hasValidationErrors || stableFieldsNotFound
          : !height || stableFieldsNotFound || hasValidationErrors;
      }
    }

    // Default return value
    return false;
  };

  useEffect(() => {
    let disabled = computeDisableContinue();

    if (brandDefined?.name === "Nina Ricci") {
      if (!policyChecked) {
        disabled = true;
      }
    }

    setDisableContinue(disabled);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    unit,
    selectedGender,
    feet,
    inches,
    height,
    weight,
    age,
    error,
    step,
    isMobile,
    policyChecked,
  ]);

  useEffect(() => {
    const handleMessage = async (event: any) => {
      let eventData = event?.data;
      const retailer = eventData?.retailer;
      if (eventData?.sizes?.length) {
        const sizesData = eventData?.sizes?.map((item: any) => ({
          variant_reference: item?.variantId,
          color: item?.color,
          quantity:
            brandDefined?.name === "Asphalte"
              ? item?.quantity
              : item?.quantity < 0
              ? 1
              : item?.quantity,
        }));
        setProductStockData(sizesData);
        stocksRetrieved.current = true;
      } else {
        if (retailer === "asphalte") {
          logger.log("Event received from Asphalte", event);
          if (eventData?.url?.variant) {
            logger.log("Asphalte Variant received");
            setVariantId(eventData?.url?.variant);
          } else if (eventData?.url?.color) {
            setVariantCol(eventData?.url?.color);
          }
        }
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const sendSizesLoop = async () => {
      for (let i = 0; i < 5; i++) {
        postGetSizes();
        if (i < 4) await new Promise((res) => setTimeout(res, 500));
      }
    };
    sendSizesLoop();
  }, []);

  const goToMockResultScreen = () => {
    if (!forcedError) {
      const result = MOCK_RECOMMENDATION;
      setRecommendedSize(result);

      if (!selectedGender && localGender) {
        setSelectedGender(localGender);
      } else if (
        !selectedGender &&
        !localGender &&
        !localGender.includes(GENDERS.F)
      ) {
        setSelectedGender(GENDERS.M);
        localStorage.setItem("gender", GENDERS.M);
      }

      const stocksData = MOCK_STOCKS_DATA;
      setProductStockData(
        stocksData.map(
          (item: { variant_reference: string; quantity: number }) => {
            return {
              ...item,
              quantity: item.quantity < 0 ? 1 : item.quantity,
            };
          }
        )
      );

      setStep(MODAL_STEPS.RESULT);
    } else {
      setStep(MODAL_STEPS.ERROR);
    }
  };

  const goToResultScreen = async (isResultGenerated?: boolean) => {
    resultLoading.current = true;

    if (pid) {
      try {
        const isFootwear =
          urlParameters.get("category") === "footwear" ||
          urlParameters.get("footwear") === "true";
        const isChildren =
          urlParameters.get("category") === "children" ||
          urlParameters.get("kids") === "true";

        const result =
          isFootwear || isChildren
            ? await shoeChildrenRecommend(pid, isChildren)
            : await recommend(pid, productStockData, variantId, variantCol);

        const normalizedResult = Array.isArray(result?.data)
          ? result?.data
          : result?.data
          ? [result?.data]
          : [];

        if (result?.success && normalizedResult.length > 0) {
          if (isFootwear && updateSelectedSize) {
            updateSelectedSize(result?.data);
          } else {
            setRecommendedSize(result?.data);
          }

          if (!selectedGender && localGender) {
            setSelectedGender(localGender);
          } else if (!selectedGender && !localGender) {
            setSelectedGender(GENDERS.M);
            localStorage.setItem("gender", GENDERS.M);
          }

          if (
            !resultScreenUI.generateResult &&
            !productStockData?.length &&
            !stocksRetrieved.current &&
            brandDefined?.name !== "Asphalte" &&
            brandDefined?.name !== "SAAJ" &&
            brandDefined?.name !== "Alme"
          ) {
            if (domain && pid) {
              const stocksData = await checkStocks(
                domain,
                pid,
                brandDefined?.name
              );
              if (stocksData !== null) {
                const resultStocksData = stocksData.map(
                  (item: { variant_reference: string; quantity: number }) => {
                    return {
                      ...item,
                      quantity: item.quantity < 0 ? 1 : item.quantity,
                    };
                  }
                );

                logger.log(`stocksData: ${resultStocksData}`);
                if (resultStocksData.length > 0) {
                  setProductStockData(resultStocksData);
                }
              }
            }
          } else {
            setStep(MODAL_STEPS.RESULT);
          }
        } else {
          setStep(
            isFootwear
              ? MODAL_STEPS.DEVICE_SELECT
              : isResultGenerated
              ? MODAL_STEPS.ERROR
              : defaultStep
          );
          removeLocalStore("gender");
          if (!uxGender) setSelectedGender("");
        }
      } catch (error) {
        logger.error(`error:  ${error}`);
        setStep(MODAL_STEPS.ERROR);
      }
    } else {
      logger.warn("Recommendation uncomputable: no pid is found");
    }

    resultLoading.current = false;
  };

  useEffect(() => {
    const checkIsSizeUnavailable = (
      quantity: boolean | number | undefined
    ): boolean => {
      if (typeof quantity === "boolean") {
        return !quantity;
      }
      if (typeof quantity === "number") {
        return quantity < 1;
      }
      return true;
    };

    if (
      productStockData !== null &&
      recommendedSize !== null &&
      recommendedSize !== undefined
    ) {
      const findVariant = productStockData?.find(
        (item: any) =>
          item.variant_reference === selectedSize?.variant_id ||
          item.variant_reference === selectedSize?.reference
      );

      if (findVariant) {
        const isSizeUnavailable = checkIsSizeUnavailable(findVariant?.quantity);
        setIsSizeUnavailable(isSizeUnavailable);
        setStep(
          isSizeUnavailable
            ? MODAL_STEPS.UNAVAILABLE_RESULT
            : MODAL_STEPS.RESULT
        );
      } else if (!resultScreenUI.generateResult) {
        setIsSizeUnavailable(true);
      }
    }
  }, [productStockData, selectedSize, recommendedSize]);

  // send data after result
  const sendQuestionAnswers = async (isSkipResult?: boolean) => {
    const isMale = selectedGender === GENDERS.M;

    const heightLocal =
      unit === "feet" ? INCHtoCM(`${feet} ${inches}`) : height;
    const weightLocal =
      unitWeight === "lbs" && weight ? LBStoKG(parseInt(weight)) : weight;

    const commonData = {
      gender: isMale ? "male" : "female",
      age: age ? parseInt(age, 10) : 0,
      height: heightLocal ? parseInt(heightLocal, 10) : 0,
      weight: weightLocal ? parseInt(weightLocal, 10) : 0,
      sleeve_length: sleeveValue.current ?? null,
    };

    const defaultQuestions = isMale
      ? { male_belly: 2, male_hip: 2, male_chest: 2 }
      : {
          female_belly: 2,
          female_hip: 2,
          female_bra_size: isSkipResult ? null : sizeValue.current,
          female_bra_cup: isSkipResult ? null : cupValue.current,
        };

    const dynamicQuestions = isMale
      ? {
          male_belly: bellyValue.current || 2,
          male_hip: torsoValue.current || 2,
          male_chest: cuissesValue.current || 2,
        }
      : {
          female_belly: bellyValue.current || 2,
          female_hip: torsoValue.current || 2,
          female_bra_size: isSkipResult ? null : sizeValue.current,
          female_bra_cup: isSkipResult ? null : cupValue.current,
        };

    const data = {
      ...commonData,
      questions: uxRoute === "none" ? defaultQuestions : dynamicQuestions,
    };

    const ageChildren =
      unitAge === "year" ? ageChildrenYears : ageChildrenMonths;

    const childrenData = {
      gender: selectedGender,
      height: heightLocal ? parseInt(heightLocal, 10) : 0,
      weight: weightLocal ? parseInt(weightLocal, 10) : 0,
      age: {
        type: unitAge || "year",
        value: ageChildren ? parseInt(ageChildren, 10) : 0,
      },
    };

    const lingerieData = {
      ...commonData,
      bra_size: {
        cup: cupValue.current,
        band: sizeValue.current,
      },
      answers: {
        lingerieQuestion1: lingerieQuestion1,
        lingerieQuestion2: lingerieQuestion2,
        lingerieQuestion3: lingerieQuestion3,
        lingerieQuestion4: lingerieQuestion4,
      },
    };

    const getPayload = () => {
      if (isChildrenProduct) return childrenData;
      if (isLingerie) return lingerieData;
      return data;
    };

    if (mock) return goToMockResultScreen();

    resultLoading.current = true;

    const payload = getPayload();
    const isSpecificProject = isChildrenProduct || isLingerie;

    const result =
      isShoesProduct && uid && shoeQuestion && selectedShoeGender
        ? await questionPushTrigger({
            user: {
              id: uid,
              answers: {
                usual_size: {
                  value: sizeValue.current,
                  system: sizeCountry,
                },
                foot_width: shoeQuestion,
                gender: selectedShoeGender,
              },
            },
          })
        : await userContext?.pushQuestionAnswers(payload, isSpecificProject);

    resultLoading.current = false;

    if (result) await goToResultScreen(true);

    return result;
  };

  // restart and refresh all data
  const restart = () => {
    setAgeChildrenYears("");
    setAgeChildrenMonths("");
    setSelectedShoeGender(null);
    setShoeQuestion(null);
    setStep(uxConsentScreen ? MODAL_STEPS.CONSENT : defaultStep);
    setHeight("");
    setUnit(parentDomain?.unit_system === "imperial" ? "feet" : "cm");
    setFeet("");
    setInches("");
    setWeight("");
    setUnitWeight(parentDomain?.unit_system === "imperial" ? "lbs" : "kg");
    setAge("");
    if (!uxGender) {
      setSelectedGender("");
      removeLocalStore("gender");
    }
    setDisableContinue(true);
    bellyValue.current = 0;
    torsoValue.current = 0;
    cuissesValue.current = 0;
    sizeValue.current = "";
    cupValue.current = "";
    setRecommendedSize(null);
    setReducedResult(null);
    if (isShoesProduct) {
      sessionStorage.setItem("shoe_retry", Date.now().toString());
    }
  };

  useEffect(() => {
    return () => restart();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const closeIframe = () => {
    postCloseIframe();

    if (step && step?.number) {
      handleAnalytics("action", "exit", null);
    }
  };

  const handleOverlayClick = (e: any) => {
    if (popupRef.current && !popupRef.current.contains(e.target)) {
      closeIframe();
    }
  };

  return (
    <Box
      className="drawer-overlay"
      onClick={(e: any) => {
        const target = e.target as HTMLElement;

        if (["INPUT", "TEXTAREA", "SELECT"].includes(target.tagName)) {
          return;
        }

        if (e.target === e.currentTarget) {
          handleOverlayClick(e);
        }
      }}
      style={{
        fontFamily: isMobile
          ? `${font}, sans-serif`
          : `${font}, sans-serif !important`,
      }}
    >
      <Box
        className="drawer-popup"
        ref={popupRef}
        onClick={(e) => e.stopPropagation()}
        style={popupStyles}
      >
        <Box>
          {isTranslationsLoaded ? (
            <>
              <Box className="drawer-popup-header" style={headerStyles}>
                <BackArrowButton
                  {...{
                    step,
                    isShoesProduct,
                    brandName: brandDefined?.name,
                    handleGoBack,
                  }}
                />
                {brandDefined?.name === "SRP" && <SrpTitleGuide />}
                <StepTitle
                  step={step}
                  isShoesProduct={isShoesProduct}
                  isSRP={isSRP}
                  outOfRange={outOfRange}
                  titleStyles={titleStyles}
                  titleTextTransform={titleTextTransform}
                  fontSizeValue={fontSizeValue}
                  topValue={topValue}
                  marginValue={marginValue}
                  MODAL_STEPS_TITLE={MODAL_STEPS_TITLE}
                />
                {brandDefined?.name === "Victoria Beckham" && (
                  <VictoriaBeckhamGuide
                    step={
                      step ?? { number: getStepMaps(isMobile)[0].stepNumber }
                    }
                    stepMap={getStepMaps(isMobile)}
                  />
                )}
                <CloseIconButton
                  {...{
                    step,
                    isShoesProduct,
                    brandName: brandDefined?.name,
                    titleStyles,
                    closeIframe,
                  }}
                />
              </Box>
              <Box
                className="drawer-popup-content"
                style={{
                  backgroundColor:
                    brandDefined?.name === "SRP" ? "#F7F7F7" : "#FFFFFF",
                }}
              >
                <MobileStepTitle
                  {...{
                    isSRP,
                    isShoesProduct,
                    brandName: brandDefined?.name,
                    step,
                    titleTextTransform,
                    titleStyles,
                    outOfRange,
                    MODAL_STEPS_TITLE,
                    OUT_OF_RANGE_TITLE,
                  }}
                />
                <DrawerSteps
                  step={step}
                  error={error}
                  height={height}
                  unit={unit}
                  feet={feet}
                  inches={inches}
                  unitWeight={unitWeight}
                  weight={weight}
                  age={age}
                  ageChildrenYears={ageChildrenYears}
                  ageChildrenMonths={ageChildrenMonths}
                  unitAge={unitAge}
                  handleUnitAgeChange={handleUnitAgeChange}
                  selectedGender={selectedGender}
                  selectedShoeGender={selectedShoeGender}
                  setSelectedShoeGender={setSelectedShoeGender}
                  disableContinue={disableContinue}
                  bellyValue={bellyValue}
                  torsoValue={torsoValue}
                  cuissesValue={cuissesValue}
                  sizeValue={sizeValue}
                  cupValue={cupValue}
                  sleeveValue={sleeveValue}
                  sendQuestionAnswers={sendQuestionAnswers}
                  handleFieldChange={handleFieldChange}
                  handleUnitChange={handleUnitChange}
                  handleUnitWeightChange={handleUnitWeightChange}
                  handleGenderSelect={handleGenderSelect}
                  setNeedValidate={setNeedValidate}
                  recommendedSize={recommendedSize || shoeSelectedSize}
                  setRecommendedSize={setRecommendedSize}
                  reducedResult={reducedResult}
                  selectedRoute={selectedRoute}
                  setSelectedRoute={setSelectedRoute}
                  sizeCountry={sizeCountry}
                  setSizeCountry={setSizeCountry}
                  productStockData={productStockData}
                  variantId={variantId}
                  variantCol={variantCol}
                  similarProducts={similarProducts}
                  isSizeUnavailable={
                    isSizeUnavailable || selectedSize?.variant_id === null
                  }
                  selectedSize={selectedSize}
                  setSelectedSize={setSelectedSize}
                  setStep={setStep}
                  setPreviousStep={setPreviousStep}
                  restart={restart}
                  setSimilarProducts={setSimilarProducts}
                  isChildrenProduct={isChildrenProduct}
                  setIsScanError={setIsScanError}
                  outOfRange={outOfRange}
                  shoeQuestion={shoeQuestion}
                  setShoeQuestion={setShoeQuestion}
                  policyChecked={policyChecked}
                  setPolicyChecked={setPolicyChecked}
                  // Lingerie
                  lingerieQuestion1={lingerieQuestion1}
                  setLingerieQuestion1={setLingerieQuestion1}
                  lingerieQuestion2={lingerieQuestion2}
                  setLingerieQuestion2={setLingerieQuestion2}
                  lingerieQuestion3={lingerieQuestion3}
                  setLingerieQuestion3={setLingerieQuestion3}
                  lingerieQuestion4={lingerieQuestion4}
                  setLingerieQuestion4={setLingerieQuestion4}
                />
              </Box>
            </>
          ) : null}
        </Box>
      </Box>
    </Box>
  );
};

export default DrawerModal;
