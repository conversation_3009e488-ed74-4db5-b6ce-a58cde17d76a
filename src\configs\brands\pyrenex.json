{"name": "Pyrenex", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "14px", "fontColor": "#777777", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#222222", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid 000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#222222", "borderRadius": "30px", "borderColor": "#222222", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#002e6c", "fontColor": "#FFFFFF", "borderRadius": "30px", "borderColor": "#002e6c", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "14px", "top": "10px", "right": "", "activeColor": "#222222", "inactiveColor": "#777777"}}, "2": {"routeCTA": {"borderColor": "#222222", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#FFFFFF"}}, "3": {"qrcode": {"backgroundColor": "#222222"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#222222", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#222222", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#777777", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#222222", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "14px", "fontColor": "#383838", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "<PERSON><PERSON>", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#222222", "fontSize": "20px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#777777", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textTransform": "capitalize", "borderRadius": "30px", "borderColor": "#222222", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#002e6c", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "capitalize", "borderRadius": "30px", "borderColor": "#002e6c", "borderWidth": "1px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#383838", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#222222", "borderRadius": "30px", "fontSize": "14px", "fontWeight": "400", "fontColor": "#222222", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#777777", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#002e6c", "borderWidth": "2px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#222222"}, "unfocused": {"color": "#777777"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#222222", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#222222", "borderWidth": "1px", "borderRadius": "5px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#222222", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#f8f6f4", "fontColor": "#777777", "fontSize": "14px", "fontWeight": "400", "borderRadius": "5px", "borderColor": "#f8f6f4", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#f8f6f4", "fontColor": "#002e6c", "fontSize": "14px", "fontWeight": "400", "borderRadius": "5px", "borderColor": "#002e6c", "borderWidth": "1px", "textTransform": "capitalize"}}, "skip": {"fontColor": "#383838", "fontWeight": "400", "fontStyle": "", "fontSize": "14px"}}}}}