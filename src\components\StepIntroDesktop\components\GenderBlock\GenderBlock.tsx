import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";

import { font, welcomeScreenUI } from "../../../../configs";
import { GENDERS } from "../../../../constants/modal";
import {
  capitalizeFirstLetter,
  isWithArrow,
  useIsMobile,
} from "../../../../utils";
import { findBrandByDomain } from "../../../../configs/configLoader";
import { handleAnalytics } from "../../../../utils/tracking";
import { logger } from "../../../../utils/logging";
import GenderButton from "./GenderButton";

import "./index.css";

interface GenderBlockProps {
  selectedGender: string;
  handleGenderSelect: (newUnit: string) => void;
  nextStep: () => void;
  nextStepDesktop: () => void;
  disableContinue: boolean;
  isChildrenProduct: boolean;
}

type GenderButtonType = "focused" | "unfocused";

const GenderBlock: React.FC<GenderBlockProps> = ({
  selectedGender,
  handleGenderSelect,
  nextStep,
  nextStepDesktop,
  disableContinue,
  isChildrenProduct,
}) => {
  const isMobile = useIsMobile();
  const isLargeHeightRelative = useMediaQuery({
    maxHeight: !isMobile ? 830 : 778,
    minWidth: 767,
  });

  const [hovered, setHovered] = useState<string | null>(null);
  const { t } = useTranslation("components/intro");

  const brandDefined = findBrandByDomain();

  const getButtonsStyles = (type: GenderButtonType) => ({
    backgroundColor: welcomeScreenUI.genderCTA[type].backgroundColor,
    borderRadius: welcomeScreenUI.genderCTA[type].borderRadius,
    borderColor: welcomeScreenUI.genderCTA[type].borderColor,
    borderWidth: welcomeScreenUI.genderCTA[type].borderWidth,
    fontSize: welcomeScreenUI.genderCTA[type].fontSize,
    fontWeight: welcomeScreenUI.genderCTA[type].fontWeight,
    textTransform:
      brandDefined?.name === "Lacoste" && isMobile
        ? "uppercase"
        : (welcomeScreenUI.genderCTA[type]
            .textTransform as React.CSSProperties["textTransform"]),
    color: welcomeScreenUI.genderCTA[type].fontColor,
    fontFamily: `${font}, sans-serif${isMobile ? "" : " !important"}`,
    letterSpacing: (welcomeScreenUI.genderCTA[type] as any)?.letterSpacing,
  });

  const femaleButtonStyles =
    selectedGender === GENDERS.F
      ? getButtonsStyles("focused")
      : getButtonsStyles("unfocused");

  const maleButtonStyles =
    selectedGender === GENDERS.M
      ? getButtonsStyles("focused")
      : getButtonsStyles("unfocused");

  const genderMaleKey = isChildrenProduct
    ? "gender.children.male"
    : "gender.male";
  const genderFemaleKey = isChildrenProduct
    ? "gender.children.female"
    : "gender.female";

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();

    if (e.key === "Tab") {
      e.preventDefault();

      const selectGender = !selectedGender
        ? GENDERS.F
        : selectedGender === GENDERS.F
        ? GENDERS.M
        : GENDERS.M;

      handleGenderSelect(selectGender);

      if (selectedGender === GENDERS.M) {
        const heightInput = document.querySelector(
          "#height"
        ) as HTMLInputElement | null;
        if (heightInput) {
          heightInput?.focus();
        } else {
          logger.log("Element with id 'height' not found.");
        }
      }
    }

    if (e.key === "Enter" && !disableContinue) {
      nextStepDesktop();
      (document.activeElement as HTMLElement)?.blur();
    }
  };

  return (
    <div
      className="gender-buttons"
      style={{
        marginBottom:
          isLargeHeightRelative && brandDefined?.name !== "Sporty & Rich"
            ? 0
            : "30px",
        justifyContent: isWithArrow(brandDefined?.name) ? "center" : "",
      }}
      onKeyDown={handleKeyDown}
      onFocus={() => handleGenderSelect(GENDERS.F)}
      tabIndex={0}
    >
      <GenderButton
        gender={GENDERS.F}
        selectedGender={selectedGender}
        baseStyles={femaleButtonStyles}
        hovered={hovered}
        setHovered={setHovered}
        brandName={brandDefined?.name}
        label={
          femaleButtonStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t(genderFemaleKey))
            : t(genderFemaleKey)
        }
        onSelect={() => {
          handleGenderSelect(GENDERS.F);
          if (isMobile) nextStep();
          handleAnalytics("action", "gender_selection", {
            key: "value",
            value: GENDERS.F,
          });
        }}
      />

      <GenderButton
        gender={GENDERS.M}
        selectedGender={selectedGender}
        baseStyles={maleButtonStyles}
        hovered={hovered}
        setHovered={setHovered}
        brandName={brandDefined?.name}
        label={
          maleButtonStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t(genderMaleKey))
            : t(genderMaleKey)
        }
        onSelect={() => {
          handleGenderSelect(GENDERS.M);
          if (isMobile) nextStep();
          handleAnalytics("action", "gender_selection", {
            key: "value",
            value: GENDERS.M,
          });
        }}
      />
    </div>
  );
};

export default GenderBlock;
