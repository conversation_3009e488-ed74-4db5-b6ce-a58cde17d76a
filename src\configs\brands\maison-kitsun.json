{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#202020", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#4A4A4A", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#202020", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#202020", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #202020"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#202020", "borderRadius": "0", "borderColor": "#B8B8B8", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#202020", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#202020", "inactiveColor": "#B8B8B8"}}, "2": {"routeCTA": {"borderColor": "#202020", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#202020"}}, "3": {"qrcode": {"backgroundColor": "#202020"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "70px", "fontColor": "#202020", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#202020", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "700", "fontSize": "12px", "fontColor": "#202020", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#B8B8B8", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "700", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "700", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "12px", "fontColor": "#202020", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#202020", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#202020", "textTransform": "capitalize", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#B8B8B8", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Montserrat", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#202020", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#202020", "fontSize": "12px"}, "cta": {"unfocused": {"backgroundColor": "#B8B8B8", "fontWeight": "700", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#202020", "fontWeight": "700", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#202020", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#B8B8B8", "borderRadius": "0", "fontSize": "10px", "fontWeight": "700", "fontColor": "#202020", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#B8B8B8", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "700", "fontSize": "12px", "fontColor": "#202020", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#202020", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "700", "fontSize": "12px", "fontColor": "#202020", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#202020"}, "unfocused": {"color": "#B8B8B8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#b8b8b8", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#202020", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#b8b8b8", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#202020", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#202020", "fontSize": "12px", "fontWeight": "700", "borderRadius": "0px", "borderColor": "#b8b8b8", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#202020", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "700", "borderRadius": "0px", "borderColor": "", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#202020", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}