import { useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

import MeasuredBy from "../molecules/MeasuredBy";

import ArrowSelectIcon from "../../icons/ArrowSelectIcon";
import PhotoCameraIcon from "../../assets/icons/PhotoCameraIcon";
import HelpIcon from "../../assets/icons/HelpIcon";

import {
  choiceScreenUI,
  font,
  subtitlesStyles,
  titleStyles,
} from "../../configs";
import { capitalizeFirstLetter, useIsMobile } from "../../utils";

import "./index.css";

interface StepDeviceSelectProps {
  step: any;
  isShoesProduct: boolean;
  nextPhotoStep: () => void;
  nextQuestionsStep: () => void;
}

const StepDeviceSelect: React.FC<StepDeviceSelectProps> = ({
  step,
  isShoesProduct,
  nextPhotoStep,
  nextQuestionsStep,
}) => {
  const isMobile = useIsMobile();
  const { t } = useTranslation("components/device_select");

  const [hoveredBlock, setHoveredBlock] = useState<
    "questions" | "photo" | null
  >(null);
  const [activeBlock, setActiveBlock] = useState<"questions" | "photo" | null>(
    null
  );

  const desktopStyles = !isMobile
    ? {
        marginTop: isShoesProduct ? "50px" : "100px",
        gap: "20px",
      }
    : undefined;

  const getButtonStyles = (type: "questions" | "photo") => {
    const defaultDeviceSelect = {
      default: {
        backgroundColor: "#FFFFFF",
        fontColor: "#000000",
        borderRadius: "0px",
        borderColor: "#000000",
        borderWidth: "1px",
        fontSize: "12px",
        fontWeight: "400",
        textTransform: "",
      },
      hover: {
        backgroundColor: "#FFFFFF",
        fontColor: "#000000",
        borderRadius: "0px",
        borderColor: "#000000",
        borderWidth: "2px",
        fontSize: "12px",
        fontWeight: "400",
        textTransform: "",
      },
    };

    const deviceSelect =
      (choiceScreenUI as any)?.deviceSelect || defaultDeviceSelect;

    let state: "default" | "hover" = "default";
    if (hoveredBlock === type) {
      state = "hover";
    }

    return {
      backgroundColor: deviceSelect[state]?.backgroundColor,
      color: deviceSelect[state]?.fontColor,
      borderRadius: deviceSelect[state]?.borderRadius,
      borderColor: deviceSelect[state]?.borderColor,
      borderWidth: deviceSelect[state]?.borderWidth,
      borderStyle: "solid",
      fontSize: deviceSelect[state]?.fontSize,
      fontWeight: deviceSelect[state]?.fontWeight,
      outline: "none",
      textTransform: deviceSelect[state]?.textTransform,
    };
  };

  const fontFamily = `${font}, sans-serif`;

  const handleBlockClick = (type: "questions" | "photo") => {
    if (type === "questions") nextQuestionsStep();
    else nextPhotoStep();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();
    let handledType = (activeBlock === "questions" ? "photo" : "questions") as
      | "questions"
      | "photo"
      | null;

    if (e.key === "Tab") {
      e.preventDefault();
      setActiveBlock(handledType);
    }

    if (e.key === "Enter" && activeBlock) {
      handleBlockClick(activeBlock);
    }
  };

  const handleFocus = () => setActiveBlock("questions");

  const buttonConfigs: {
    type: "questions" | "photo";
    icon: JSX.Element;
    title: string;
    description: string;
  }[] = [
    {
      type: "questions",
      icon: <HelpIcon fill={titleStyles.color} />,
      title: isShoesProduct ? t("shoe.titleQuestions") : t("titleQuestions"),
      description: isShoesProduct
        ? t("shoe.descriptionQuestions")
        : t("descriptionQuestions"),
    },
    {
      type: "photo",
      icon: <PhotoCameraIcon fill={titleStyles.color} />,
      title: isShoesProduct ? t("shoe.titleScan") : t("titleScan"),
      description: isShoesProduct
        ? t("shoe.descriptionScan")
        : t("descriptionScan"),
    },
  ];

  const orderedButtons = isShoesProduct
    ? buttonConfigs.reverse()
    : buttonConfigs;

  return (
    <>
      {isShoesProduct ? (
        <span
          className="description"
          style={{
            marginTop: "10px",
            ...subtitlesStyles,
            justifyContent:
              subtitlesStyles.textAlign as React.CSSProperties["justifyContent"],
            textAlign:
              subtitlesStyles.textAlign as React.CSSProperties["textAlign"],
            textTransform: (subtitlesStyles.textTransform === "capitalize"
              ? "none"
              : subtitlesStyles.textTransform) as React.CSSProperties["textTransform"],
          }}
        >
          {subtitlesStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("shoe.shoesDescription"))
            : t("shoe.shoesDescription")}
        </span>
      ) : null}
      <Box
        className="main-container"
        style={desktopStyles}
        onFocus={handleFocus}
      >
        {orderedButtons.map((btn) => (
          <Box
            key={btn.type}
            className="button-container"
            tabIndex={0}
            role="button"
            onClick={() => handleBlockClick(btn.type)}
            style={getButtonStyles(btn.type)}
            onMouseEnter={() => setHoveredBlock(btn.type)}
            onMouseLeave={() => setHoveredBlock(null)}
            onMouseDown={() => setActiveBlock(btn.type)}
            onMouseUp={() => setActiveBlock(null)}
            onKeyDown={handleKeyDown}
          >
            <Box className="parts-container">
              <Box className="title-container">
                <Box className="icon-text">
                  {btn.icon}
                  <Typography className="title" style={{ fontFamily }}>
                    {btn.title}
                  </Typography>
                </Box>
                <Typography className="title2" style={{ fontFamily }}>
                  {btn.description}
                </Typography>
              </Box>
              <ArrowSelectIcon />
            </Box>
          </Box>
        ))}
      </Box>
      <MeasuredBy step={step} />
    </>
  );
};

export default StepDeviceSelect;
