{"name": "IZAC", "config": {"ux": {"gender": "male", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#413F3F", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#545454", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#333333", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#333333", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #333333"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#413F3F", "borderRadius": "0", "borderColor": "#413F3F", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#3C4041", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "#3C4041", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#3C4041", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#3C4041"}}, "3": {"qrcode": {"backgroundColor": "#3C4041"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "800", "fontSize": "72px", "fontColor": "#413F3F", "textAlign": "center"}, "subtitles": {"fontWeight": "600", "fontSize": "14px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#413F3F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "600", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#413F3F", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#413F3F", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "600", "fontSize": "14px", "fontColor": "#413F3F", "textTransform": "none", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Futura", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "none", "color": "#323232", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "500", "textTransform": "none", "color": "#545454", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#a3a3a3", "fontWeight": "600", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#a3a3a3", "borderWidth": "", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#3C4041", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#3C4041", "borderWidth": ""}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "", "borderRadius": "0", "fontSize": "14px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#413F3F", "borderWidth": "1px", "borderRadius": "none", "fontWeight": "400", "fontSize": "14px", "fontColor": "#413F3F", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#413F3F", "borderWidth": "2px", "borderRadius": "none", "fontWeight": "500", "fontSize": "14px", "fontColor": "#413F3F", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#413F3F"}, "unfocused": {"color": "#e6e6e8"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#413F3F", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "600", "fontSize": "13px", "fontColor": "#413F3F", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#A5A8AB", "fontSize": "12px", "fontWeight": "600", "borderRadius": "2px", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "unset"}, "focused": {"backgroundColor": "#413F3F", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "600", "borderRadius": "2px", "borderColor": "#413F3F", "borderWidth": "1px", "textTransform": "unset"}}, "skip": {"fontColor": "#413F3F", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}