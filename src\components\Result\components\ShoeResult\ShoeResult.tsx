import React, { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";
import MeasuredBy from "../../../molecules/MeasuredBy";
import {
  activeStylesContinue,
  disabledStylesContinue,
  resultScreenUI,
} from "../../../../configs";

import { CheckCircle as CheckCircleIcon } from "@mui/icons-material";

import { removeLocalStore } from "../../../../store/localStoreUtils";
import {
  capitalizeFirstLetter,
  getActualDescriptionApparel,
  getActualDescriptionShoes,
  useIsMobile,
} from "../../../../utils";
import {
  findBrandByDomain,
  getQueryParam,
} from "../../../../configs/configLoader";
import {
  postAddToCart,
  postClearMid,
  postClearUid,
  postCloseIframe,
  postSelectSize,
  postSendUid,
} from "../../../../utils/post";
import { logger } from "../../../../utils/logging";
import { useUserContext } from "../../../../store/userContext";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { hoverStylesContinue } from "../../../../configs/stylesLoader";
import InfoStyledIcon from "../../../../assets/icons/InfoStyledIcon";

import "./index.css";

type SelectorSizeType = "unfocused" | "focused" | "unavailable";

interface IPropsShoeResult {
  step: any;
  restart: () => void;
  recommendation: any | null;
  recommendedSize: any;
  selectedVariant: string;
  setSelectedVariant: React.Dispatch<React.SetStateAction<string>>;
  isSizeUnavailable: boolean;
  isChildrenProduct: boolean;
  isShoesProduct: boolean;
}

const ShoeResult: FC<IPropsShoeResult> = ({
  step,
  restart,
  recommendation,
  recommendedSize,
  selectedVariant,
  setSelectedVariant,
  isSizeUnavailable,
  isChildrenProduct,
  isShoesProduct,
}) => {
  logger.log("recommendation", recommendation);
  const { t } = useTranslation("components/results/result");

  const isMobile = useIsMobile();
  const isVerySmallMobile = useMediaQuery({ maxWidth: 400 });

  const userContext = useUserContext();

  const urlParameters = new URLSearchParams(window.location.search);
  const isMockData = urlParameters.get("mock") === "true";
  const isLingerie = urlParameters.get("lingerie") === "true";

  const fit_feedbacks = [t("shoe.size1"), t("shoe.size2"), t("shoe.size3")];

  const brandDefined = findBrandByDomain();

  const [hovered, setHovered] = useState(false);

  const [selectedSize, setSelectedSize] = useState<any | null>(null);

  const [goodSize, setGoodSize] = useState<any | null>(null);
  const [smallerSize, setSmallerSize] = useState<any | null>(null);
  const [biggerSize, setBiggerSize] = useState<any | null>(null);

  useEffect(() => {
    const sizes = ["good", "smaller", "bigger"] as const;

    sizes.forEach((size, index) => {
      const data = isMockData
        ? recommendedSize?.[index]
        : isChildrenProduct || isShoesProduct
        ? recommendedSize?.[size]
        : recommendation?.data?.recommendation?.[size];

      if (data) {
        const setter =
          size === "good"
            ? setGoodSize
            : size === "smaller"
            ? setSmallerSize
            : setBiggerSize;

        setter({
          label: data?.size_label || data?.label,
          reference: data?.reference,
          variant_id: data?.reference || data?.variant_id,
          possible: size === "good" ? 10 : 0,
        });
      }
    });
  }, [
    recommendation,
    recommendedSize,
    isChildrenProduct,
    isShoesProduct,
    isMockData,
  ]);

  useEffect(() => {
    const isSelectSize = isMockData
      ? goodSize
      : goodSize && goodSize?.variant_id;

    if (isSelectSize) {
      setSelectedSize(goodSize);
    }
  }, [goodSize, isMockData]);

  useEffect(() => {
    const ref = selectedSize?.reference;
    if (ref?.toString().trim()) {
      postSelectSize(ref);
    }
  }, [selectedSize]);

  const getSelectorSizeStyles = (type: SelectorSizeType, variant: string) => {
    const sizeStyles = resultScreenUI.sizeSelector[type]!;
    const cornerBorderWidth = "2px";
    const borderColor =
      type === "unavailable"
        ? "#CEA13F"
        : type === "focused"
        ? "#44883F"
        : "#E9E9E9";
    const borderRadius = "4px";

    const selectorSizeStyles = {
      backgroundColor: "#FFF",
      color: "#2E2E2E",
      fontWeight: 600,
      fontSize: "11px",
      textTransform:
        sizeStyles.textTransform as React.CSSProperties["textTransform"],
      borderTopLeftRadius:
        smallerSize?.variant_id && variant === "0" ? borderRadius : 0,
      borderBottomLeftRadius:
        smallerSize?.variant_id && variant === "0" ? borderRadius : 0,
      borderTopRightRadius:
        biggerSize?.variant_id && variant === "2" ? borderRadius : 0,
      borderBottomRightRadius:
        biggerSize?.variant_id && variant === "2" ? borderRadius : 0,
      borderWidthStyle: sizeStyles.borderWidth,
      borderTop: `${sizeStyles.borderWidth} solid ${borderColor}`,
      borderBottom: `${sizeStyles.borderWidth} solid ${borderColor}`,
      borderLeft: `${cornerBorderWidth} solid ${borderColor}`,
      borderRight: `${cornerBorderWidth} solid ${borderColor}`,
      letterSpacing: (sizeStyles as any)?.letterSpacing,
    };

    return selectorSizeStyles;
  };

  const firstSelectorStyles =
    smallerSize?.variant_id === selectedSize?.variant_id
      ? getSelectorSizeStyles("unavailable", "0")
      : getSelectorSizeStyles("unfocused", "0");

  const secondSelectorStyles =
    goodSize?.variant_id === selectedSize?.variant_id
      ? getSelectorSizeStyles("focused", "1")
      : getSelectorSizeStyles("unfocused", "1");

  const thirdSelectorStyles =
    biggerSize?.variant_id === selectedSize?.variant_id
      ? getSelectorSizeStyles("unavailable", "2")
      : getSelectorSizeStyles("unfocused", "2");

  const skipTextTransform = resultScreenUI.restartCTA
    .textTransform as React.CSSProperties["textTransform"];

  const sendIds = () => {
    const uid = localStorage.getItem("uid");
    if (uid && uid !== null && uid !== undefined) {
      postSendUid(uid);
    }
  };

  // const clearMid = () => {
  //   postClearMid();
  // };

  const addToCart = () => {
    logger.log(`addToCart called: ${selectedSize?.variant_id}`);
    const variantId = selectedSize?.variant_id;
    if (variantId) postAddToCart(variantId);
  };

  useEffect(() => {
    sendIds();
  }, []);

  const continueButtonStyles = isSizeUnavailable
    ? disabledStylesContinue
    : activeStylesContinue;

  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...continueButtonStyles,
    ...(isIntersport && hovered ? hoverStylesContinue : {}),
    textTransform:
      continueButtonStyles.textTransform === "capitalize"
        ? "none"
        : continueButtonStyles.textTransform,
    letterSpacing: (continueButtonStyles as any)?.letterSpacing,
    padding: brandDefined?.name === "Victoria Beckham" ? "7px" : "16px",
    height: brandDefined?.name === "Victoria Beckham" ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && !isSizeUnavailable ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && !isSizeUnavailable ? 1 : 0,
    transform:
      hovered && !isSizeUnavailable ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div
      className="result"
      style={{
        marginTop: "20px",
      }}
    >
      <div className="size">
        <h1
          style={{
            color:
              isSizeUnavailable && brandDefined?.name !== "SRP"
                ? (resultScreenUI.unavailable_size_text
                    .fontColor as React.CSSProperties["color"])
                : (resultScreenUI.recommendedSize
                    .fontColor as React.CSSProperties["color"]),
            fontSize: resultScreenUI.recommendedSize
              .fontSize as React.CSSProperties["fontSize"],
            fontWeight: resultScreenUI.recommendedSize
              .fontWeight as React.CSSProperties["fontWeight"],
            textAlign: resultScreenUI.recommendedSize
              .textAlign as React.CSSProperties["textAlign"],
          }}
        >
          {selectedSize?.label || ""}
        </h1>

        {!isShoesProduct && !isLingerie ? (
          <>
            <div className="size-name">
              {goodSize?.variant_id === selectedSize?.variant_id && (
                <>
                  <h2
                    style={{
                      color: "#44883F",
                      fontSize: resultScreenUI.subtitles
                        .fontSize as React.CSSProperties["fontSize"],
                      fontWeight: resultScreenUI.subtitles
                        .fontWeight as React.CSSProperties["fontWeight"],
                      textAlign: resultScreenUI.subtitles
                        .textAlign as React.CSSProperties["textAlign"],
                    }}
                  >
                    {t("size.title")}
                  </h2>
                  <CheckCircleIcon
                    style={{
                      color: "#44883F",
                    }}
                  />
                </>
              )}
            </div>
            <div className="sizes-menu">
              <div
                className={`menu-item ${
                  smallerSize?.variant_id === selectedSize?.variant_id &&
                  "active"
                }`}
                onClick={() => {
                  if (!!smallerSize) {
                    setSelectedVariant("0");
                    setSelectedSize(smallerSize);
                  }
                }}
                style={{
                  ...firstSelectorStyles,
                  cursor: !smallerSize ? "default" : "pointer",
                  textTransform:
                    firstSelectorStyles?.textTransform === "capitalize"
                      ? "none"
                      : firstSelectorStyles?.textTransform,
                }}
              >
                <p>{!smallerSize ? "" : fit_feedbacks[0].toUpperCase()}</p>
              </div>
              <div
                className={`menu-item ${
                  goodSize?.variant_id === selectedSize?.variant_id && "active"
                }`}
                onClick={() => {
                  if (!!goodSize) {
                    setSelectedVariant("1");
                    setSelectedSize(goodSize);
                  }
                }}
                style={{
                  ...secondSelectorStyles,
                  cursor: "pointer",
                  textTransform:
                    secondSelectorStyles?.textTransform === "capitalize"
                      ? "none"
                      : secondSelectorStyles?.textTransform,
                }}
              >
                <p>{fit_feedbacks[1].toUpperCase()}</p>
              </div>
              <div
                className={`menu-item ${
                  biggerSize?.variant_id === selectedSize?.variant_id &&
                  "active"
                }`}
                onClick={() => {
                  if (!!biggerSize) {
                    setSelectedVariant("2");
                    setSelectedSize(biggerSize);
                  }
                }}
                style={{
                  ...thirdSelectorStyles,
                  cursor: !biggerSize ? "default" : "pointer",
                  textTransform:
                    thirdSelectorStyles?.textTransform === "capitalize"
                      ? "none"
                      : thirdSelectorStyles?.textTransform,
                }}
              >
                <p>{!biggerSize ? "" : fit_feedbacks[2].toUpperCase()}</p>
              </div>
            </div>
          </>
        ) : null}
      </div>

      {isLingerie ? (
        <div
          className="result-body"
          style={{
            height: "auto",
            padding: isMobile ? "10px 0" : "20px 0",
            display: "flex",
            flexDirection: "column",
            gap: "5px",
            marginBottom: "30px",
            border: "1px #E9E9E9 solid",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              alignItems: "center",
              padding: "0 20px",
            }}
          >
            <InfoStyledIcon
              style={{
                fontSize: "16px",
              }}
              fill={
                resultScreenUI.Description
                  .fontColor as React.CSSProperties["color"]
              }
            />
            <h3
              style={{
                margin: 0,
                fontSize: isVerySmallMobile ? "11px" : "15px",
                fontWeight: 700,
                color: resultScreenUI.Description
                  .fontColor as React.CSSProperties["color"],
                letterSpacing: (resultScreenUI.Description as any)
                  ?.letterSpacing,
              }}
            >
              {t("size_parameter_title").replace(
                "[S]",
                selectedSize?.label || ""
              )}
            </h3>
          </div>
          <p
            style={{
              margin: 0,
              paddingLeft: "45px",
              paddingRight: isMobile ? "10px" : "50px",
              fontSize: isVerySmallMobile ? "11px" : "15px",
              fontWeight: 500,
              color: resultScreenUI.Description
                .fontColor as React.CSSProperties["color"],
              letterSpacing: (resultScreenUI.Description as any)?.letterSpacing,
            }}
          >
            {t("size_parameter_description").replace(
              "[S]",
              selectedSize?.label || ""
            )}
          </p>
        </div>
      ) : (
        <div
          className="result-body-text"
          style={{
            borderBottom: "none",
            justifyContent: resultScreenUI.Description
              .textAlign as React.CSSProperties["justifyContent"],
            backgroundColor: "transparent",
            paddingBottom: 0,
            margin: brandDefined?.name === "SRP" ? "32px 0" : "15px 0",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "10px",
            }}
          >
            <div
              style={{
                color: "#2E2E2E",
                fontSize:
                  brandDefined?.name === "SRP"
                    ? "14px"
                    : (resultScreenUI.Description
                        .fontSize as React.CSSProperties["fontSize"]),
                fontWeight: resultScreenUI.Description
                  .fontWeight as React.CSSProperties["fontWeight"],
                textAlign: "left",
                lineHeight: "20px",
                display: "flex",
                alignItems: "center",
                minHeight: "60px",
              }}
            >
              <span
                dangerouslySetInnerHTML={{
                  __html: t(
                    isChildrenProduct
                      ? getActualDescriptionApparel(
                          selectedVariant,
                          selectedSize
                        )
                      : getActualDescriptionShoes(
                          selectedSize,
                          smallerSize,
                          goodSize,
                          biggerSize
                        )
                  ).replace("[S]", selectedSize?.label || ""),
                }}
              />
            </div>
          </div>
        </div>
      )}
      {selectedSize && selectedSize?.variant_id ? (
        <button
          type="button"
          className={`continue-button step-bottom-result`}
          onMouseEnter={(e) => {
            setHovered(true);
            if (!isSizeUnavailable) {
              Object.assign(e.currentTarget.style, hoverStylesContinue);
            }
          }}
          onMouseLeave={(e) => {
            setHovered(false);
            if (!isSizeUnavailable) {
              Object.assign(e.currentTarget.style, CTAStyles);
            }
          }}
          disabled={isSizeUnavailable}
          onClick={() => {
            if (selectedSize?.variant_id) {
              addToCart();
              sendIds();
              postCloseIframe();
            }
          }}
          style={CTAStyles}
        >
          <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
            {t("button").replace("[S]", selectedSize?.label || "")}

            {isIntersport && (
              <span style={arrowContainerStyle} aria-hidden>
                <ChevronRight style={iconStyle} />
              </span>
            )}
          </div>
        </button>
      ) : null}
      <span
        className="skip"
        onClick={() => {
          const currentDomain = getQueryParam("domain");
          if (userContext && currentDomain) {
            logger.log("restart called");
            restart();
            localStorage.removeItem("uid");
            localStorage.removeItem("mid");
            postClearUid();
            postClearMid();
            userContext?.createNewUser(currentDomain);
            const urlParams = new URLSearchParams(window.location.search);
            removeLocalStore(`${urlParams.get("pid")}`);
          }
        }}
        style={{
          backgroundColor: "transparent",
          fontWeight: 400,
          fontSize: "13px",
          color: "#2E2E2E",
          textTransform: "capitalize",
          textDecoration: resultScreenUI.restartCTA
            .textDecoration as React.CSSProperties["textDecoration"],
          height: resultScreenUI.restartCTA.borderWidth ? "50px" : "auto",
          alignItems: "center",
          marginTop: "20px",
          letterSpacing: (resultScreenUI.restartCTA as any)?.letterSpacing,
        }}
      >
        {skipTextTransform === "capitalize"
          ? capitalizeFirstLetter(t("skip"))
          : t("skip")}
      </span>
      <MeasuredBy step={step} />
    </div>
  );
};

export default ShoeResult;
