<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56.74 114">
  <defs>
    <style>
      .cls-1 {
        fill: #2e2e2e;
      }

      .cls-1, .cls-2, .cls-3, .cls-4 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #747474;
      }

      .cls-5 {
        stroke-width: .3px;
      }

      .cls-5, .cls-6, .cls-7 {
        stroke: #2e2e2e;
      }

      .cls-5, .cls-6, .cls-7, .cls-8, .cls-9 {
        fill: none;
        stroke-miterlimit: 10;
      }

      .cls-6 {
        stroke-dasharray: 0 0 3 3;
      }

      .cls-6, .cls-8, .cls-9 {
        stroke-linecap: round;
      }

      .cls-3 {
        fill: #fff;
      }

      .cls-4 {
        fill: #231f20;
      }

      .cls-7 {
        stroke-width: .15px;
      }

      .cls-8 {
        stroke-width: .23px;
      }

      .cls-8, .cls-9 {
        stroke: #fff;
      }

      .cls-9 {
        stroke-width: .25px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <g>
        <path class="cls-2" d="M6.51,113.49h8.5s3.33.1,7,0h3c.29-1.5,2.12-9.87,2.47-11.28l1.1-5.25,1.74-.02,1.45,16.78c.32-.24,0,0,2.74-.22,2.5,0,3.27.07,6.5,0h6c.5.5,1.16-12.5,1.47-13.64.32-6.74.51-13.31-.95-21.42-.53-2.15-.62-4.41-2.93-8.59-.18.12-1.48.48-1.84.7-7.81,4.76-16.44,3.94-17.47,3.83-3-.32-4.42-1-9.37-1.35-1.47-.1-3.15-.02-3.91-.03"/>
        <path class="cls-5" d="M35.76,4.64c-.99,3.55-.38,5.98,0,7.06.17.49.32.92.67,1.43,1.09,1.56,2.68,2,5.47,3.02.97.36,3.57,1.74,5.25,2.55"/>
        <path class="cls-5" d="M55.62,32.67c.29-3.09-.47-8.37-3.23-11.65-1.59-1.89-3.07-2.19-3.45-2.26-.66-.11-1.18-.04-1.5,0-3,.41-3.77.51-5.02.71-3.62,0-4.18,1.03-8.16,1.54M55.62,32.67c-.04.45-.18,1.64,0,3.23.17,1.5.53,2.53.67,3.01.79,2.6-.19,4.2-.9,9.62-.62,4.71.02,4.27-.6,6.54-.74,2.71-1.96,4.47-4.05,7.44-1.35,1.92-3.34,4.49-6.07,7.29M55.62,32.67c-.28.89-1.07,2.89-2.03,3.81"/>
        <path class="cls-5" d="M49.14,31.76c0,1.95-.22,3.55-.45,4.73,0,0-.42,2.2-1.28,3.91-.25.51-.57.93-.57.93,0,0-.26.35-.56.65-.51.51-1.53,1.1-2.92,1.5-1.9.58-1.71.97-6.83.4"/>
        <path class="cls-5" d="M47.23,113.98c.41-3.17.91-7.66,1.2-13.08.42-7.87.71-13.14-.37-19.69-.61-3.67-1.33-6.85-4.07-12.91-.12-1.03-.75-4.62-.65-6.48.2-3.88,1.22-6.62,1.72-8.19.89-2.76,2.12-6.87,2-12.59"/>
        <path class="cls-5" d="M9.33,34.17c.38.61.82,1.5.98,2.63.11.81.03,1.43,0,1.65-.33,2.73.2,8.21.34,10,.12,1.59.74,3.4.74,3.4.04.15.15.57.3,1.09.61,2.14,1.25,3.79,1.36,4.07.52,1.37.12,4.68-.45,11.27-.28,3.27-.67,6.69-.67,6.69-.23,1.98-.43,3.54-.67,6.09-.14,1.46-.24,2.66-.3,3.46-.92,4.26-1.76,8.8-2.47,13.6-.81,5.48-1.37,10.68-1.72,15.56"/>
        <path class="cls-5" d="M24.81,113.68c.8-3.69,1.65-7.42,2.55-11.2.45-1.87.89-3.72,1.35-5.56.62.05,1.25.1,1.88.15.28,0,.58-.02.89-.05,1.03-.11,1.91-.35,2.63-.63"/>
        <path class="cls-5" d="M31.79,113.68c-.25-3.15-.5-6.31-.75-9.47-.28-2.39-.55-4.78-.83-7.17"/>
        <path class="cls-5" d="M22.28,5.88c.04.45,0,4.63-.41,6-.1.32-.41,1.27-1.11,2.15-1.32,1.67-3.14,2.04-4.95,2.71-1.52.56-3.61,1.56-5.91,3.45"/>
        <path class="cls-5" d="M.9,34.99c1.26-9.75,2.24-11.59,3.75-12.78,1.12-.88,2.44-1.33,2.92-1.5,2.47-.85,4.64-.64,5.77-.45,4.7-.3,5.57.82,8.98.82M.9,34.99c-.49,3.76-.73,5.64-.75,7.06-.05,3.88.53,4.92.75,10.75.14,3.73-.08,3.76.23,5.18.44,2.07,1.31,3.88,4.12,7.74,1.61,2.2,3.85,5.08,6.78,8.29M.9,34.99c-.23,1.06-.26,3.65,1.52,5.47"/>
        <path class="cls-3" d="M10.36,37.63c-.04.49,1.5,6.69,2.98,6.62l-2.98-6.62Z"/>
        <path class="cls-5" d="M10.36,37.63c-.04.49,1.5,6.69,2.98,6.62,3.86,1.34,8.41-.94,8.41-.94"/>
        <path class="cls-3" d="M37.61.44c.07,1.17-1.42,1.88-1.42,1.88v-.08c0,1.03-.15,2.95-1.18,5.04-1.08,2.16-3.97,4.34-4.23,4.51-.11.11-.65.46-1.5.48-.82.02-1.37-.27-1.5-.38-1.03-.55-3.07-1.83-4.71-4.29-1.64-2.46-1.87-4.51-1.98-5.67h.04s-1.28-.76-1.37-1.93"/>
        <path class="cls-5" d="M37.61.44c.07,1.17-1.42,1.88-1.42,1.88v-.08c0,1.03-.15,2.95-1.18,5.04-1.08,2.16-3.97,4.34-4.23,4.51-.11.11-.65.46-1.5.48-.82.02-1.37-.27-1.5-.38-1.03-.55-3.07-1.83-4.71-4.29-1.64-2.46-1.87-4.51-1.98-5.67h.04s-1.28-.76-1.37-1.93"/>
        <path class="cls-5" d="M28.46,61.03c0-.72.39-1.31.88-1.31s.88.59.88,1.31"/>
        <path class="cls-5" d="M10.94,50.23c-.08,1.33-.18,2.7-.32,4.11-.05.49-.1.98-.15,1.46-.08.75.13,1.5.6,2.09.47.59.92,1.52,1.38,2.12.26.34.52,2.54.78,2.88"/>
        <path class="cls-6" d="M39.01,47.05s9.22-.85,10.72-3.85c3.5-5.5,1-7.5,1.5-10.5"/>
        <path class="cls-6" d="M20.7,46.79s-10.1,1.7-11.6-1.3c-3.5-5.5-1-7.5-1.5-10.5"/>
      </g>
      <g>
        <path class="cls-7" d="M29.3,77.86s1.49,17.65-.72,19.1"/>
        <path class="cls-1" d="M12.11,73.31c17.58,2.03,19.56,3.35,32.67-2.98l1.11,2.77c-8.63,6.25-20.91,6.14-34.24,3.06l.47-2.85Z"/>
        <path class="cls-2" d="M36.55,73.58s1.23,1.63.8,3.57l1.92-.55s.62-2.5-.62-3.6l-2.1.58Z"/>
        <path class="cls-2" d="M23.52,74.5s-1.23,2-.8,4l-1.92-.56s-.62-2.46.62-3.6l2.1.16Z"/>
        <path class="cls-2" d="M14.47,73.31s-.84,2.3-.55,4.55l-1.31-.63s-.44-2.72.41-4l1.45.08Z"/>
        <path class="cls-2" d="M42.97,70.79s.78,1.89.51,4.14l1.22-.63s.46-2.79-.33-4.07l-1.4.56Z"/>
        <path class="cls-8" d="M28.33,74.92l3.69-.14c.23,0,.22.34.24.54l.21,2.29c.02.21.06.59-.18.6l-3.71.28"/>
        <path class="cls-9" d="M31.43,76.52s.85.36,1.23-.17"/>
        <circle class="cls-4" cx="34.04" cy="76.15" r=".19"/>
        <circle class="cls-4" cx="30.08" cy="76.61" r=".19"/>
        <circle class="cls-4" cx="27.35" cy="76.54" r=".19"/>
        <circle class="cls-4" cx="36.36" cy="75.58" r=".19"/>
      </g>
      <path class="cls-1" d="M13.37,77.15c.28,3.74-.77,7.55-2.74,10.72,1.8-3.27,2.83-6.98,2.74-10.72h0Z"/>
      <path class="cls-1" d="M20.81,89.23c-.51,8.05-.98,16.26-3.3,24.01,2.03-7.82,2.72-16.02,3.3-24.01h0Z"/>
      <path class="cls-1" d="M43.15,89.23c.89,8.12,1.02,16.53-1.04,24.48,1.77-8,1.86-16.39,1.04-24.48h0Z"/>
    </g>
  </g>
</svg>