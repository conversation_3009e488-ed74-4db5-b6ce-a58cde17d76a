{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "14px", "fontColor": "#121010", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#121010", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #121010"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#5B5A57", "borderRadius": "4px", "borderColor": "#5B5A57", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#9d5248", "fontColor": "#FFFFFF", "borderRadius": "4px", "borderColor": "#9d5248", "borderWidth": "1px", "fontSize": "14px", "fontWeight": "400", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#121010", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#121010", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#FFFFFF"}}, "3": {"qrcode": {"backgroundColor": "#121010"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#121010", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#121010", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#707070", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#121010", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "14px", "fontColor": "#383838", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "4px", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Optima", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#121010", "fontSize": "20px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#121010", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#5B5A57", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#5B5A57", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#9d5248", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "14px", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#9d5248", "borderWidth": "1px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#383838", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#5B5A57", "borderRadius": "4px", "fontSize": "14px", "fontWeight": "400", "fontColor": "#5B5A57", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#707070", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#121010", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#121010"}, "unfocused": {"color": "#707070"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#121010", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#121010", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "14px", "fontColor": "#121010", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#5B5A57", "fontSize": "14px", "fontWeight": "400", "borderRadius": "50%", "borderColor": "#5B5A57", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#9d5248", "fontColor": "#FFFFFF", "fontSize": "14px", "fontWeight": "400", "borderRadius": "50%", "borderColor": "#9d5248", "borderWidth": "1px", "textTransform": "capitalize"}}, "skip": {"fontColor": "#383838", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}