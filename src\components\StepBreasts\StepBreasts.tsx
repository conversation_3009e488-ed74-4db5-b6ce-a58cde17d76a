import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import MeasuredBy from "../molecules/MeasuredBy";

import { BASE_SIZES } from "../../constants/modal";

import {
  disabledStylesContinue,
  activeStylesContinue,
  femaleBraScreenUI,
  welcomeScreenUI,
  font,
} from "../../configs";
import { capitalizeFirstLetter, useIsMobile } from "../../utils";
import { findBrandByDomain } from "../../configs/configLoader";
import StyledSelector from "./components/StyledSelector";
import BaseSizeSelector from "./components/BaseSizeSelector";
import { handleAnalytics } from "../../utils/tracking";
import ShoesComponentSize from "./components/ShoesComponentSize";
import {
  MEDIUM_PADDING_BRANDS,
  SMALL_PADDING_BRANDS,
} from "../../constants/sizes";
import { hoverStylesContinue } from "../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";

import "./index.css";

interface IPropsStepBreasts {
  step: any;
  value_1: {
    current: string | null;
  };
  value_2: {
    current: string | null;
  };
  nextStep: () => void;
  skipResult: () => void;
  selectedGender: string;
  sizeCountry: string;
  setSizeCountry: React.Dispatch<React.SetStateAction<string>>;
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
  isShoesProduct: boolean;
}

type BodyItemsType = "unfocused" | "focused";

const StepBreasts: FC<IPropsStepBreasts> = ({
  step,
  value_1,
  value_2,
  selectedGender,
  nextStep,
  skipResult,
  sizeCountry,
  setSizeCountry,
  subtitleStyles,
  isShoesProduct,
}) => {
  const isMobile = useIsMobile();
  const [open, setOpen] = useState(false);

  const { t } = useTranslation("components/breasts");

  const [hoveredSize, setHoveredSize] = useState<string | null>(null);
  const [hoveredCup, setHoveredCup] = useState<string | null>(null);

  const [selectedSize, setSelectedSize] = useState<any | undefined>();
  const [selectedCup, setSelectedCup] = useState<any | undefined>();

  const [selectedSizeFR, setSelectedSizeFR] = useState<any | undefined>();
  const [selectedCupFR, setSelectedCupFR] = useState<any | undefined>();

  const [hovered, setHovered] = useState(false);

  const [activeSizeIndex, setActiveSizeIndex] = useState(0);
  const [activeCupIndex, setActiveCupIndex] = useState(0);
  const [isActiveCupSelection, setIsActiveCupSelection] = useState(false);

  const brandDefined = findBrandByDomain();

  const fontFamily = `${font}, sans-serif`;

  const urlParameters = new URLSearchParams(window.location.search);

  const isLingerie = urlParameters.get("lingerie") === "true";

  const isIntersport = brandDefined?.name === "Intersport";
  const isVB = brandDefined?.name === "Victoria Beckham";
  const isSRP = brandDefined?.name === "SRP";
  const isTheodore = brandDefined?.name === "Theodore";
  const isZadig = brandDefined?.name === "Zadig & Voltaire";

  useEffect(() => {
    if (selectedSize) {
      const sizeValue =
        sizeCountry === "FR"
          ? (selectedSize?.size as string | null) || null
          : (selectedSize?.fr as string | null) || null;

      setSelectedSizeFR(sizeValue);

      value_1.current = sizeValue ? sizeValue?.toString() : null;
    }

    if (selectedCup) {
      const cupValue =
        sizeCountry === "FR"
          ? (selectedCup?.size as string | null) || null
          : (selectedCup?.fr as string | null) || null;

      const cupValueFR = selectedCup?.fr as string | null;

      setSelectedCupFR(cupValue);

      value_2.current = cupValueFR ? cupValueFR?.toString() : null;
    }
  }, [sizeCountry, selectedSize, selectedCup, value_1, value_2]);

  const handlePaddingForBrand = (brandName: string) => {
    const smallPadding = "10px";
    const mediumPadding = "13px";

    if (brandName === "Zadig & Voltaire" || brandName === "Lacoste") {
      return "12px";
    }

    if (SMALL_PADDING_BRANDS.includes(brandName)) {
      return smallPadding;
    } else if (MEDIUM_PADDING_BRANDS.includes(brandName)) {
      return mediumPadding;
    } else {
      return smallPadding;
    }
  };

  const getBodyItemsStyles = (
    type: BodyItemsType,
    isHovered = false,
    isSelected = false
  ) => {
    const padding = brandDefined?.name
      ? handlePaddingForBrand(brandDefined?.name)
      : "10px";

    const sizeSelectorItem = femaleBraScreenUI.sizeSelector;

    const isSizeUnderlined =
      (sizeSelectorItem["focused"] as any)?.textDecoration === "underline";

    const borderWidth = isSizeUnderlined
      ? "0 0 1px 0"
      : sizeSelectorItem[type].borderWidth;

    let borderColor = sizeSelectorItem[type].borderColor;
    const color = isSizeUnderlined
      ? "#000000"
      : sizeSelectorItem[type].fontColor;

    if (isIntersport && isHovered && !isSelected) {
      borderColor = "#A1A1A1";
    }

    return {
      padding,
      backgroundColor: isSizeUnderlined
        ? "transparent"
        : sizeSelectorItem[type].backgroundColor,
      borderRadius: sizeSelectorItem[type].borderRadius,
      borderWidth,
      borderStyle: "solid",
      borderColor,
      color,
      fontSize: sizeSelectorItem[type].fontSize,
      fontWeight: sizeSelectorItem[type]
        .fontWeight as React.CSSProperties["fontWeight"],
      textTransform: sizeSelectorItem[type]
        .textTransform as React.CSSProperties["textTransform"],
      height: `calc(${padding} * 3)`,
      fontFamily,
      letterSpacing: (sizeSelectorItem[type] as any)?.letterSpacing,
    };
  };

  const titlesStyles = {
    fontWeight: welcomeScreenUI.input_fields.title.fontWeight,
    textAlign: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["textAlign"],
    textTransform: welcomeScreenUI.input_fields.title
      .textTransform as React.CSSProperties["textTransform"],
    color: welcomeScreenUI.input_fields.title
      .fontColor as React.CSSProperties["color"],
    fontSize: isMobile
      ? "14px"
      : (welcomeScreenUI.input_fields.title
          .fontSize as React.CSSProperties["fontSize"]),
    justifyContent: welcomeScreenUI.input_fields.title
      .textAlign as React.CSSProperties["justifyContent"],
    fontStyle: (welcomeScreenUI.input_fields.title as any)
      .fontStyle as React.CSSProperties["fontStyle"],
    fontFamily: (welcomeScreenUI.input_fields.title as any)
      .fontFamily as React.CSSProperties["fontFamily"],
  };

  const handleNextSelection = (
    listKey: "cup_sizes" | "band_sizes",
    activeIndex: number,
    setActiveIndex: React.Dispatch<React.SetStateAction<number>>,
    setSelectedItem: React.Dispatch<React.SetStateAction<any>>
  ) => {
    const list = BASE_SIZES.find((value: any) => value.name === sizeCountry)?.[
      listKey
    ];
    const nextIndex = list ? (activeIndex + 1) % list.length : -1;
    const selectedItem = list && nextIndex !== -1 ? list[nextIndex] : null;

    setActiveIndex(nextIndex);
    setSelectedItem(selectedItem);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();

    if (e.key === "Tab") {
      e.preventDefault();

      if (isActiveCupSelection) {
        handleNextSelection(
          "cup_sizes",
          activeCupIndex,
          setActiveCupIndex,
          setSelectedCup
        );
      } else {
        handleNextSelection(
          "band_sizes",
          activeSizeIndex,
          setActiveSizeIndex,
          setSelectedSize
        );
      }
    }

    if (e.key === "Enter") {
      if (!selectedCup) {
        setIsActiveCupSelection(true);
      } else if (
        typeof selectedSizeFR === "number" &&
        typeof selectedCupFR === "string"
      ) {
        nextStep();
      }
    }
  };

  const CTAStyles = {
    ...(typeof selectedSizeFR !== "number" || typeof selectedCupFR !== "string"
      ? disabledStylesContinue
      : activeStylesContinue),
    ...(isIntersport && hovered && selectedSize ? hoverStylesContinue : {}),
    position: "relative" as React.CSSProperties["position"],
    marginBottom: !isMobile && isVB ? "60px" : isMobile ? "20px" : "0px",
    marginTop: !isMobile && isVB ? "60px" : isMobile ? "10px" : "30px",
    textTransform:
      activeStylesContinue.textTransform === "capitalize"
        ? "none"
        : (activeStylesContinue.textTransform as React.CSSProperties["textTransform"]),
    padding: isVB ? "7px" : "16px",
    height: isVB ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && selectedSize ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && selectedSize ? 1 : 0,
    transform: hovered && selectedSize ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return isShoesProduct ? (
    <ShoesComponentSize
      step={step}
      value_1={value_1}
      nextStep={nextStep}
      selectedGender={selectedGender}
      sizeCountry={sizeCountry}
      setSizeCountry={setSizeCountry}
      isShoesProduct={isShoesProduct}
    />
  ) : (
    <div className="breasts" onKeyDown={handleKeyDown}>
      {!isVB ? (
        <p
          className="breasts-description"
          style={{
            ...subtitleStyles,
            textTransform:
              subtitleStyles.textTransform === "capitalize"
                ? "none"
                : (subtitleStyles.textTransform as React.CSSProperties["textTransform"]),
            marginBottom: isMobile ? "25px" : "15px",
            marginTop: isMobile && !isSRP ? "0" : "10px",
          }}
        >
          {subtitleStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("description"))
            : t("description")}
        </p>
      ) : null}
      <div
        className="breasts__titles"
        style={{
          display: "flex",
        }}
      >
        <p className="breasts__titles__title" style={titlesStyles}>
          {titlesStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("pays"))
            : t("pays")}
        </p>
        <div onClick={() => setOpen(!open)}>
          {isSRP ? (
            <StyledSelector
              sizeCountry={sizeCountry}
              setSizeCountry={setSizeCountry}
              open={open}
              setOpen={setOpen}
              isShoesProduct={isShoesProduct}
            />
          ) : (
            <BaseSizeSelector
              sizeCountry={sizeCountry}
              setSizeCountry={setSizeCountry}
              open={open}
              setOpen={setOpen}
              isShoesProduct={isShoesProduct}
            />
          )}
        </div>
      </div>
      <div className="breasts__titles">
        <p className="breasts__titles__title" style={titlesStyles}>
          {titlesStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("size"))
            : t("size")}
        </p>
      </div>
      <div className="breasts__body">
        <table className="breasts__body__table">
          <tbody>
            <tr>
              {BASE_SIZES.find(
                (value: any) => value.name === sizeCountry
              )?.band_sizes.map((row, index: number) => (
                <td key={row?.size}>
                  <button
                    className={`breasts__body__table__cell ${
                      selectedSize && selectedSize?.size === row?.size
                        ? "breasts__body__table__cell--selected"
                        : undefined
                    }`}
                    onClick={() => setSelectedSize(row)}
                    onMouseEnter={() => setHoveredSize(index?.toString())}
                    onMouseLeave={() => setHoveredSize(null)}
                    style={Object.assign({
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      width:
                        row?.size?.toString()?.length > 4
                          ? "auto"
                          : `calc(${
                              getBodyItemsStyles(
                                selectedSize?.size === row?.size
                                  ? "focused"
                                  : "unfocused"
                              ).padding
                            } * 3)`,
                      ...getBodyItemsStyles(
                        selectedSize?.size === row?.size
                          ? "focused"
                          : "unfocused",
                        hoveredSize === index.toString(),
                        selectedSize?.size === row?.size
                      ),
                    })}
                  >
                    {row?.size}
                  </button>
                </td>
              ))}
            </tr>
          </tbody>
        </table>
        <div className="breasts__titles">
          <p
            className="breasts__titles__title"
            style={{
              ...titlesStyles,
              position: "relative",
              top: isMobile ? 0 : "10px",
            }}
          >
            {titlesStyles.textTransform === "capitalize"
              ? capitalizeFirstLetter(t("cup"))
              : t("cup")}
          </p>
        </div>
        <table className="breasts__body__table">
          <tbody>
            <tr>
              {BASE_SIZES.find(
                (value: any) => value.name === sizeCountry
              )?.cup_sizes.map((row, index: number) => (
                <td key={row?.size} className="breasts__body__table__test">
                  <button
                    className={`breasts__body__table__cell ${
                      selectedCup && selectedCup?.size === row?.size
                        ? "breasts__body__table__cell--selected"
                        : undefined
                    }`}
                    onClick={() => setSelectedCup(row)}
                    onMouseEnter={() => setHoveredCup(index?.toString())}
                    onMouseLeave={() => setHoveredCup(null)}
                    style={Object.assign({
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      width:
                        row?.size?.toString()?.length > 4
                          ? "auto"
                          : `calc(${
                              getBodyItemsStyles(
                                selectedCup?.size === row?.size
                                  ? "focused"
                                  : "unfocused"
                              ).padding
                            } * 3)`,
                      ...getBodyItemsStyles(
                        selectedCup?.size === row?.size
                          ? "focused"
                          : "unfocused",
                        hoveredCup === index.toString(),
                        selectedCup?.size === row?.size
                      ),
                    })}
                  >
                    {row?.size}
                  </button>
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>
      <button
        type="button"
        disabled={
          typeof selectedSizeFR !== "number" ||
          typeof selectedCupFR !== "string"
        }
        className={`continue-button breasts-continue`}
        onMouseEnter={(e) => {
          setHovered(true);
          if (
            !(
              typeof selectedSizeFR !== "number" ||
              typeof selectedCupFR !== "string"
            )
          ) {
            Object.assign(e.currentTarget.style, hoverStylesContinue);
          }
        }}
        onMouseLeave={(e) => {
          setHovered(false);
          if (
            !(
              typeof selectedSizeFR !== "number" ||
              typeof selectedCupFR !== "string"
            )
          ) {
            Object.assign(e.currentTarget.style, CTAStyles);
          }
        }}
        onClick={() => {
          nextStep();
          // handleAnalytics("action", "continue", {
          //   key: "current_step",
          //   type: "STR",
          //   value: "6_BRA",
          // });
        }}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {isLingerie
            ? t("continue", { ns: "components/intro" })
            : isTheodore
            ? t("continue")
            : activeStylesContinue.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("continue"))
            : t("continue")}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>
      {!isLingerie && !isVB && (
        <span
          className="breasts-skip"
          onClick={() => {
            skipResult();
            handleAnalytics("action", "bra_skip", null);
          }}
          style={{
            color: femaleBraScreenUI.skip.fontColor,
            fontWeight: femaleBraScreenUI.skip.fontWeight,
            fontSize: femaleBraScreenUI.skip.fontSize,
            textDecoration: femaleBraScreenUI.skip.fontStyle,
            textTransform: isZadig ? "uppercase" : "none",
            letterSpacing: (femaleBraScreenUI.skip as any)?.letterSpacing,
          }}
        >
          {t("skip")}
        </span>
      )}
      <MeasuredBy step={step} />
    </div>
  );
};

export default StepBreasts;
