{"name": "Intersport", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "16px", "fontColor": "#242424", "textAlign": "left", "borderColor": "#D3D3D3"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#242424", "textAlign": "left", "borderColor": "#036DED"}, "borderRadius": "4px", "mobileBorderRadius": "4px", "borderColor": "#D3D3D3", "marginBottom": "0px", "padding": "10px 0px 10px 10px", "borderBottom": "1px solid #D3D3D3"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#111E64", "borderRadius": "4px", "borderColor": "#E0E0E0", "borderWidth": "1px", "fontSize": "16px", "fontWeight": "400", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#111e64", "borderRadius": "4px", "borderColor": "#111e64", "borderWidth": "1.4px", "fontSize": "16px", "fontWeight": "400", "textTransform": "capitalize", "outlineColor": "#111E64", "outlineWidth": "2px", "outlineOffset": "1px"}}, "units": {"fontWeight": "400", "fontSize": "16px", "top": "8px", "right": "", "activeColor": "#000000", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#000000"}, "deviceSelect": {"default": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "4px", "borderColor": "#E0E0E0", "borderWidth": "1px", "fontSize": "16px", "fontWeight": "400", "textTransform": ""}, "hover": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "4px", "borderColor": "#A1A1A1", "borderWidth": "2px", "fontSize": "16px", "fontWeight": "400", "textTransform": ""}}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "16px", "fontColor": "#000000", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#707070", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "16px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "16px", "fontColor": "#383838", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Open Sans", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#000000", "fontSize": "24px", "letterSpacing": "0.5px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#000000", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#666666", "fontWeight": "600", "fontSize": "16px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "36px", "borderColor": "#666666", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#111e64", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "16px", "textTransform": "capitalize", "borderRadius": "36px", "borderColor": "#000000", "borderWidth": "1px"}, "hover": {"backgroundColor": "#111e64", "fontWeight": "600", "fontColor": "#FFFFFF", "fontSize": "16px", "textTransform": "capitalize", "borderRadius": "36px", "borderColor": "#000000", "borderWidth": "1px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#383838", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#5B5A57", "borderRadius": "36px", "fontSize": "16px", "fontWeight": "400", "fontColor": "#5B5A57", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#707070", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#111e64", "borderWidth": "2px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#111e64"}, "unfocused": {"color": "#414141"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#D3D3D3", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#D3D3D3", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "16px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#111E64", "fontSize": "16px", "fontWeight": "400", "borderRadius": "4px", "borderColor": "#E0E0E0", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#111e64", "fontSize": "16px", "fontWeight": "400", "borderRadius": "4px", "borderColor": "#111e64", "borderWidth": "1.4px", "textTransform": "capitalize", "outlineColor": "#111E64", "outlineWidth": "2px", "outlineOffset": "1px"}}, "skip": {"fontColor": "#383838", "fontWeight": "400", "fontStyle": "", "fontSize": "16px"}}}}}