{"name": "<PERSON>", "config": {"ux": {"gender": "male", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "16px", "fontColor": "#3D3C3B", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "12px", "fontColor": "#A3A1A7", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#3D3C3B", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #3D3C3B"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#3D3C3B", "borderRadius": "0", "borderColor": "#3D3C3B", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "lowercase"}, "focused": {"backgroundColor": "#3D3C3B", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "fontSize": "12px", "fontWeight": "400", "textTransform": "lowercase"}}, "units": {"fontWeight": "400", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#3D3C3B", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#3D3C3B", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#3D3C3B"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#3D3C3B", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#3D3C3B", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textTransform": "lowercase", "borderRadius": "0", "borderColor": "#D9D9D9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "lowercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "lowercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "14px", "fontColor": "#3D3C3B", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#3D3C3B", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textTransform": "uppercase", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Inter", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "uppercase", "color": "#3D3C3B", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#3D3C3B", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#d9d9d9", "fontWeight": "400", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#3D3C3B", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#3D3C3B", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "", "borderColor": "", "borderRadius": "", "fontSize": "", "fontWeight": "", "fontColor": "", "textTransform": "", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#3D3C3B", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#3D3C3B", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#3D3C3B"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#3D3C3B", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#3D3C3B", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#3D3C3B", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontColor": "#000000", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "", "fontColor": "#3D3C3B", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "textTransform": "uppercase", "textDecoration": "underline"}}, "skip": {"fontColor": "#3D3C3B", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}