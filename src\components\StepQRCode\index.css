.main-qr-container {
  min-height: 72.3vh;
}

.main-qr-container .text-block {
  margin-top: 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  gap: 20px;
}

.main-qr-container .text-block .title-scan {
  display: flex;
}

.main-qr-container .text-block-mobile {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.continue-button {
  background: #2E2E2E;
  padding: 16px;
  margin-top: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  color: #fff;
  width: 100%;
  border-radius: 5px;
  border: 1ps solid #2E2E2E;
}