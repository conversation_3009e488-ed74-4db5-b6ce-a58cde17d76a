name: "<PERSON><PERSON><PERSON> Slack si PR >15 commits"

on:
  pull_request:
    types: [opened, reopened, synchronize]

jobs:
  slack_alert:
    runs-on: ubuntu-latest
    steps:
      - name: <PERSON><PERSON><PERSON><PERSON><PERSON> le nombre de commits de la PR
        id: count_commits
        run: |
          echo "::set-output name=count::${{ github.event.pull_request.commits }}"

      - name: <PERSON><PERSON><PERSON> sur Slack si >15 commits
        if: ${{ steps.count_commits.outputs.count && steps.count_commits.outputs.count > 15 }}
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": ":warning: PR *#${{ github.event.pull_request.number }}* now has *${{ steps.count_commits.outputs.count }} commits*! Please merge asap. <${{ github.event.pull_request.html_url }}|See PR>"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_FRONT }}
