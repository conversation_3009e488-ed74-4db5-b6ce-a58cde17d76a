{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#131d26", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "12px", "fontColor": "#131d26", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#131d26", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#131d26", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #131d26"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#131d26", "inactiveColor": "#888888"}}, "2": {"routeCTA": {"borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#FFFFFF"}}, "3": {"qrcode": {"backgroundColor": "#131d26"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#131d26", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "12px", "fontColor": "#131d26", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#131d26", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#707070", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "12px", "fontColor": "#131d26", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "12px", "fontColor": "#131d26", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#383838", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Instrument Sans", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "capitalize", "color": "#131d26", "fontSize": "20px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#131d26", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#000000", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#383838", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0px", "fontSize": "12px", "fontWeight": "400", "fontColor": "#000000", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#131d26"}, "unfocused": {"color": "#707070"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "1px", "borderRadius": "0px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0px", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "capitalize", "textDecoration": "underline"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "", "fontSize": "12px"}}}}}