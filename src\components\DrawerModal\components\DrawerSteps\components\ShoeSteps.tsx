import { MODAL_STEPS } from "../../../../../constants/modal";
import { useUserContext } from "../../../../../store/userContext";
import StepShoeQuestions from "../../StepShoeQuestions";

export interface ShoeStepsProps {
  step: any;
  shoeQuestion: string | null;
  setShoeQuestion: React.Dispatch<React.SetStateAction<string | null>>;
  setStep: (step: any) => void;
  subtitlesStyles?: any;
  mock: boolean;
  domain: string | null;
  visionInit: (uid: string) => Promise<any>;
  sendQuestionAnswers?: () => void | Promise<void>;
}

const noop = () => {};

const ShoeSteps: React.FC<ShoeStepsProps> = ({
  step,
  shoeQuestion,
  setShoeQuestion,
  setStep,
  subtitlesStyles,
  mock,
  domain,
  visionInit,
  sendQuestionAnswers = noop,
}) => {
  const userContext = useUserContext();

  return (
    <>
      {step?.number === MODAL_STEPS.SHOE_QUESTIONS.number && (
        <StepShoeQuestions
          shoeQuestion={shoeQuestion}
          setShoeQuestion={setShoeQuestion}
          nextStep={async () => {
            if (mock) {
              sendQuestionAnswers();
              setStep(MODAL_STEPS.RESULT);
              return;
            }

            if (userContext) {
              await userContext.createNewMeasure("question");

              if (!userContext?.uid && domain) {
                userContext.createNewUser(domain);
              }

              if (userContext?.uid) {
                const result = await visionInit(userContext?.uid);
                if (result) {
                  sendQuestionAnswers();
                }
              }
            }
          }}
          subtitlesStyles={subtitlesStyles}
        />
      )}
    </>
  );
};

export default ShoeSteps;
