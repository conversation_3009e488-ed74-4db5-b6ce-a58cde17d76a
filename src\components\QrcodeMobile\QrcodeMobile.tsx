import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { activeStylesContinue, font } from "../../configs";
import { GENDERS } from "../../constants/modal";

import { Help as HelpIcon } from "@mui/icons-material";
import { Popper, Typography, Paper, ClickAwayListener } from "@mui/material";
import { capitalizeFirstLetter } from "../../utils";
import { handleAnalytics } from "../../utils/tracking";
import { hoverStylesContinue } from "../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { findBrandByDomain } from "../../configs/configLoader";

import "./index.css";
interface IPropsQrcodeMobile {
  isShoesProduct?: boolean;
  loading?: boolean;
  url?: string;
  gender: string | undefined;
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
}

const QrcodeMobile: FC<IPropsQrcodeMobile> = ({
  isShoesProduct,
  loading,
  url,
  gender,
  subtitleStyles,
}) => {
  const { t } = useTranslation("components/qrcode/mobile");
  const scanType = isShoesProduct ? "shoe_scan" : "scan";

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [hovered, setHovered] = useState(false);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const open = Boolean(anchorEl);
  const id = open ? "info-popper" : undefined;

  const openScan = () => {
    if (!loading) {
      handleAnalytics("action", "scan_clicked", null);

      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  const brandDefined = findBrandByDomain();
  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...activeStylesContinue,
    ...(isIntersport && hovered ? hoverStylesContinue : {}),
    textTransform:
      activeStylesContinue.textTransform === "capitalize"
        ? "none"
        : activeStylesContinue.textTransform,
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered ? 1 : 0,
    transform: hovered ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div className="mobile-qr-container">
      <video muted className="qrcode-video" playsInline autoPlay loop>
        <source
          src={
            isShoesProduct
              ? "https://kleep-prod-assets-public.s3.eu-west-1.amazonaws.com/videos/Animation_Footwear.mp4"
              : gender === GENDERS.M
              ? require("../../assets/qrcode/demo-male.mp4")
              : require("../../assets/qrcode/demo-female.mp4")
          }
          type="video/mp4"
        />
      </video>
      <div className="help-info">
        <HelpIcon />
        <span
          className="link"
          aria-describedby={id}
          onClick={handleClick}
          style={{
            color: `${subtitleStyles.color}`,
            fontFamily: `${font}, sans-serif`,
          }}
        >
          {t("info")}
        </span>
        <Popper id={id} open={open} anchorEl={anchorEl} placement="top">
          <ClickAwayListener onClickAway={() => setAnchorEl(null)}>
            <Paper
              className="popper-container"
              style={{
                border: `1px solid ${subtitleStyles.color}`,
                fontFamily: `${font}, sans-serif`,
              }}
            >
              <Typography
                style={{
                  color: `${subtitleStyles.color}`,
                  fontFamily: `${font}, sans-serif`,
                }}
              >
                {t("popupText")}
              </Typography>
              <div
                className="popper-triangle"
                style={{
                  borderTop: `10px solid ${subtitleStyles.color}`,
                }}
              />
            </Paper>
          </ClickAwayListener>
        </Popper>
      </div>
      <button
        type="button"
        className={`continue-button`}
        onMouseEnter={(e) => {
          setHovered(true);
          Object.assign(e.currentTarget.style, hoverStylesContinue);
        }}
        onMouseLeave={(e) => {
          setHovered(false);
          Object.assign(e.currentTarget.style, CTAStyles);
        }}
        onClick={openScan}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {activeStylesContinue.textTransform === "capitalize"
            ? capitalizeFirstLetter(t(scanType))
            : t(scanType)}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>
    </div>
  );
};

export default QrcodeMobile;
