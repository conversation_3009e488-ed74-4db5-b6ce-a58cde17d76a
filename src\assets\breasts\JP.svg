<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="white-gradient" x1="50%" y1="0%" x2="50%" y2="100%">
            <stop stop-color="#FFFFFF" offset="0%" />
            <stop stop-color="#F0F0F0" offset="100%" />
        </linearGradient>
        <linearGradient id="red-gradient" x1="50%" y1="0%" x2="50%" y2="100%">
            <stop stop-color="#FF4B55" offset="0%" />
            <stop stop-color="#D32E2E" offset="100%" />
        </linearGradient>
    </defs>
    <rect width="21" height="15" fill="url(#white-gradient)" />
    <circle cx="10.5" cy="7.5" r="4.5" fill="url(#red-gradient)" />
</svg>
