import { FC, useState } from "react";
import Stepper from "../molecules/Stepper/Stepper";
import BellyChoice from "../BellyChoice";
import MeasuredBy from "../molecules/MeasuredBy";

import { GENDERS, LANGUAGE_RULES, MODAL_STEPS } from "../../constants/modal";

import { useTranslation } from "react-i18next";

import torse_1_female from "../../assets/questionnaire/torse/torse-1-female.svg";
import torse_2_female from "../../assets/questionnaire/torse/torse-2-female.svg";
import torse_3_female from "../../assets/questionnaire/torse/torse-3-female.svg";

import torse_1_male from "../../assets/questionnaire/torse/torse-1-male.svg";
import torse_2_male from "../../assets/questionnaire/torse/torse-2-male.svg";
import torse_3_male from "../../assets/questionnaire/torse/torse-3-male.svg";

import victoria_selected_1 from "../../assets/questionnaire/torse/victoria-assets/selected/VB_SIZE&FIT_ILLUSTRATIONS-15.svg";
import victoria_selected_2 from "../../assets/questionnaire/torse/victoria-assets/selected/VB_SIZE&FIT_ILLUSTRATIONS-16.svg";
import victoria_selected_3 from "../../assets/questionnaire/torse/victoria-assets/selected/VB_SIZE&FIT_ILLUSTRATIONS-17.svg";

import victoria_unselected_1 from "../../assets/questionnaire/torse/victoria-assets/unselected/VB_SIZE&FIT_ILLUSTRATIONS-21.svg";
import victoria_unselected_2 from "../../assets/questionnaire/torse/victoria-assets/unselected/VB_SIZE&FIT_ILLUSTRATIONS-22.svg";
import victoria_unselected_3 from "../../assets/questionnaire/torse/victoria-assets/unselected/VB_SIZE&FIT_ILLUSTRATIONS-23.svg";

import modern_sillhouette_1 from "../../assets/questionnaire/torse/modern/F-HANCHE_1.svg";
import modern_sillhouette_2 from "../../assets/questionnaire/torse/modern/F-HANCHE_2.svg";
import modern_sillhouette_3 from "../../assets/questionnaire/torse/modern/F-HANCHE_3.svg";

import ogier_modern_1_female from "../../assets/questionnaire/torse/ogier/female/F-HANCHE_1.svg";
import ogier_modern_2_female from "../../assets/questionnaire/torse/ogier/female/F-HANCHE_2.svg";
import ogier_modern_3_female from "../../assets/questionnaire/torse/ogier/female/F-HANCHE_3.svg";

import ogier_modern_1_male from "../../assets/questionnaire/torse/ogier/male/M-TORSE_1.svg";
import ogier_modern_2_male from "../../assets/questionnaire/torse/ogier/male/M-TORSE_2.svg";
import ogier_modern_3_male from "../../assets/questionnaire/torse/ogier/male/M-TORSE_3.svg";

import PhotoCameraIcon from "../../assets/icons/PhotoCameraIcon";

import {
  titleStyles,
  disabledStylesContinue,
  activeStylesContinue,
  scanCTAStyle,
  welcomeScreenUI,
} from "../../configs";
import {
  capitalizeFirstLetter,
  getLangBase,
  handleAdditionalBrands,
  isEmptyCTAStyle,
  useIsMobile,
  useKeyNavigation,
} from "../../utils";

import { findBrandByDomain } from "../../configs/configLoader";
import { useUserContext } from "../../store/userContext";
import { hoverStylesContinue } from "../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";

import "./index.css";

interface IPropsStepTorse {
  step: any;
  gender: string;
  nextStep: () => void;
  setStep: (
    value: React.SetStateAction<{
      number: number;
    } | null>
  ) => void;
  setPreviousStep: React.Dispatch<
    React.SetStateAction<{
      number: number;
    } | null>
  >;
  value: {
    current: number;
  };
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
}

const StepTorse: FC<IPropsStepTorse> = ({
  step,
  gender,
  nextStep,
  setStep,
  setPreviousStep,
  value,
  subtitleStyles,
}) => {
  const isMobile = useIsMobile();
  const [selected, setSelected] = useState<number | undefined>(value.current);

  const { t } = useTranslation("components/torso");

  const userContext = useUserContext() || null;

  const brandDefined = findBrandByDomain();

  const [hovered, setHovered] = useState(false);

  const urlParameters = new URLSearchParams(window.location.search);

  const lang = urlParameters.get("lang");
  const market = urlParameters.get("market");

  const isIntersport = brandDefined?.name === "Intersport";
  const isVB = brandDefined?.name === "Victoria Beckham";
  const isSRP = brandDefined?.name === "SRP";
  const isHast = brandDefined?.name === "Hast";
  const isKenzo = brandDefined?.name === "Kenzo";
  const isLacoste = brandDefined?.name === "Lacoste";

  const langBase = market
    ? getLangBase(market)
    : lang
    ? getLangBase(lang)
    : null;

  const langRules =
    LANGUAGE_RULES[langBase as keyof typeof LANGUAGE_RULES] ||
    LANGUAGE_RULES.default;

  const scanTextTransform =
    scanCTAStyle.textTransform as React.CSSProperties["textTransform"];
  const continueButtonStyles = !selected
    ? disabledStylesContinue
    : activeStylesContinue;

  // useEffect(() => {
  //   if (selected) {
  //     handleAnalytics(
  //       "action",
  //       gender === GENDERS.M ? "chest_selected" : "hip_selected",
  //       {
  //         key: "shape",
  //         type: "INT",
  //         value: selected,
  //       }
  //     );
  //   }

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [selected, gender]);

  // useEffect(() => {
  //   if (gender === GENDERS.M) {
  //     handleAnalytics("step", "chest", {
  //       key: "step",
  //       type: "STR",
  //       value: "5_CHEST",
  //     });
  //   }

  //   if (gender === GENDERS.F) {
  //     handleAnalytics("step", "hip", {
  //       key: "step",
  //       type: "STR",
  //       value: "5_HIP",
  //     });
  //   }

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [gender]);

  const handleSilhouettesMap = (isSelected: boolean) => {
    const silhouettesMap: Record<string, (string | null)[]> = {
      "Victoria Beckham": [
        null,
        isSelected ? victoria_selected_1 : victoria_unselected_1,
        isSelected ? victoria_selected_2 : victoria_unselected_2,
        isSelected ? victoria_selected_3 : victoria_unselected_3,
      ],
      Ogier: [
        null,
        ogier_modern_1_female,
        ogier_modern_2_female,
        ogier_modern_3_female,
      ],
      Modern: [
        null,
        modern_sillhouette_1,
        modern_sillhouette_2,
        modern_sillhouette_3,
      ],
    };

    return silhouettesMap;
  };

  const handleFemaleImage = (item: number) => {
    const isSelected = selected === item;
    const isAdditionalBrands = handleAdditionalBrands(brandDefined?.name);

    if (isAdditionalBrands) {
      const relatedSilhouettes =
        handleSilhouettesMap(isSelected)[brandDefined?.name || "Modern"] ||
        handleSilhouettesMap(isSelected)["Modern"];
      return relatedSilhouettes[item] ?? relatedSilhouettes[1];
    }

    const defaultImages = [
      null,
      torse_1_female,
      torse_2_female,
      torse_3_female,
    ];
    return defaultImages[item] ?? defaultImages[1];
  };

  const maleSilhouettesMap: Record<string, (string | null)[]> = {
    Ogier: [
      null,
      ogier_modern_1_male,
      ogier_modern_2_male,
      ogier_modern_3_male,
    ],
    Default: [null, torse_1_male, torse_2_male, torse_3_male],
  };

  const handleMaleImage = (item: number) => {
    if (gender === GENDERS.F) return handleFemaleImage(item);

    const relatedSilhouettes =
      maleSilhouettesMap[brandDefined?.name || "Default"] ||
      maleSilhouettesMap["Default"];

    return relatedSilhouettes[item] ?? relatedSilhouettes[1];
  };

  useKeyNavigation({
    selected,
    setSelected,
    valueRef: value,
    onEnter: nextStep,
  });

  const CTAStyles: React.CSSProperties = {
    ...continueButtonStyles,
    ...(isIntersport && hovered && selected ? hoverStylesContinue : {}),
    marginTop: "20px",
    textTransform:
      continueButtonStyles.textTransform === "capitalize"
        ? "none"
        : continueButtonStyles.textTransform,
    padding: isVB ? "7px" : "16px",
    height: isVB ? "30px" : "auto",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && selected ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && selected ? 1 : 0,
    transform: hovered && selected ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <>
      <div
        className="belly-description"
        style={{
          ...subtitleStyles,
          textAlign: titleStyles.textAlign as React.CSSProperties["textAlign"],
          justifyContent:
            titleStyles.textAlign as React.CSSProperties["justifyContent"],
          textTransform:
            subtitleStyles.textTransform === "capitalize"
              ? "none"
              : (subtitleStyles.textTransform as React.CSSProperties["textTransform"]),
          fontStyle: (welcomeScreenUI.input_fields.title as any)
            .fontStyle as React.CSSProperties["fontStyle"],
        }}
      >
        <p
          style={{
            margin: 0,
          }}
        >
          {isVB
            ? "Stomach Shape"
            : subtitleStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("description"))
            : t("description")}
        </p>
      </div>
      <div
        className="belly"
        style={{
          marginTop: isMobile ? "20px" : "40px",
          gap: isMobile ? "10px" : "30px",
        }}
      >
        <div
          className="body"
          style={{
            gap: isMobile && isVB ? "0px" : "8px",
          }}
        >
          <BellyChoice
            image={handleMaleImage(1)}
            onClick={() => {
              value.current = 1;
              setSelected(1);
            }}
            text={
              gender === GENDERS.M ? t("size.male.one") : t("size.female.one")
            }
            isSelected={selected === 1}
            gender={gender}
            type="torso"
          />
          <BellyChoice
            image={handleMaleImage(2)}
            onClick={() => {
              value.current = 2;
              setSelected(2);
            }}
            text={
              gender === GENDERS.M ? t("size.male.two") : t("size.female.two")
            }
            isSelected={selected === 2}
            gender={gender}
            type="torso"
          />
          <BellyChoice
            image={handleMaleImage(3)}
            onClick={() => {
              value.current = 3;
              setSelected(3);
            }}
            text={
              gender === GENDERS.M
                ? t("size.male.three")
                : t("size.female.three")
            }
            isSelected={selected === 3}
            gender={gender}
            type="torso"
          />
        </div>
        <div className="controls">
          {!isSRP && !isVB && (
            <Stepper
              stepsNum={isHast ? 4 : gender === GENDERS.M ? 3 : 2}
              step={2}
              key={1}
            />
          )}
          <button
            type="button"
            disabled={!selected}
            className={`continue-button step-belly-button`}
            onMouseEnter={(e) => {
              setHovered(true);
              if (selected) {
                Object.assign(e.currentTarget.style, hoverStylesContinue);
              }
            }}
            onMouseLeave={(e) => {
              setHovered(false);
              if (selected) {
                Object.assign(e.currentTarget.style, continueButtonStyles);
              }
            }}
            onClick={() => nextStep()}
            style={CTAStyles}
          >
            <div
              style={{ display: "inline-flex", alignItems: "center", gap: 0 }}
            >
              {continueButtonStyles.textTransform === "capitalize"
                ? capitalizeFirstLetter(
                    t("continue", { ns: "components/intro" })
                  )
                : t("continue", { ns: "components/intro" })}

              {isIntersport && (
                <span style={arrowContainerStyle} aria-hidden>
                  <ChevronRight style={iconStyle} />
                </span>
              )}
            </div>
          </button>

          {!!scanCTAStyle &&
          !isEmptyCTAStyle(scanCTAStyle) &&
          !(isKenzo && langRules?.unit === "imperial") ? (
            <div
              style={{
                display: "flex",
                border: `1px solid ${scanCTAStyle.borderColor}`,
                borderWidth: isLacoste
                  ? "1px 0 1px 0"
                  : scanCTAStyle.borderWidth,
                borderRadius: scanCTAStyle.borderRadius,
                borderColor: scanCTAStyle.borderColor,
                fontSize: scanCTAStyle.fontSize,
                color: scanCTAStyle.fontColor,
                fontWeight: scanCTAStyle.fontWeight,
                textTransform:
                  scanTextTransform === "capitalize"
                    ? "none"
                    : scanTextTransform,
                marginTop: isMobile ? "10px" : "0px",
                width: "calc(100% - 3px)",
                height: "auto",
                maxHeight: "48px",
                backgroundColor: "#FFFFFF",
                cursor: "pointer",
                padding: isMobile ? "11px 0" : "12px 0",
                letterSpacing: (scanCTAStyle as any)?.letterSpacing,
              }}
              className="scan-CTA-button"
              onClick={async () => {
                if (userContext) {
                  const resultNewMeasure = await userContext.createNewMeasure(
                    "scan"
                  );
                  if (resultNewMeasure) {
                    setStep(MODAL_STEPS.QR_CODE);
                    setPreviousStep(MODAL_STEPS.TORSO);
                  }
                }
              }}
            >
              <div
                style={{
                  display: "flex",
                  gap: "10px",
                  textDecoration:
                    scanCTAStyle.fontStyle === "underline"
                      ? "none"
                      : scanCTAStyle.fontStyle,
                  alignItems: "center",
                  width: "100%",
                  justifyContent: "center",
                  padding: !isMobile ? 0 : "0 24px",
                }}
              >
                <div
                  style={{
                    minWidth: "30px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <PhotoCameraIcon fill={scanCTAStyle.fontColor} />
                </div>
                <span
                  style={{
                    fontSize: isMobile
                      ? `calc(${scanCTAStyle.fontSize} - 1px`
                      : scanCTAStyle.fontSize,
                    letterSpacing: (scanCTAStyle as any)?.letterSpacing,
                    wordBreak: "break-word",
                  }}
                  className={`${
                    scanCTAStyle.fontStyle === "underline"
                      ? "scan-text underline"
                      : ""
                  }`}
                >
                  {scanTextTransform === "capitalize"
                    ? capitalizeFirstLetter(
                        t("scan", { ns: "components/belly" })
                      )
                    : t("scan", { ns: "components/belly" })}
                </span>
              </div>
            </div>
          ) : null}
        </div>
        <MeasuredBy step={step} />
      </div>
    </>
  );
};

export default StepTorse;
