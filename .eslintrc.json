{
  "extends": ["react-app", "react-app/jest"],
  "overrides": [
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "excludedFiles": ["**/logging.ts"],
      "rules": {
        // "no-restricted-syntax": [
        //   "error",
        //   {
        //     "selector": "CallExpression[callee.object.name='console'][callee.property.name=/^(log|warn|error)$/]",
        //     "message": "Use the method logger() instead of console.log/warn/error"
        //   }
        // ]
      }
    },
    // rules for tests (Playwright)
    {
      "files": ["tests/e2e/**/*.ts"],
      "rules": {
        "testing-library/prefer-screen-queries": "off",
        "testing-library/no-render-in-setup": "off",
        "testing-library/no-debug": "off"
      }
    }
  ]
}
