{"components/results/result": {"mark1": "<PERSON><PERSON> eng", "mark2": "Perfekte Passform", "size": {"title": "Ideale Größe", "size1": "<PERSON><PERSON><PERSON><PERSON>", "size2": "Ideal", "size3": "<PERSON><PERSON><PERSON>", "variations": {"size1": "Regular", "size2": "Oversize", "size3": "Perfekte Passform", "size4": "<PERSON><PERSON> eng", "size5": "Le<PERSON>t eng", "size6": "<PERSON><PERSON> eng", "size7": "<PERSON><PERSON><PERSON><PERSON><PERSON> weit", "size8": "<PERSON><PERSON><PERSON> locker", "size9": "<PERSON><PERSON>", "size10": "<PERSON>hr weit"}}, "description": {"normal": {"ideal": "Aufgrund Ihrer Körperform und des Feedbacks unserer Kunden glauben wir, dass Ihnen die Größe [S] wie angegossen passt! ", "not_possible_up": "Ihre Idealgröße konnte nicht ermittelt werden: basierend auf Ihrer Körperform wird die Größe [S] zu groß sein.", "not_possible_down": "Ihre Idealgröße konnte nicht ermittelt werden: basierend auf Ihrer Körperform wird die Größe [S] zu klein sein.", "not_possible_secondary": "Wir empfehlen <PERSON>hnen, eine Boutique zu besuchen, um eine maßgeschneiderte Begleitung zu erhalten."}, "size_up": {"ideal": "<PERSON><PERSON>r einen weiteren Sitz wählen Sie Größe [S].", "not_possible": "Basierend auf Ihrer Körperform wird die Größe [S] zu groß sein. Wir empfeh<PERSON>hnen, eine kleinere Größe zu wählen."}, "size_down": {"ideal": "<PERSON>ür eine schlankere Passform wählen Sie Größe [S].", "not_possible": "Basierend auf Ihrer Körperform wird die Größe [S] zu klein sein. Wir empfehlen <PERSON>hnen, eine größere Größe zu wählen."}}, "result": {"title": "Regular Fit", "description": "Perfekte Balance zwischen Stil und Komfort für einen sportlichen und lässigen Look."}, "unavailable": {"description": "Le<PERSON> ist die ausgewählte Größe nicht auf Lager.", "title": "Intenta de nuevo :"}, "button": "Grösse [S] Zum Warenkorb Hinzufügen", "skip": "<PERSON><PERSON><PERSON> beginnen"}}