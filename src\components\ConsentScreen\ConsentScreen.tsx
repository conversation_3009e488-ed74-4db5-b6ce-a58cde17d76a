import { FC, useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";

import {
  activeStylesContinue,
  disabledStylesContinue,
  titleStyles,
} from "../../configs";
import { capitalizeFirstLetter } from "../../utils";
import { handleAnalytics } from "../../utils/tracking";
import CustomCheckbox from "./components/CustomCheckbox";
import { hoverStylesContinue } from "../../configs/stylesLoader";
import { ArrowForward as ChevronRight } from "@mui/icons-material";
import { findBrandByDomain } from "../../configs/configLoader";

import "./index.css";

interface IPropsConsentScreen {
  nextStep: () => void;
  clearMid: () => void;
}

const ConsentScreen: FC<IPropsConsentScreen> = ({ nextStep, clearMid }) => {
  const { t } = useTranslation("components/consentement");

  const [checked, setChecked] = useState(false);
  const consentRef = useRef<HTMLDivElement>(null);

  const [hovered, setHovered] = useState(false);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
    handleAnalytics("action", "consent_click", {
      key: "value",
      value: event.target.checked,
    });
  };

  const description = t("description");
  const paragraphs = description.split("\n\n");

  const makeLinksClickable = (text: string) => {
    const urlPattern = /(https?:\/\/[^\s]+)/g;
    return text.split(urlPattern).map((part: string, index: number) => {
      if (part.match(urlPattern)) {
        return (
          <a
            key={index}
            href={part}
            target="_blank"
            rel="noopener noreferrer"
            className="link-truncate"
            title={part}
          >
            {part}
          </a>
        );
      }
      return part;
    });
  };

  useEffect(() => {
    if (consentRef.current) {
      consentRef.current?.focus();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const continueButtonStyles = !checked
    ? disabledStylesContinue
    : activeStylesContinue;

  const handleKeyDown = (e: any) => {
    e.stopPropagation();

    if (e.key === "Enter") {
      if (!checked) setChecked(true);
      if (checked) nextStep();
    }
  };

  const brandDefined = findBrandByDomain();

  const isIntersport = brandDefined?.name === "Intersport";

  const CTAStyles = {
    ...continueButtonStyles,
    ...(isIntersport && hovered && checked ? hoverStylesContinue : {}),
    textTransform:
      continueButtonStyles.textTransform === "capitalize"
        ? "none"
        : continueButtonStyles.textTransform,
    marginTop: "20px",
  };

  const arrowContainerStyle: React.CSSProperties = {
    display: "inline-block",
    width: hovered && checked ? 16 : 0,
    overflow: "hidden",
    verticalAlign: "middle",
    transition: "width 160ms ease",
  };

  const iconStyle: React.CSSProperties = {
    width: 16,
    height: 16,
    opacity: hovered && checked ? 1 : 0,
    transform: hovered && checked ? "translateX(0)" : "translateX(-6px)",
    transition: "opacity 160ms ease, transform 160ms ease",
    display: "inline-block",
    verticalAlign: "middle",
    pointerEvents: "none",
  };

  return (
    <div
      className="consent"
      ref={consentRef}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className="consent-description">
        {paragraphs.map((paragraph, index) => (
          <p key={index} style={{ marginBottom: "15px" }}>
            {makeLinksClickable(paragraph)}
          </p>
        ))}
      </div>
      <div className="check-container">
        <CustomCheckbox
          checked={checked}
          onChange={handleChange}
          style={{
            color: "#000000",
            margin: 0,
            padding: 0,
          }}
        />
        <p
          className="breasts-description"
          style={{
            textAlign:
              titleStyles.textAlign as React.CSSProperties["textAlign"],
            justifyContent:
              titleStyles.textAlign as React.CSSProperties["textAlign"],
            letterSpacing: (titleStyles as any)?.letterSpacing,
          }}
        >
          {t("access")}
        </p>
      </div>
      <button
        type="button"
        className={`continue-button breasts-continue`}
        onMouseEnter={(e) => {
          setHovered(true);
          if (checked) {
            Object.assign(e.currentTarget.style, hoverStylesContinue);
          }
        }}
        onMouseLeave={(e) => {
          setHovered(false);
          if (checked) {
            Object.assign(e.currentTarget.style, continueButtonStyles);
          }
        }}
        disabled={!checked}
        onClick={() => nextStep()}
        style={CTAStyles}
      >
        <div style={{ display: "inline-flex", alignItems: "center", gap: 0 }}>
          {continueButtonStyles.textTransform === "capitalize"
            ? capitalizeFirstLetter(t("submit"))
            : t("submit")}

          {isIntersport && (
            <span style={arrowContainerStyle} aria-hidden>
              <ChevronRight style={iconStyle} />
            </span>
          )}
        </div>
      </button>
    </div>
  );
};

export default ConsentScreen;
