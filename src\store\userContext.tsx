import React, { useState, useEffect, useContext, useRef } from "react";
import PropTypes from "prop-types";
import { getLocalAndParse } from "./localStoreUtils";

import {
  newUser,
  newMeasure,
  questionPushAnswers,
  visionCheck,
  shoeChildrenRecommend,
} from "../api/endpoints";
import { FOOTWEAR_WEB_APP_URL } from "../constants/modal";
import { logger } from "../utils/logging";
import {
  postGetMid,
  postGetSizes,
  postSendMid,
  postSendUid,
  useIsMobile,
} from "../utils";

type MeasureType = "question" | "scan";

const urlParameters = new URLSearchParams(window.location.search);

interface IPropsUserContext {
  user: {
    age?: number;
    height?: number;
    heightUnit?: string;
    weight?: number;
    weightUnit?: string;
    gender?: string;
    name?: string;
    lastName?: string;
    code?: string;
    service?: string;
  };
  pid: string | null;
  contentResults: any;
  uid: string | null;
  mid: string | null;
  currentSize: any;
  selectedSize: any;
  shoeWebAppURL: string | null;
  isScanChecked: { current: boolean };
  isQuestionPushed: { current: boolean };
  midType: { current: MeasureType | null };
  setUser: (content: any) => void;
  addResultContent: (content: any) => void;
  updateCurrentSize: (content: any) => void;
  updateSelectedSize: (content: any) => void;
  createNewUser: (domain: string) => Promise<boolean>;
  createNewMeasure: (content: MeasureType) => Promise<boolean>;
  setShoeWebAppURL: (content: string | null) => void;
  updateUid: (content: string | null) => void;
  updateMid: (content: string | null) => void;
  pushQuestionAnswers: (
    answers: object,
    isSpecificProject?: boolean
  ) => Promise<boolean>;
  handleCheckShoeRecommend: () => void;
  createShoeWebAppURL: (uid: string) => string;
}

const UserContext = React.createContext<IPropsUserContext | null>(null);

export const UserContextProvider = (props: any) => {
  // States
  const [user, setUser] = useState({
    age: undefined,
    height: undefined,
    weight: undefined,
    gender: undefined,
    name: undefined,
    lastName: undefined,
    code: undefined,
    service: undefined,
  });

  const [uid, setUid] = useState<string | null>(
    localStorage.getItem("uid") ?? null
  );
  const [mid, setMid] = useState<string | null>(
    localStorage.getItem("mid") ?? null
  );
  const [pid, setPid] = useState<string | null>(
    urlParameters.get("product_id") ?? null
  );
  const pidStored = useRef<string | null>(null);

  const [contentResults, setContentResults] = useState(null);
  const [currentSize, setCurrentSize] = useState(null);
  const [selectedSize, setSelectedSize] = useState(null);
  const [shoeWebAppURL, setShoeWebAppURL] = useState<string | null>(null);

  const midType = useRef<MeasureType | null>(null);
  const isScanChecked = useRef<boolean>(false);
  const isQuestionPushed = useRef<boolean>(false);

  const [checkCount, setCheckCount] = useState(0);
  const [isScanError, setIsScanError] = useState<boolean | undefined>(
    undefined
  );

  // Hooks
  const isMobile = useIsMobile();

  // Utils
  const createShoeWebAppURL = (uid: string) => {
    const lang = urlParameters.get("lang")?.split("-")[0].toLowerCase() || "";
    return `${FOOTWEAR_WEB_APP_URL}?uid=${uid}&product_id=${pid}&lang=${lang}&isMobile=${isMobile}`;
  };

  // Event Handlers
  const handleMessage = (event: any) => {
    if (event.data?.action === "pid_update" && event.data?.value) {
      logger.log(`PID received by PM ${event.data.value}`);
      const newPid = event.data.value;
      if (newPid && newPid !== pidStored.current) {
        logger.log("PID updated");
        pidStored.current = newPid;
        postGetSizes();
        setPid(newPid);
      }
    } else if (event.data?.action === "mid_update") {
      logger.log("MID received by PM", event.data.value);

      const localMid = event.data.value.mid;
      const localUid = event.data.value.uid;

      if (localMid && localUid) {
        localStorage.setItem("mid", localMid);
        setMid(localMid);

        localStorage.setItem("uid", localUid);
        setUid(localUid);
      }
    }
  };

  // Effects
  useEffect(() => {
    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  useEffect(() => {
    const storedUid = localStorage.getItem("uid");
    if (storedUid) {
      setUid(storedUid);
    }
    setContentResults(null);
  }, [user]);

  useEffect(() => {
    if (uid) {
      localStorage.setItem("uid", uid);
      setShoeWebAppURL(createShoeWebAppURL(uid));
    }

    const isUserDefined = Object.values(user).some(
      (value) => value !== undefined
    );
    if (isUserDefined) {
      const oldUser = getLocalAndParse("user");
      const newUser = { ...oldUser, ...user };
      localStorage.setItem("user", JSON.stringify(newUser));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uid, user]);

  useEffect(() => {
    postGetMid();
  }, []);

  // Context Methods
  const addResultContent = (content: any) => {
    setContentResults(content);
  };

  const updateSelectedSize = (content: any) => {
    setSelectedSize(content);
  };

  const updateCurrentSize = (content: any) => {
    setCurrentSize(content);
  };

  const updateMid = (content: string | null) => {
    if (content) {
      localStorage.setItem("mid", content);
      setMid(content);
      postSendMid(content);
    }
  };

  const updateUid = (content: string | null) => {
    if (content) {
      localStorage.setItem("uid", content);
      setUid(content);
      postSendUid(content);
    }
  };

  const createNewUser = async (domain: string) => {
    return await newUser(domain)
      .then((newUid) => {
        updateUid(newUid);
        return true;
      })
      .catch((e) => {
        logger.log("Error when calling /new-user", e);
        return false;
      });
  };

  const pushQuestionAnswers = async (
    answers: object,
    isSpecificProject?: boolean
  ) => {
    return await questionPushAnswers(answers, isSpecificProject)
      .then((value) => {
        if (value) isQuestionPushed.current = true;
        return isQuestionPushed.current;
      })
      .catch((e) => {
        logger.log("Error when calling /question-push-answers", e);
        return false;
      });
  };

  const createNewMeasure = async (type: MeasureType) => {
    midType.current = type;
    if (type === "scan") isScanChecked.current = false;
    else isQuestionPushed.current = false;

    return await newMeasure(type)
      .then((newMid) => {
        if (newMid) {
          updateMid(newMid);
          return true;
        } else {
          return false;
        }
      })
      .catch((e) => {
        logger.log(`Error when calling /new-measure: ${e}`);
        return false;
      });
  };

  const handleCheckShoeRecommend = () => {
    const retryInterval = 1000;

    const check = () => {
      if (!uid) return;

      visionCheck(uid)
        .then((value: any) => {
          logger.log("visionCheck", value);

          if (value?.data?.completed) {
            const date_retry: number | null = sessionStorage.getItem(
              "shoe_retry"
            )
              ? parseInt(sessionStorage.getItem("shoe_retry")!, 10)
              : null;
            const date_completion: number | null = new Date(
              value?.data?.completion_time
            ).getTime();

            if (!date_completion) {
              logger.warn("Something wrong with date_completion");
            }

            logger.log(date_completion, date_retry);

            if (date_retry && date_completion < date_retry) {
              setTimeout(check, retryInterval);
            } else {
              setCheckCount(0);
              setIsScanError(false);

              if (pid) {
                shoeChildrenRecommend(pid, false).then((result: any) => {
                  if (result?.success) {
                    updateSelectedSize(result?.data);
                  } else {
                    setCheckCount(0);
                    setIsScanError(true);
                  }
                });
              }
            }
          } else {
            setTimeout(check, retryInterval);
          }
        })
        .catch((error: string) => {
          logger.error(`Error: scan shoe failed - ${error}`);
          setCheckCount(0);
          setIsScanError(true);
        });
    };

    check();
  };

  return (
    <UserContext.Provider
      value={{
        pid,
        uid,
        mid,
        user,
        contentResults,
        currentSize,
        selectedSize,
        shoeWebAppURL,
        isScanChecked,
        isQuestionPushed,
        midType,
        setUser,
        addResultContent,
        updateCurrentSize,
        updateSelectedSize,
        createNewUser,
        createNewMeasure,
        setShoeWebAppURL,
        updateUid,
        updateMid,
        pushQuestionAnswers,
        handleCheckShoeRecommend,
        createShoeWebAppURL,
      }}
    >
      {props.children}
    </UserContext.Provider>
  );
};

UserContextProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useUserContext = () => {
  return useContext(UserContext);
};
