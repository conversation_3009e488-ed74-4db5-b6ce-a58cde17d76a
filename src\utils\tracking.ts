import { pushAnalytics, pushGtag } from "../api/endpoints";
import { MODAL_STEPS } from "../constants/modal";

export const handleAnalytics = async (
  event_type: "step" | "action",
  event_name: string,
  data: {
    key: string;
    value: string | number | boolean;
  } | null
) => {
  const hostname = window.location.hostname;
  const urlParams = new URLSearchParams(window.location.search);

  const is_test = hostname.includes("localhost") || hostname.includes("dev");
  const is_mock = urlParams.get("mock") === "true";
  const is_tracking_disabled = urlParams.get("disableTracking") === "true";

  const disable = is_tracking_disabled || is_test || is_mock;

  if (!disable) {
    await pushAnalytics({
      event_type,
      event_name,
      data: data
        ? {
            key: data.key,
            value: data.value,
          }
        : null,
    });

    await pushGtag({
      event_type,
      event_name,
      data: data
        ? {
            key: data.key,
            value: data.value,
          }
        : null,
    });
  }
};

export const getStepByNumber = (number: number) => {
  const foundStep = Object.keys(MODAL_STEPS).find(
    (key) => MODAL_STEPS[key as keyof typeof MODAL_STEPS].number === number
  );

  return foundStep ? `${number}_${foundStep}` : "";
};

export const handleTrackingStep = (stepNumber: number | undefined) => {
  if (stepNumber !== undefined) {
    if (stepNumber !== 1 && stepNumber !== 3) {
      handleAnalytics("step", `step_${stepNumber}`, null);
    }
  }
};
