import { useTranslation } from "react-i18next";
import { MODAL_STEPS } from "../../../../constants/modal";
import { capitalizeFirstLetter, useIsMobile } from "../../../../utils";
import React from "react";

import "./index.css";

interface MobileStepTitleProps {
  isSRP: boolean;
  isShoesProduct: boolean;
  brandName?: string;
  step: any | null;
  titleTextTransform: string;
  titleStyles: any;
  outOfRange: boolean;
  MODAL_STEPS_TITLE: string[];
}

export const MobileStepTitle = ({
  isSRP,
  isShoesProduct,
  brandName,
  step,
  titleTextTransform,
  titleStyles,
  outOfRange,
  MODAL_STEPS_TITLE,
}: MobileStepTitleProps) => {
  const isMobile = useIsMobile();

  const { t } = useTranslation("components/intro");

  const OUT_OF_RANGE_TITLE = t("steps.error_outOfRange");

  const isFirstIntroScreen =
    step?.number === MODAL_STEPS.INTRO.number ||
    step?.number === MODAL_STEPS.GENDER.number ||
    step?.number === MODAL_STEPS.INTRO_MOBILE.number;

  if (
    !isMobile ||
    !step ||
    brandName === "Victoria Beckham" ||
    step?.number === MODAL_STEPS.QR_CODE.number ||
    (isMobile && isShoesProduct && isFirstIntroScreen) ||
    (isMobile && brandName === "Velour Garments")
  ) {
    return null;
  }

  const getTitle = () => {
    if (outOfRange && step?.number === MODAL_STEPS.ERROR.number)
      return OUT_OF_RANGE_TITLE.replace(/&nbsp;/g, "\u00A0");

    if (titleTextTransform === "capitalize") {
      return capitalizeFirstLetter(MODAL_STEPS_TITLE[step?.number]).replace(
        /&nbsp;/g,
        "\u00A0"
      );
    }

    return MODAL_STEPS_TITLE[step?.number].replace(/&nbsp;/g, "\u00A0");
  };

  return (
    <h2
      style={{
        textAlign:
          brandName === "Lacoste" && step?.number === MODAL_STEPS.RESULT.number
            ? "center"
            : titleStyles.textAlign,
        fontWeight: titleStyles.fontWeight,
        textTransform: (titleTextTransform === "capitalize"
          ? "none"
          : titleTextTransform) as React.CSSProperties["textTransform"],
        color: isShoesProduct ? "#2E2E2E" : titleStyles.color,
        fontSize: titleStyles.fontSize,
        minHeight: "25px",
        marginTop:
          isMobile && isSRP && step?.number === MODAL_STEPS.RESULT.number
            ? 0
            : "15px",
        letterSpacing: titleStyles.letterSpacing,
      }}
    >
      {getTitle()}
    </h2>
  );
};

export default MobileStepTitle;
