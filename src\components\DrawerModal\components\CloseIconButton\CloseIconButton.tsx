import { IconButton } from "@mui/material";
import { getCloseButtonSize, useIsMobile } from "../../../../utils";
import { useMediaQuery } from "react-responsive";
import CloseIcon from "../../../../assets/icons/CloseIcon";
import { findBrandByDomain } from "../../../../configs/configLoader";

import "./index.css";

interface CloseIconButtonProps {
  step: any | null;
  isShoesProduct: boolean;
  brandName?: string;
  titleStyles: any;
  closeIframe: () => void;
}

export const CloseIconButton = ({
  step,
  isShoesProduct,
  brandName,
  titleStyles,
  closeIframe,
}: CloseIconButtonProps) => {
  const isMobile = useIsMobile();

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: 830,
    minWidth: 767,
  });

  const isMediumHeightRelative = useMediaQuery({
    maxHeight: 678,
    minWidth: 767,
  });

  if (!step || step?.number < 0) return null;

  const brandDefined = findBrandByDomain();

  const isSRP = brandDefined?.name === "SRP";
  const isSportyAndRich = brandDefined?.name === "Sporty & Rich";

  const getDynamicTop = (): string => {
    if (isMobile && isSRP) return "0px";
    if (isLargeHeightRelative && !isSportyAndRich) return "10px";
    if (isMediumHeightRelative && isSportyAndRich) return "10px";
    if (isShoesProduct && !isSportyAndRich) return "10px";
    return "15px";
  };

  const closeButtonSize = getCloseButtonSize(
    isMobile,
    brandName,
    { mobile: "16px", desktop: "18px" }, // standart
    { mobile: "18px", desktop: "20px" } // custom
  );

  return (
    <IconButton
      onClick={closeIframe}
      className="close-icon"
      sx={{
        position: "fixed",
        right: "20px",
        top: getDynamicTop(),
      }}
    >
      <CloseIcon
        className="icon"
        style={{
          color: titleStyles.color ?? "black",
          width: closeButtonSize,
          height: closeButtonSize,
        }}
      />
    </IconButton>
  );
};

export default CloseIconButton;
