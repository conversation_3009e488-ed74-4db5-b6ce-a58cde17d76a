{"name": "Chlore", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "14px", "fontColor": "#050505", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#050505", "marginBottom": "10px", "padding": "12px 0", "borderBottom": "1px solid #050505"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#050505", "borderRadius": "0", "borderColor": "#9fa0a1", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#050505", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "2px", "fontSize": "12px", "fontWeight": "700", "textTransform": "uppercase"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#050505", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#050505"}}, "3": {"qrcode": {"backgroundColor": "#050505"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#050505", "textAlign": "center"}, "subtitles": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textAlign": "center"}, "sizeSelector": {"focused": {"backgroundColor": "#050505", "fontWeight": "700", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#050505", "borderWidth": "1px"}, "unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "700", "fontSize": "12px", "fontColor": "#050505", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#9fa0a1", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "700", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "4px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#050505", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Montserrat", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#050505", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "300", "textTransform": "capitalize", "color": "#050505", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#9fa0a1", "fontWeight": "700", "fontSize": "12px", "fontColor": "#FAFAFA", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "", "borderWidth": "", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#050505", "fontWeight": "700", "fontColor": "#FAFAFA", "fontSize": "12px", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#050505", "borderWidth": ""}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#050505", "borderRadius": "0", "fontSize": "12px", "fontWeight": "400", "fontColor": "#050505", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#9fa0a1", "borderWidth": "1px", "borderRadius": "none", "fontWeight": "700", "fontSize": "12px", "fontColor": "#050505", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#050505", "borderWidth": "2px", "borderRadius": "none", "fontWeight": "700", "fontSize": "12px", "fontColor": "#050505", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#152F4E"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#050505", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A7A7A7", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#050505", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#050505", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#9fa0a1", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#050505", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "#050505", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#050505", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}