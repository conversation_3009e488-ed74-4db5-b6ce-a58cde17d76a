{"name": "Place du Jour", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "700", "fontSize": "14px", "fontColor": "#052e5d", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#052e5d", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#052e5d", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #052e5d"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#052e5d", "borderRadius": "25px", "borderColor": "#052e5d", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "lowercase"}, "focused": {"backgroundColor": "#052e5d", "fontColor": "#FFFFFF", "borderRadius": "25px", "borderColor": "", "borderWidth": "", "fontSize": "12px", "fontWeight": "500", "textTransform": "lowercase"}}, "units": {"fontWeight": "500", "fontSize": "12px", "top": "10px", "right": "", "activeColor": "#052e5d", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#052e5d"}}, "3": {"qrcode": {"backgroundColor": "#052e5d"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#052e5d", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#052e5d", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#052e5d", "textTransform": "capitalize", "borderRadius": "10px", "borderColor": "#052e5d", "borderWidth": "2px"}, "focused": {"backgroundColor": "#052e5d", "fontWeight": "500", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "capitalize", "borderRadius": "10px", "borderColor": "#052e5d", "borderWidth": "2px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "10px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#052e5d", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#052e5d", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#052e5d", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "<PERSON><PERSON><PERSON>", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "capitalize", "color": "#052e5d", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#052e5d", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#c9ccd3", "fontWeight": "500", "fontSize": "12px", "fontColor": "#052e5d7a", "textTransform": "uppercase", "borderRadius": "25px", "borderColor": "#b8b8b8", "borderWidth": "", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#052e5d", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "uppercase", "borderRadius": "25px", "borderColor": "#052e5d", "borderWidth": ""}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#052e5d", "borderRadius": "25px", "fontSize": "12px", "fontWeight": "500", "fontColor": "#052e5d", "textTransform": "capitalize", "fontStyle": "underline"}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#052e5d", "borderWidth": "2px", "borderRadius": "10px", "fontWeight": "500", "fontSize": "12px", "fontColor": "#052e5d", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#052e5d", "borderWidth": "3px", "borderRadius": "10px", "fontWeight": "500", "fontSize": "12px", "fontColor": "#052e5d", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#052e5d"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#052e5d", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#052e5d", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#052e5d", "fontSize": "12px", "fontWeight": "400", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#052e5d", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "25px", "borderColor": "#052e5d", "borderWidth": "1px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#052e5d", "fontWeight": "400", "fontStyle": "underline", "fontSize": "12px"}}}}}