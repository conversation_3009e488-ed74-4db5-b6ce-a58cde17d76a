import { FC, useState } from "react";

import QRCode from "react-qr-code";
import <PERSON><PERSON> from "lottie-react";

import { bodyScanScreenUI } from "../../configs";
import { useTranslation } from "react-i18next";
import { font } from "../../configs";
import HelpIcon from "../../assets/icons/HelpIcon";
import { Popper, Typography, Paper, ClickAwayListener } from "@mui/material";

import "./index.css";

interface IPropsQrcodeDesktop {
  url?: string;
  loading?: boolean;
  subtitleStyles: {
    color: React.CSSProperties["color"];
    fontSize: React.CSSProperties["fontSize"];
    fontWeight: React.CSSProperties["fontWeight"];
    textAlign: React.CSSProperties["textAlign"];
    textTransform: React.CSSProperties["textTransform"];
    justifyContent: React.CSSProperties["justifyContent"];
  };
}

const QrcodeDesktop: FC<IPropsQrcodeDesktop> = ({
  url,
  loading,
  subtitleStyles,
}) => {
  const { t } = useTranslation("components/qrcode/mobile");

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const open = Boolean(anchorEl);
  const id = open ? "info-popper" : undefined;

  return (
    <>
      {!loading ? (
        <QRCode
          className="qrcode"
          size={220}
          value={`${url}`}
          fgColor={bodyScanScreenUI.qrcode.backgroundColor}
          viewBox={"0 0 256 256"}
        />
      ) : (
        <Lottie
          className="qrcode_animation"
          animationData={require("../../assets/animations/qrcode_loader.json")}
        />
      )}
      <div className="help-info">
        <HelpIcon />
        <span
          className="link"
          aria-describedby={id}
          onMouseEnter={(e) => setAnchorEl(e.currentTarget)}
          onMouseLeave={() => setAnchorEl(null)}
          style={{
            color: `${subtitleStyles.color}`,
            fontFamily: `${font}, sans-serif`,
          }}
        >
          {t("info")}
        </span>
        <Popper id={id} open={open} anchorEl={anchorEl} placement="top">
          <ClickAwayListener onClickAway={() => setAnchorEl(null)}>
            <Paper
              className="popper-container"
              style={{
                border: `1px solid ${subtitleStyles.color}`,
                fontFamily: `${font}, sans-serif`,
              }}
            >
              <Typography
                style={{
                  color: `${subtitleStyles.color}`,
                  fontFamily: `${font}, sans-serif`,
                }}
              >
                {t("popupText")}
              </Typography>
              <div
                className="popper-triangle"
                style={{
                  borderTop: `10px solid ${subtitleStyles.color}`,
                }}
              />
            </Paper>
          </ClickAwayListener>
        </Popper>
      </div>
    </>
  );
};

export default QrcodeDesktop;
