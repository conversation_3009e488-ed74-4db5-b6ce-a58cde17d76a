export interface IUserLocation {
  ip: string;
  country: string;
  region: string;
  timezone: string;
}

interface IUserBrowser {
  type: string;
  lang: string;
}

interface IUserDevice {
  type: "mobile" | "tablet" | "desktop";
  model: string;
  screenWidth: number;
  screenHeight: number;
}

export interface IUserPushAnalytics {
  device: IUserDevice;
  browser: IUserBrowser;
  location: IUserLocation;
}

interface IDataPushAnalytics {
  key: string;
  value: string | number | boolean | string[] | number[] | boolean[];
}

export interface IPushAnalytics {
  event_type: "step" | "action";
  event_name: string;
  data: IDataPushAnalytics | null;
}

export interface IPushAnalyticsResponse {
  event: string;
  customer_id: string | null;
  user_id: string | null;
  measure_id: string | null;
  session_id: string;
  retailer: string;
  timestamp: number;
  data: {
    is_test: boolean;
    event_type: "step" | "action";
    event_name: string;
    user: IUserPushAnalytics;
    data: IDataPushAnalytics | null;
  };
}

export interface IVisionCheckBody {
  user: {
    id: string;
    answers: {
      usual_size: {
        value: string | null;
        system: string;
      };
    };
  };
}

export interface IQuestionsPushBody {
  user: {
    id: string;
    answers: {
      usual_size: {
        value: string | null;
        system: string;
      };
      foot_width: string;
      gender: string;
    };
  };
}
