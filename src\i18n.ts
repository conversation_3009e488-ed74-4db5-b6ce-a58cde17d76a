import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { logger } from "./utils/logging";
import { handleParentDomain } from "./configs/configLoader";
import { Translations } from "./types/translations";
import { brandsArray } from "./configs/brandsArray";

const parentDomain = handleParentDomain();

const matchedDomain = brandsArray.find((b) =>
  b.domains.some((d) => d.name === parentDomain?.subdomain?.name)
);

const domain = matchedDomain
  ? matchedDomain.name.toLowerCase().replace(/\s+/g, "")
  : "default";

const region = parentDomain?.region;

const namespaces = [
  "components/intro",
  "components/device_select",
  "components/qrcode/desktop",
  "components/qrcode/mobile",
  "components/belly",
  "components/torso",
  "components/cuisses",
  "components/breasts",
  "components/sleeves",
  "components/results/error",
  "components/results/result",
  "components/antiBracketing",
  "components/consentement",
  "components/lingerie-question",
];

const TRANSLATIONS_CACHE_KEY = "translations_cache";

const handleDefaultConfig = async (lng: string) => {
  const defaultPath = `/locales/default/${lng}.json`;

  try {
    const response = await fetch(defaultPath);
    if (response.ok) {
      logger.log(`⚠️ Fallback to default domain config (${lng})`);
      return await response.json();
    }
  } catch {
    logger.warn(`Could not load default translation for path: ${defaultPath}`);
  }

  return {};
};

const getDomainJson = async (domain: string, lng: string, region?: string) => {
  const domainsToTry = [];

  if (region) {
    domainsToTry.push(`${domain}/${region}`);
  }

  domainsToTry.push(domain);

  for (const b of domainsToTry) {
    const domainPath = `/locales/${b}/${lng}.json`;
    try {
      const response = await fetch(domainPath);
      if (response.ok) {
        logger.log(`✅ Loaded domain config: ${b} (${lng})`);
        return await response.json();
      }
    } catch {
      logger.warn(`Could not load translation for path: ${domainPath}`);
    }
  }

  return await handleDefaultConfig(lng);
};

export async function loadTranslations(lang: string) {
  const lng =
    parentDomain?.unit_system === "imperial" ? "en" : lang?.trim() || "fr";

  const force_update = true;
  const cachedTranslations = localStorage.getItem(TRANSLATIONS_CACHE_KEY);

  if (cachedTranslations && !force_update) {
    try {
      const allTranslations = JSON.parse(cachedTranslations);
      addTranslationsToI18n(allTranslations, lng);
      return;
    } catch (error) {
      logger.error(`Error parsing cached translations: ${error}`);
    }
  }

  const translations: Translations = { default: {}, [domain]: {} };

  try {
    const data = await getDomainJson(domain, lng, region);
    translations[domain][lng] = data;

    if (domain !== "default") {
      const defaultPath = `/locales/default/${lng}.json`;
      const response = await fetch(defaultPath);
      if (response.ok) {
        const data = await response.json();
        translations.default[lng] = data;
      }
    }

    localStorage.setItem(TRANSLATIONS_CACHE_KEY, JSON.stringify(translations));
    addTranslationsToI18n(translations, lng);
  } catch (error) {
    logger.error(`Error loading translations: ${error}`);
  }
}

function addTranslationsToI18n(allTranslations: Translations, lng: string) {
  const defaultTranslations = allTranslations["default"] || {};
  let domainTranslations: Record<string, any> = allTranslations[domain] || {};

  if (
    typeof domainTranslations === "object" &&
    Object.keys(domainTranslations).length === 0 &&
    domain !== "default"
  ) {
    const groupedKey = Object.keys(allTranslations).find((key) =>
      key.split("/").includes(domain)
    );
    domainTranslations = groupedKey ? allTranslations[groupedKey] : {};
  }

  for (const ns of namespaces) {
    const defaultNS = defaultTranslations[lng]?.[ns] || {};
    const domainNS =
      domainTranslations[lng]?.[ns] ||
      domainTranslations["default"]?.[ns] ||
      {};

    const mergedTranslation = { ...defaultNS, ...domainNS };
    i18n.addResourceBundle(lng, ns, mergedTranslation, true, true);
  }
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: "en",
    ns: namespaces,
    defaultNS: "components/intro",
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    saveMissing: false,
  });

export { i18n };
