import { FC } from "react";
import { MenuItem, Select } from "@mui/material";

import { BASE_SIZES, SHOES_BASE_SIZES } from "../../../../constants/modal";
import { font, femaleBraScreenUI } from "../../../../configs";

import RotatedArrowIcon from "../RotatedArrowIcon";
import { findBrandByDomain } from "../../../../configs/configLoader";

import "./index.css";

interface IPropsBaseSizeSelector {
  sizeCountry: string;
  setSizeCountry: React.Dispatch<React.SetStateAction<string>>;
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isShoesProduct: boolean;
}

const BaseSizeSelector: FC<IPropsBaseSizeSelector> = ({
  sizeCountry,
  setSizeCountry,
  open,
  setOpen,
  isShoesProduct,
}) => {
  const brandDefined = findBrandByDomain();

  const fontFamily = `${font}, sans-serif`;

  const currentState = open ? "open" : "closed";

  const langDropdownStyles = {
    color: femaleBraScreenUI.sizeSystem[currentState].fontColor,
    borderColor: femaleBraScreenUI.sizeSystem[currentState].borderColor,
    borderWidth: femaleBraScreenUI.sizeSystem[currentState].borderWidth,
    borderRadius: femaleBraScreenUI.sizeSystem[currentState].borderRadius,
    letterSpacing: (femaleBraScreenUI.sizeSystem[currentState] as any)
      ?.letterSpacing,
  };

  return (
    <Select
      value={sizeCountry}
      onChange={(event) => {
        setSizeCountry(event.target.value);
      }}
      open={open}
      onClose={() => setOpen(false)}
      className="breasts__select"
      IconComponent={(props) => <RotatedArrowIcon open={open} {...props} />}
      MenuProps={{
        MenuListProps: {
          sx: {
            padding: 0,
            fontFamily,
            textAlign: "center",
            "& .Mui-selected": {
              display: "none",
            },
          },
        },
        PaperProps: {
          sx: {
            fontFamily,
            textAlign: "center",
            border: `${langDropdownStyles.borderWidth} solid ${langDropdownStyles.borderColor}`,
            borderTopColor: "transparent",
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0,
            boxShadow: "none !important",
            transform: "translateY(-5px) !important",
            borderBottomLeftRadius: langDropdownStyles.borderRadius,
            borderBottomRightRadius: langDropdownStyles.borderRadius,
            paddingBottom:
              langDropdownStyles.borderRadius === "23px" && open ? "10px" : 0,
          },
        },
      }}
      sx={{
        fontFamily,
        border: `${langDropdownStyles.borderWidth} solid ${langDropdownStyles.borderColor}`,
        borderBottomColor: open
          ? "transparent"
          : langDropdownStyles.borderColor,
        borderRadius: open
          ? `${langDropdownStyles.borderRadius} ${langDropdownStyles.borderRadius} 0 0`
          : langDropdownStyles.borderRadius,
        padding:
          langDropdownStyles.borderRadius === "23px" && open ? "20px 0" : 0,
      }}
    >
      {(isShoesProduct ? SHOES_BASE_SIZES : BASE_SIZES)?.map(({ name }) => (
        <MenuItem
          key={name}
          className="breasts__select__item"
          value={name}
          style={{
            fontFamily,
          }}
        >
          <span
            className="breasts__select__item__flag-icons"
            style={{
              color: femaleBraScreenUI.sizeSystem.closed.fontColor,
              position: "relative",
              left: brandDefined?.name === "Victoria Beckham" ? "12px" : "8px",
              fontStyle:
                brandDefined?.name === "Victoria Beckham" ? "normal" : "",
            }}
          >
            {name}
          </span>
        </MenuItem>
      ))}
    </Select>
  );
};

export default BaseSizeSelector;
