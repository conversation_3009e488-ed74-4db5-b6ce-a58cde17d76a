{"name": "Club Brugge", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textTransform": "capitalize", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "13px", "fontColor": "#91A4C2", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textAlign": "left"}, "borderRadius": "0px", "mobileBorderRadius": "0px", "borderColor": "#2A3548", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid 000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#2A3548", "borderRadius": "30px", "borderColor": "#2A3548", "borderWidth": "1px", "fontSize": "13px", "fontWeight": "400", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#0572FF", "fontColor": "#FFFFFF", "borderRadius": "30px", "borderColor": "#0572FF", "borderWidth": "1px", "fontSize": "13px", "fontWeight": "400", "textTransform": "capitalize"}}, "units": {"fontWeight": "400", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#2A3548", "inactiveColor": "#91A4C2"}}, "2": {"routeCTA": {"borderColor": "#2A3548", "borderWidth": "1px", "borderRadius": "0px", "fontColor": "#FFFFFF"}}, "3": {"qrcode": {"backgroundColor": "#2A3548"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#2A3548", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "13px", "fontColor": "#2A3548", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#91A4C2", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#44883f", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "capitalize", "fontSize": "13px", "fontColor": "#2A3548", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "13px", "fontColor": "#383838", "textTransform": "capitalize", "textDecoration": "underline", "borderRadius": "0px", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Roboto", "titles": {"textAlign": "left", "fontWeight": "400", "textTransform": "uppercase", "color": "#2A3548", "fontSize": "24px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "capitalize", "color": "#91A4C2", "fontSize": "13px"}, "cta": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textTransform": "capitalize", "borderRadius": "30px", "borderColor": "#2A3548", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#0572FF", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "13px", "textTransform": "capitalize", "borderRadius": "30px", "borderColor": "#0572FF", "borderWidth": "1px"}}, "Politicy": {"fontSize": "10px", "fontColor": "#383838", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#2A3548", "borderRadius": "30px", "fontSize": "13px", "fontWeight": "400", "fontColor": "#2A3548", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#91A4C2", "borderWidth": "1px", "borderRadius": "3px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textAlign": "center", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#0572FF", "borderWidth": "2px", "borderRadius": "3px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textAlign": "center", "textTransform": "capitalize"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#2A3548"}, "unfocused": {"color": "#91A4C2"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#2A3548", "borderWidth": "1px", "borderRadius": "3px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textTransform": "capitalize", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#2A3548", "borderWidth": "1px", "borderRadius": "3px", "fontWeight": "400", "fontSize": "13px", "fontColor": "#2A3548", "textTransform": "capitalize", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#2A3548", "fontSize": "13px", "fontWeight": "400", "borderRadius": "3px", "borderColor": "#2A3548", "borderWidth": "1px", "textTransform": "capitalize"}, "focused": {"backgroundColor": "#0572FF", "fontColor": "#FFFFFF", "fontSize": "13px", "fontWeight": "400", "borderRadius": "3px", "borderColor": "#0572FF", "borderWidth": "1px", "textTransform": "capitalize"}}, "skip": {"fontColor": "#383838", "fontWeight": "400", "fontStyle": "", "fontSize": "13px"}}}}}