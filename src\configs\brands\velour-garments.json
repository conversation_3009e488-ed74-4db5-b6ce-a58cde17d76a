{"name": "Velour Garments", "config": {"ux": {"gender": "", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textTransform": "uppercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "300", "fontSize": "16px", "fontColor": "#A3A1A7", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#393939", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #393939"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#393939", "borderRadius": "0", "borderColor": "#393939", "borderWidth": "1px", "fontSize": "16px", "fontWeight": "400", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#393939", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "fontSize": "16px", "fontWeight": "400", "textTransform": "uppercase"}}, "units": {"fontWeight": "400", "fontSize": "12px", "top": "-40px", "right": "", "activeColor": "#000000", "inactiveColor": "#FFFFFF"}}, "2": {"routeCTA": {"borderColor": "#393939", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#393939"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#393939", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#393939", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#D9D9D9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "16px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "16px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontTransform": "uppercase", "fontSize": "14px", "fontColor": "#393939", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#393939", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textTransform": "uppercase", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#888888", "textAlign": "center", "textTransform": "uppercase"}}, "all": {"font": "Inter", "titles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#393939", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "700", "textTransform": "uppercase", "color": "#393939", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#d9d9d9", "fontWeight": "400", "fontSize": "16px", "fontColor": "#FFFFFF", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#393939", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "16px", "textTransform": "uppercase", "borderRadius": "0", "borderColor": "", "borderWidth": "0px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#393939", "fontWeight": "regular", "textTransform": "uppercase"}}, "questions": {"scanCTA": {"borderWidth": "", "borderColor": "", "borderRadius": "", "fontSize": "", "fontWeight": "", "fontColor": "", "textTransform": "", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#393939", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textAlign": "center", "textTransform": "uppercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#393939", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textAlign": "center", "textTransform": "uppercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#393939"}, "unfocused": {"color": "#888888"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#393939", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textTransform": "uppercase", "textAlign": "center"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#393939", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "16px", "fontColor": "#393939", "textTransform": "uppercase", "textAlign": "center"}}, "sizeSelector": {"unfocused": {"backgroundColor": "", "fontColor": "#000000", "fontSize": "16px", "fontWeight": "400", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "textTransform": "uppercase"}, "focused": {"backgroundColor": "", "fontColor": "#FFFFFF", "fontSize": "16px", "fontWeight": "400", "borderRadius": "0", "borderColor": "", "borderWidth": "0px", "textTransform": "uppercase"}}, "skip": {"fontColor": "#393939", "fontWeight": "400", "fontStyle": "", "fontSize": "16px"}}}}}