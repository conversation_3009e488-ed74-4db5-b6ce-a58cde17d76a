{"name": "Faguo", "config": {"ux": {"gender": "male", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "600", "fontSize": "16px", "fontColor": "#191F40", "textTransform": "unset", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "14px", "fontColor": "#A2A2A2", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#262626", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#262626", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #262626"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#191F40", "borderRadius": "3px", "borderColor": "#191F40", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "textTransform": ""}, "focused": {"backgroundColor": "#191F40", "fontColor": "#FFFFFF", "borderRadius": "3px", "borderColor": "#191F40", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": ""}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#191F40", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#191F40", "borderWidth": "1px", "borderRadius": "0", "fontColor": "#191F40"}}, "3": {"qrcode": {"backgroundColor": "#191F40"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#191F40", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#44883f", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#191F40", "textTransform": "uppercase", "borderRadius": "3px", "borderColor": "#D9D9D9", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#44883f", "textTransform": "uppercase", "borderRadius": "3px", "borderColor": "#44883f", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "400", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "uppercase", "borderRadius": "3px", "borderColor": "#CEA13F", "borderWidth": "2px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#191F40", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#191F40", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "400", "fontSize": "12px", "fontColor": "#191F40", "textTransform": "capitalize", "textDecoration": "", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Overpass", "titles": {"textAlign": "left", "fontWeight": "600", "textTransform": "", "color": "#191F40", "fontSize": "18px"}, "subtitles": {"textAlign": "left", "fontWeight": "400", "textTransform": "", "color": "#191F40", "fontSize": "16px"}, "cta": {"unfocused": {"backgroundColor": "#A2A2A2", "fontWeight": "400", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "", "borderRadius": "3px", "borderColor": "transparent", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#191F40", "fontWeight": "400", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "", "borderRadius": "3px", "borderColor": "transparent", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#191F40", "borderRadius": "0", "fontSize": "13px", "fontWeight": "400", "fontColor": "#191F40", "textTransform": "capitalize", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#191F40", "borderWidth": "1px", "borderRadius": "3px", "fontWeight": "400", "fontSize": "12px", "fontColor": "#191F40", "textAlign": "center", "textTransform": ""}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#191F40", "borderWidth": "2px", "borderRadius": "3px", "fontWeight": "500", "fontSize": "12px", "fontColor": "#191F40", "textAlign": "center", "textTransform": ""}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#191F40"}, "unfocused": {"color": "#A3A1A7"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#A2A2A2", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#191F40", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#A2A2A2", "borderWidth": "1px", "borderRadius": "0", "fontWeight": "400", "fontSize": "12px", "fontColor": "#191F40", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#A2A2A2", "fontSize": "12px", "fontWeight": "400", "borderRadius": "3px", "borderColor": "#191F40", "borderWidth": "1px", "textTransform": ""}, "focused": {"backgroundColor": "#191F40", "fontColor": "#FFFFFF", "fontSize": "12px", "fontWeight": "400", "borderRadius": "3px", "borderColor": "#191F40", "borderWidth": "1px", "textTransform": ""}}, "skip": {"fontColor": "#191F40", "fontWeight": "400", "fontStyle": "underline", "fontSize": "13px"}}}}}