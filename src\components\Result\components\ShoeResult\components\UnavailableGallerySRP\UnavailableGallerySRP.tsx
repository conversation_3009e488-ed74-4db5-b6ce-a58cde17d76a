import React, { FC, useState, useRef } from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import ArrowIcon from "../../../../../../assets/icons/ArrowIcon";
import ArrowIconNew from "../../../../../../assets/icons/ArrowIconNew";

import { resultScreenUI } from "../../../../../../configs";
import { findBrandByDomain } from "../../../../../../configs/configLoader";
import { useIsMobile } from "../../../../../../utils";

import "./index.css";

interface IPropsUnavailableGallerySRP {
  similarProducts: Array<{ image_url: string; title: string }>;
}

const UnavailableGallerySRP: FC<IPropsUnavailableGallerySRP> = ({
  similarProducts,
}) => {
  const isMobile = useIsMobile();

  const brandDefined = findBrandByDomain();

  const isSRP = brandDefined?.name === "SRP";

  const isLargeHeightRelative = useMediaQuery({
    maxHeight: !isMobile ? 830 : 778,
    minWidth: 767,
  });

  const { t } = useTranslation("components/results/result");

  const scrollRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeftX, setScrollLeftX] = useState(0);
  const [draggingThresholdExceeded, setDraggingThresholdExceeded] =
    useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const [arrowPositionHandle, setArrowPositionHandle] = useState(0);
  const [isLeftArrowVisible, setIsLeftArrowVisible] = useState(false);
  const [isRightArrowVisible, setIsRightArrowVisible] = useState(true);

  const scrollByItem = (direction: "left" | "right") => {
    if (scrollRef.current) {
      const itemWidth =
        scrollRef.current.querySelector(".info")?.clientWidth || 160;

      const gap = 10;
      const totalScrollAmount = itemWidth + gap;
      const newScrollPosition =
        scrollRef.current.scrollLeft +
        (direction === "right" ? totalScrollAmount : -totalScrollAmount);

      setArrowPositionHandle(newScrollPosition);

      setIsLeftArrowVisible(newScrollPosition > 0);
      setIsRightArrowVisible(newScrollPosition !== arrowPositionHandle);

      scrollRef.current.scrollTo({
        left: newScrollPosition,
        behavior: "smooth",
      });
    }
  };

  // const extractIdFromUrl = (url: string) => {
  //   const urlObj = new URL(url);
  //   return urlObj.searchParams.get("v");
  // };

  const handleClick = (item: any, index: number) => {
    // const id = extractIdFromUrl(item.image_url);

    // handleAnalytics("action", "similar_product", {
    //   key: "variant_selected",
    //   type: "INT",
    //   value: index + 1,
    // });

    // if (id) {
    //   handleAnalytics("action", "similar_product", {
    //     key: "variant_id",
    //     type: "STR",
    //     value: id,
    //   });
    // }

    if (!draggingThresholdExceeded) {
      if (item.product_url) {
        window.open(item.product_url, "_blank");
      }
    }
    setDraggingThresholdExceeded(false); // Reset after click
  };

  const scrollToProduct = (index: number) => {
    if (scrollRef.current) {
      const itemWidth =
        scrollRef.current.querySelector(".info")?.clientWidth || 160;
      const gap = 10;
      const scrollAmount = index * (itemWidth + gap);
      scrollRef.current.scrollTo({
        left: scrollAmount,
        behavior: "smooth",
      });
      setActiveIndex(index);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (scrollRef.current) {
      setIsDragging(true);
      setStartX(e.pageX - scrollRef.current.offsetLeft);
      setScrollLeftX(scrollRef.current.scrollLeft);
      setDraggingThresholdExceeded(false); // Reset threshold on new drag
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollRef.current) return;
    e.preventDefault();

    const x = e.pageX - scrollRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Adjust scrolling speed

    // Check if the dragging distance exceeds the threshold
    if (Math.abs(walk) > 5) {
      setDraggingThresholdExceeded(true);
    }

    scrollRef.current.scrollLeft = scrollLeftX - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const productTitle =
    (resultScreenUI.similarProducts as any)?.productTitle || {};

  const productTitleStyles: React.CSSProperties = {
    color: productTitle.fontColor as React.CSSProperties["color"],
    fontSize: productTitle.fontSize as React.CSSProperties["fontSize"],
    fontWeight:
      (productTitle.fontWeight as React.CSSProperties["fontWeight"]) ||
      "normal",
    maxWidth: isSRP
      ? "100px"
      : isLargeHeightRelative
      ? "120px"
      : isMobile
      ? "100px"
      : "160px",
    textAlign: isSRP ? "left" : "center",
    marginLeft: !isMobile && isSRP ? "20px" : isMobile && isSRP ? "10px" : "0",
    marginRight: isMobile && isSRP ? "5px" : "0",
    display: isSRP ? "-webkit-box" : "block",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: isSRP ? 2 : "none",
    overflow: "hidden",
    textOverflow: isSRP ? "ellipsis" : "unset",
  };

  return (
    <div className="gallery-container">
      <p
        className="title"
        style={{
          color: resultScreenUI.similarProducts.title
            .fontColor as React.CSSProperties["color"],
          fontSize:
            isSRP && !isMobile
              ? "15px"
              : isSRP && isMobile
              ? "14px"
              : (resultScreenUI.similarProducts.title
                  .fontSize as React.CSSProperties["fontSize"]),
          fontWeight: isSRP
            ? 600
            : (resultScreenUI.similarProducts.title
                .fontWeight as React.CSSProperties["fontWeight"]),
          textAlign: resultScreenUI.similarProducts.title
            .textAlign as React.CSSProperties["textAlign"],
          textTransform: isSRP ? "uppercase" : "none",
          margin: isSRP ? "5px 0 5px 0" : "20px 0 5px 0",
          letterSpacing: (resultScreenUI.similarProducts as any)?.letterSpacing,
        }}
      >
        {t("unavailable.title")}
      </p>
      <div style={{ display: "flex", alignItems: "center" }}>
        {isLeftArrowVisible && similarProducts?.length ? (
          <div
            onClick={() => scrollByItem("left")}
            className={isSRP ? "left-arrow-srp" : "left-arrow"}
          >
            {isSRP ? (
              <ArrowIconNew style={{ transform: "rotate(180deg)" }} />
            ) : (
              <ArrowIcon />
            )}
          </div>
        ) : null}
        <div
          className="scroll-container"
          ref={scrollRef}
          style={{
            overflowX: "auto",
            whiteSpace: "nowrap",
            display: "inline-flex",
            marginBottom:
              isMobile && !isSRP ? "30px" : isMobile && isSRP ? 0 : 0,
            cursor: isDragging ? "grabbing" : "grab",
            width: isLargeHeightRelative ? "auto" : "600px",
          }}
          onMouseDown={handleMouseDown}
          onMouseLeave={handleMouseLeave}
          onMouseUp={handleMouseUp}
          onMouseMove={handleMouseMove}
        >
          {similarProducts?.map((item, index) => (
            <div
              key={index}
              className="info"
              style={{
                minWidth: isLargeHeightRelative
                  ? "120px"
                  : isMobile
                  ? "86px"
                  : "160px",
                maxWidth: isLargeHeightRelative
                  ? "120px"
                  : isMobile
                  ? "86px"
                  : "160px",
                flex: "0 0 auto",
                borderRadius: isSRP ? "12px" : 0,
                backgroundColor: isSRP ? "white" : "transparent",
                paddingBottom:
                  !isMobile && isSRP
                    ? "20px"
                    : isMobile && isSRP
                    ? "10px"
                    : "0",
                border: isSRP ? "1px solid rgba(39, 38, 38, 0.2)" : "none",
              }}
              onClick={() => handleClick(item, index)}
            >
              {isMobile && isSRP ? (
                <div
                  style={{
                    backgroundImage: `url(${item.image_url})`,
                    backgroundSize: "contain",
                    backgroundPosition: "center",
                    backgroundRepeat: "no-repeat",
                    width: "86px",
                    height: "129px",
                    borderTopLeftRadius: "12px",
                    borderTopRightRadius: "12px",
                    pointerEvents: "none",
                  }}
                ></div>
              ) : (
                <div
                  className="img-container"
                  style={{
                    width: isLargeHeightRelative
                      ? "120px"
                      : isMobile && !isSRP
                      ? "100px"
                      : "160px",
                    height:
                      isSRP && !isMobile
                        ? "213px"
                        : isLargeHeightRelative
                        ? "115px"
                        : isMobile
                        ? "100px"
                        : "175px",
                    background: `url(${item.image_url}) no-repeat center center`,
                    backgroundSize: "cover",
                    pointerEvents: "none",
                    borderTopLeftRadius: isSRP ? "12px" : "0",
                    borderTopRightRadius: isSRP ? "12px" : "0",
                  }}
                />
              )}
              <p
                className="info-text"
                style={productTitle ? productTitleStyles : undefined}
              >
                {item.title}
              </p>
            </div>
          ))}
        </div>
        {isRightArrowVisible && similarProducts?.length ? (
          <div
            onClick={() => scrollByItem("right")}
            className={isSRP ? "right-arrow-srp" : "right-arrow"}
          >
            {isSRP ? (
              <ArrowIconNew />
            ) : (
              <ArrowIcon style={{ transform: "rotate(180deg)" }} />
            )}
          </div>
        ) : null}
      </div>
      {similarProducts?.length ? (
        <div className="dots-container">
          {similarProducts?.map((_, index) => (
            <div
              key={index}
              className={`dot ${activeIndex === index ? "active" : ""}`}
              onClick={() => scrollToProduct(index)}
            />
          ))}
        </div>
      ) : null}
    </div>
  );
};

export default UnavailableGallerySRP;
