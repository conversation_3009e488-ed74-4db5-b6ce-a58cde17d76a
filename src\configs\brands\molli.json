{"name": "<PERSON><PERSON>", "config": {"ux": {"gender": "female", "route": "question_only", "consentScreen": false, "fitFeedbacks": false}, "ui": {"1": {"input_fields": {"title": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textTransform": "lowercase", "textAlign": "left"}, "value": {"empty": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#737373", "textAlign": "left"}, "filled": {"backgroundColor": "#FFFFFF", "fontWeight": "700", "fontSize": "12px", "fontColor": "#000000", "textAlign": "left"}, "borderRadius": "0", "mobileBorderRadius": "0", "borderColor": "#000000", "marginBottom": "10px", "padding": "10px 0", "borderBottom": "1px solid #000000"}}, "genderCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "500", "textTransform": "lowercase"}, "focused": {"backgroundColor": "#000000", "fontColor": "#FFFFFF", "borderRadius": "0", "borderColor": "", "borderWidth": "", "fontSize": "12px", "fontWeight": "500", "textTransform": "lowercase"}}, "units": {"fontWeight": "600", "fontSize": "13px", "top": "10px", "right": "", "activeColor": "#000000", "inactiveColor": "#DDDDDD"}}, "2": {"routeCTA": {"borderColor": "#F0F0F0", "borderWidth": "1px", "borderRadius": "4px", "fontColor": "#000000"}}, "3": {"qrcode": {"backgroundColor": "#000000"}}, "7": {"generateResult": false, "recommendedSize": {"fontWeight": "700", "fontSize": "72px", "fontColor": "#000000", "textAlign": "center"}, "subtitles": {"fontWeight": "500", "fontSize": "14px", "fontColor": "#44883F", "textAlign": "center"}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#000000", "textTransform": "capitalize", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px"}, "focused": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "12px", "fontColor": "#44883F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#44883F", "borderWidth": "1px"}, "unavailable": {"backgroundColor": "", "fontWeight": "500", "fontSize": "12px", "fontColor": "#CEA13F", "textTransform": "capitalize", "borderRadius": "0px", "borderColor": "#CEA13F", "borderWidth": "1px"}}, "Description": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}, "similarProducts": {"title": {"fontWeight": "400", "fontSize": "14px", "fontColor": "#000000", "textAlign": "left"}}, "restartCTA": {"backgroundColor": "#FFFFFF", "fontWeight": "500", "fontSize": "14px", "fontColor": "#000000", "textTransform": "lowercase", "textDecoration": "underline", "borderRadius": "0", "borderColor": "", "borderWidth": ""}, "unavailable_size_text": {"fontWeight": "500", "fontSize": "72px", "fontColor": "#737373", "textAlign": "center", "textTransform": "capitalize"}}, "all": {"font": "Archivo", "titles": {"textAlign": "left", "fontWeight": "500", "textTransform": "lowercase", "color": "#000000", "fontSize": "16px"}, "subtitles": {"textAlign": "left", "fontWeight": "300", "textTransform": "lowercase", "color": "#000000", "fontSize": "14px"}, "cta": {"unfocused": {"backgroundColor": "#b8b8b8", "fontWeight": "500", "fontSize": "12px", "fontColor": "#FFFFFF", "textTransform": "lowercase", "borderRadius": "0", "borderColor": "#b8b8b8", "borderWidth": "1px", "borderTop": "none", "borderBottom": "none", "borderLeft": "none", "borderRight": "none"}, "focused": {"backgroundColor": "#000000", "fontWeight": "500", "fontColor": "#FFFFFF", "fontSize": "12px", "textTransform": "lowercase", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px"}}, "Politicy": {"fontSize": "12px", "fontColor": "#B9B9B9", "fontWeight": "regular", "textTransform": "capitalize"}}, "questions": {"scanCTA": {"borderWidth": "1px", "borderColor": "#000000", "borderRadius": "0", "fontSize": "12px", "fontWeight": "500", "fontColor": "#000000", "textTransform": "lowercase", "fontStyle": ""}}, "4/5/6": {"morphoCTA": {"unfocused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "2px", "borderRadius": "0", "fontWeight": "500", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "lowercase"}, "focused": {"backgroundColor": "#FFFFFF", "borderColor": "#000000", "borderWidth": "3px", "borderRadius": "0", "fontWeight": "500", "fontSize": "12px", "fontColor": "#000000", "textAlign": "center", "textTransform": "lowercase"}}, "stepper": {"stepNumber": "1", "stepTotal": "3", "focused": {"color": "#152F4E"}, "unfocused": {"color": "#D9D9D9"}}}, "6-2": {"sizeSystem": {"closed": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "500", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}, "open": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDDDD", "borderWidth": "1px", "borderRadius": "4px", "fontWeight": "500", "fontSize": "13px", "fontColor": "#000000", "textTransform": "uppercase", "textAlign": "left"}}, "sizeSelector": {"unfocused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "12px", "fontWeight": "500", "borderRadius": "0", "borderColor": "transparent", "borderWidth": "1px", "textTransform": "lowercase"}, "focused": {"backgroundColor": "#FFFFFF", "fontColor": "#000000", "fontSize": "12px", "fontWeight": "500", "borderRadius": "0", "borderColor": "#000000", "borderWidth": "1px", "textTransform": "lowercase"}}, "skip": {"fontColor": "#000000", "fontWeight": "400", "fontStyle": "underline", "fontSize": "12px"}}}}}